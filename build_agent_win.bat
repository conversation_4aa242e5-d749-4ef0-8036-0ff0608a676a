@echo off
REM Build MMR-Agent.exe with PyInstaller on Windows
REM Requires: Python, pyinstaller, arca-logo-sec.ico, version_info.txt

pyinstaller --onefile --noconsole ^
  --name "MMR-Agent" ^
  --icon "arca-logo-sec.ico" ^
  --version-file "version_info.txt" ^
  audio_agent.py

REM Copy config and privacy policy to dist
copy audio_agent_config.ini dist\
copy "ARCA Privacy Policy.pdf" dist\

echo Build complete. Distributables are in dist\
