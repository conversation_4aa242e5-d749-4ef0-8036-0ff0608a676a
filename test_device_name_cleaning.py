#!/usr/bin/env python3
"""
Test script to verify the clean_device_name function works correctly
"""

def clean_device_name(device_name):
    """Extract clean device name from system path, especially for Bluetooth devices."""
    if not device_name:
        return device_name

    # Handle Bluetooth device names with system paths
    # Example: "Headset (@System32\drivers\bthhfenum.sys,#2;%1 Hands-Free%0 ;(JBL WAVE BEAM))"
    # Extract the part in the last parentheses
    if "(" in device_name:
        # Find the last occurrence of parentheses
        last_paren_start = device_name.rfind("(")
        if last_paren_start >= 0:
            # Extract content from last parentheses
            remaining = device_name[last_paren_start + 1:]
            # Remove all closing parentheses from the end
            clean_name = remaining.rstrip(")")
            if clean_name and clean_name != device_name:  # Only use if different and not empty
                return clean_name

    # Handle other system path patterns
    # Remove common system path prefixes
    if "@System32" in device_name or "drivers\\" in device_name:
        # Try to extract device name after semicolon patterns
        parts = device_name.split(";")
        for part in reversed(parts):  # Check from end to beginning
            part = part.strip()
            if part and not part.startswith("@") and not part.startswith("%"):
                # Remove parentheses if they wrap the entire string
                if part.startswith("(") and part.endswith(")"):
                    part = part[1:-1]
                if part:
                    return part

    return device_name

def test_device_name_cleaning():
    """Test the clean_device_name function with various inputs"""
    
    test_cases = [
        # Your specific case
        ("Headset (@System32\\drivers\\bthhfenum.sys,#2;%1 Hands-Free%0 ;(JBL WAVE BEAM))", "JBL WAVE BEAM"),

        # Case that might be causing the extra parenthesis
        ("JBL WAVE BEAM)", "JBL WAVE BEAM"),
        ("(JBL WAVE BEAM)", "JBL WAVE BEAM"),
        ("(JBL WAVE BEAM))", "JBL WAVE BEAM"),

        # Other potential cases
        ("Regular Microphone", "Regular Microphone"),
        ("", ""),
        (None, None),
        ("Headset (@System32\\drivers\\bthhfenum.sys,#1;%1 Hands-Free%0 ;(Sony WH-1000XM4))", "Sony WH-1000XM4"),
        ("Microphone (@System32\\drivers\\usbaud.sys,#1;%1 USB Audio%0 ;(Blue Yeti))", "Blue Yeti"),
    ]
    
    print("Testing clean_device_name function:")
    print("=" * 60)
    
    for i, (input_name, expected) in enumerate(test_cases, 1):
        result = clean_device_name(input_name)
        status = "✓ PASS" if result == expected else "✗ FAIL"
        
        print(f"Test {i}: {status}")
        print(f"  Input:    {repr(input_name)}")
        print(f"  Expected: {repr(expected)}")
        print(f"  Got:      {repr(result)}")
        print()

if __name__ == "__main__":
    test_device_name_cleaning()
