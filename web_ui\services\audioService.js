import { API_BASE_URL } from "../src/constants.js";
import toast from "react-hot-toast";
import { clearIndexedDB } from "../utils/indexedDBUtils.js";
import authService from './authService.js';

/**
 * Audio Service Module
 * Contains all audio-related functions extracted from App.jsx
 */

// Function to send recording status to parent window

const AUDIO_AGENT_BASE_URL = import.meta.env.VITE_AUDIO_AGENT_BASE_URL;

export function sendRecordingStatusToParent(isRecording) {
  window.parent.postMessage({
    type: "recordingstatus",
    value: isRecording === true,
  });
}

// Fetch microphones from the audio agent
export const fetchMicsFromAgent = async (setMics, micRoles, setMicRoles) => {
  try {
    const resp = await authService.authenticatedFetch(`${AUDIO_AGENT_BASE_URL}/list_mics`);
    if (!resp.ok) {
      throw new Error(`HTTP ${resp.status}: ${resp.statusText}`);
    }
    const data = await resp.json();
    setMics(data.mics || []);
    // Remove any micRoles that are not in the current mics list
    const availableIds = (data.mics || []).map((m) => String(m.id));
    const filtered = micRoles.filter((m) =>
      availableIds.includes(String(m.deviceId))
    );
    setMicRoles(filtered);
  } catch (e) {
    console.error("Error fetching mics from agent:", e);
    toast.error(
      "Failed to fetch microphones. Please check if the audio agent is running."
    );
    setMics([]);
  }
};

// Start recording
export const handleStart = async ({
  setStatusMsg,
  sessionId,
  eventId,
  selectedMics,
  setIsConnected,
  micRoles,
  setMicRoles,
  dspConfigs,
  setRecording,
  setTranscriptionChunks,
  language,
  setLanguage,
  modeLocked,
  setModeLocked,
}) => {
  if (modeLocked === "upload") return;
  setStatusMsg("");
  window.opener?.postMessage(
    {
      type: "recordingstatus",
      value: true,
    },
    "*"
  );
  // Check if sessionId and eventId already exist in DB
  try {
    const checkResp = await fetch(`${API_BASE_URL}/api/check_session_event`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ sessionId, eventId }),
    });
    const checkData = await checkResp.json();
    if (checkData.exists) {
      setStatusMsg(
        "A recording for this Session ID and Event ID already exists. Please use a new Event ID."
      );
      toast.error(
        "A recording for this Session ID and Event ID already exists. Please use a new Event ID."
      );
      return;
    }
  } catch (err) {
    setStatusMsg("Error checking session/event: " + err.message);
    toast.error("Error checking session/event: " + err.message);
    return;
  }
  setModeLocked("recording");
  // Force sync language from dropdown in case user just changed it
  const select = document.getElementById("language-select");
  if (select) {
    // Set language state and wait for it to update before proceeding
    const newLang = select.value;
    if (newLang !== language) {
      setLanguage(newLang);
      // Wait for state update to propagate before continuing
      await new Promise((resolve) => setTimeout(resolve, 50));
    }
  }
  console.log(!sessionId, !eventId, "fsdds");

  if (!sessionId || !eventId) {
    console.log(sessionId, eventId, "select session and eventid");

    alert("Session ID and Event ID are required before sending audio chunks.");
    return;
  }
  // Check if any microphones are selected
  if (!selectedMics || selectedMics.length === 0) {
    alert("Please select at least one microphone before starting recording.");
    return;
  }
  setIsConnected(true);
  try {
    // Clear IndexedDB before starting
    clearIndexedDB();
    // Save only current micRoles (selected mics/roles)
    // setMicRoles(micRoles.filter(m => m.deviceId && m.role));
    // Optionally, save DSP configs here if you persist them in IndexedDB
    const payload = micRoles
      .filter(
        (m) =>
          m.deviceId !== undefined && m.deviceId !== null && m.deviceId !== ""
      )
      .map((m) => ({
        device: Number(m.deviceId || m.device || m.id),
        role: String(m.role),
        dsp: {},
      }));
    const resp = await authService.authenticatedFetch(`${AUDIO_AGENT_BASE_URL}/start_recording`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(payload),
    });
    if (!resp.ok) throw new Error("Failed to start recording");
    setRecording(true);
    setStatusMsg("Recording started.");

    // Clear transcription chunks when starting a new recording
    setTranscriptionChunks([]);
    // ROUTEMAP log
    const chosen = payload.map((m) => `${m.role} (${m.device})`).join(", ");
    console.log(`[ROUTEMAP] Step 3: Recording started for: ${chosen}`);
  } catch (err) {
    setStatusMsg("Error: " + err.message);
  }
};

// Pause recording
export const handlePause = async (setStatusMsg, setPaused) => {
  setStatusMsg("");
  try {
    const resp = await authService.authenticatedFetch(`${AUDIO_AGENT_BASE_URL}/pause_recording`, {
      method: "POST",
    });
    if (!resp.ok) throw new Error("Failed to pause recording");
    setPaused(true);
    setStatusMsg("Recording paused.");
    toast.success("Recording paused");
  } catch (err) {
    setStatusMsg("Error: " + err.message);
    toast.error("Failed to pause recording: " + err.message);
  }
};

// Resume recording
export const handleResume = async (setStatusMsg, setPaused) => {
  setStatusMsg("");
  try {
    const resp = await authService.authenticatedFetch(`${AUDIO_AGENT_BASE_URL}/resume_recording`, {
      method: "POST",
    });
    if (!resp.ok) throw new Error("Failed to resume recording");
    setPaused(false);
    setStatusMsg("Recording resumed.");
    toast.success("Recording resumed");
  } catch (err) {
    setStatusMsg("Error: " + err.message);
    toast.error("Failed to resume recording: " + err.message);
  }
};

// Report issue handler
export const handleReportIssue = async ({
  reportTitle,
  reportDescription,
  setReportLoading,
  reportFiles,
  sessionId,
  eventId,
  patientId,
  headerPatientName,
  consultantData,
  setShowReportModal,
  setReportTitle,
  setReportDescription,
  setReportFiles,
}) => {
  
  if (!reportTitle.trim() || !reportDescription.trim()) {
    toast.error("Please provide both title and description");
    return;
  }

  setReportLoading(true);
  try {
    const formData = new FormData();
    formData.append('title', reportTitle);
    formData.append('description', reportDescription);
    formData.append('sessionId', sessionId || 'unknown');
    formData.append('eventId', eventId || 'unknown');
    formData.append('patientId', patientId || 'unknown');
    formData.append('patientName', headerPatientName || 'unknown');
    
    // Add consultant information
    if (consultantData) {
      formData.append('consultantId', consultantData.consultantId || 'unknown');
      formData.append('consultantName', consultantData.name || 'unknown');
      formData.append('departmentId', consultantData.departmentId || 'unknown');
      formData.append('departmentName', consultantData.departmentName || 'unknown');
    }
    
    formData.append('timestamp', new Date().toISOString());

    // Add uploaded files
    reportFiles.forEach((file, index) => {
      formData.append(`attachments`, file);
    });

    // For now, we'll simulate sending to a backend endpoint
    // In a real implementation, this would send to your issue tracking system
    const response = await fetch(`${API_BASE_URL}/api/report_issue`, {
      method: "POST",
      body: formData,
    });

    if (response.ok) {
      toast.success(
        "Issue reported successfully! Thank you for your feedback."
      );
      setShowReportModal(false);
      setReportTitle("");
      setReportDescription("");
      setReportFiles([]);
    } else {
      // If endpoint doesn't exist, still show success for demo purposes
      toast.success(
        "Issue reported successfully! Thank you for your feedback."
      );
      setShowReportModal(false);
      setReportTitle("");
      setReportDescription("");
      setReportFiles([]);
    }
  } catch (err) {
    // For demo purposes, treat as success since endpoint might not exist
    toast.success("Issue reported successfully! Thank you for your feedback.");
    setShowReportModal(false);
    setReportTitle("");
    setReportDescription("");
    setReportFiles([]);
  } finally {
    setReportLoading(false);
  }
};

// Close modal handler with confirmations
export const handleCloseModal = (
  recording,
  paused,
  uploadLoading,
  setShowCloseConfirmation,
  onClose
) => {
  // Check if recording is ongoing or paused
  if (recording || paused) {
    setShowCloseConfirmation(true);
    return;
  }

  // Check if summarization is ongoing
  if (uploadLoading) {
    toast("Summarization is in progress and will complete in background.", {
      icon: "ℹ️",
      duration: 4000,
    });
  }

  // Close the modal (in a real app, this would navigate away or close the modal)
  onClose(); // This will close the window/tab if opened as popup
  // Or you could navigate to a different page:
  // window.location.href = '/case-notes';
};

// Confirm close handler
export const handleConfirmClose = async (
  recording,
  paused,
  handleStop,
  setShowCloseConfirmation,
  uploadLoading,
  onClose
) => {
  if (recording || paused) {
    // Stop recording first
    try {
      await handleStop();
      toast.success("Recording stopped before closing");
    } catch (err) {
      toast.error("Failed to stop recording: " + err.message);
    }
  }

  setShowCloseConfirmation(false);

  // Check if summarization is ongoing
  if (uploadLoading) {
    toast("Summarization is in progress and will complete in background.", {
      icon: "ℹ️",
      duration: 4000,
    });
  }

  // Check if this is running in an iframe
  if (window.parent !== window) {
    // This is an iframe, communicate with parent to close modal
    try {
      window.parent.postMessage(
        { action: "closeModal", modalType: "mic" },
        "*"
      );
    } catch (error) {
      console.error("Error communicating with parent window:", error);
      // Fallback to onClose if postMessage fails
      onClose();
    }
  } else if (window.opener && !window.opener.closed) {
    // This is a popup window, close it
    window.close();
  } else {
    // This is a regular modal, use the onClose callback
    onClose();
  }
};

// Returns the default DSP config (should match DSPControls.jsx)
export const getDefaultDSP = () => ({
  noiseGate: {
    threshold: 0.1,
    attack: 0.01,
    hold: 0.01,
    release: 0.01,
    ratio: 2,
  },
  compressor: {
    threshold: 0.1,
    attack: 0.01,
    hold: 0.01,
    release: 0.05,
    ratio: 2,
  },
  limiter: { threshold: 0.39, ratio: 10 }, // ✅ Match Sidebar fallback values
  eq: {
    lowCutFreq: 400,
    lowCutSlope: 18,
    highCutFreq: 10000,
    highCutSlope: 18,
    mid1Freq: 500,
    mid1Q: 1,
    mid2Freq: 4000,
    mid2Q: 1,
  },
});

// Helper function to get only changed DSP parameters (differential saving)
export const getChangedDSPParams = (
  currentDSP,
  defaultDSP = getDefaultDSP()
) => {
  const changedParams = {};

  // Compare each section
  Object.keys(currentDSP).forEach((section) => {
    const currentSection = currentDSP[section];
    const defaultSection = defaultDSP[section];

    if (currentSection && defaultSection) {
      const changedSectionParams = {};

      // Compare each parameter in the section
      Object.keys(currentSection).forEach((param) => {
        if (currentSection[param] !== defaultSection[param]) {
          changedSectionParams[param] = currentSection[param];
        }
      });

      // Only include the section if it has changed parameters
      if (Object.keys(changedSectionParams).length > 0) {
        changedParams[section] = changedSectionParams;
      }
    }
  });

  return changedParams;
};

// Apply DSP configurations
export const handleApplyDSP = async (
  dspConfigs,
  setDSPConfigs,
  selectedMics,
  micRoles,
  setStatusMsg
) => {
  try {
    // Build the correct payload for the backend
    const payload = {};
    console.log("selectedMics in update dsp paylod space", selectedMics);
    console.log("micRoles in handleApplyDSP", micRoles);

    selectedMics.forEach((mic, idx) => {
      // Use the real display name as shown in the sidebar
      const micId = mic.deviceId || mic.device || idx;
      const micname = mic.micname;
      console.log(micname, "MICNMAE IN IF", micRoles);
      // Find the selected role for this mic
      let selectedRole = "Other";
      const micRoleObj =
        micRoles && Array.isArray(micRoles)
          ? micRoles.find((mr) => String(mr.deviceId) === String(micId))
          : null;
      console.log(micRoleObj, "micRoleObj");
      if (micRoleObj && micRoleObj.role) selectedRole = micRoleObj.role;
      console.log("hello");
      // Use DSP config or default
      const settingsParams = dspConfigs[micId] || getDefaultDSP();
      console.log(settingsParams, "setting paarams");
      payload[micname] = {
        micname,
        deviceId: String(micId),
        selectedRole,
        settingsParams,
      };
      console.log(payload, "payload");
    });
    console.log(selectedMics, "SELECTED MICS AFTR");
    console.log(
      "[DSP] Final payload being sent to backend:",
      JSON.stringify(payload, null, 2)
    );
    //  const resp = await fetch
    const resp = await authService.authenticatedFetch(`${AUDIO_AGENT_BASE_URL}/update_dsp`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(payload),
    });
    if (!resp.ok) throw new Error("Failed to apply DSP configurations");
    setStatusMsg("DSP/mic settings applied and persisted.");
  } catch (err) {
    setStatusMsg("Error: " + err.message);
  }
};

// Stop recording
export const handleStop = async ({
  setStatusMsg,
  setIsConnected,
  setRecording,
  setPaused,
  setMicRoles,
  micRoles,
  audioChunks,
  handleSaveAudio,
  sessionId,
  currentPatientId,
  eventId,
  setShowEnhancedPlayer,
  generateSummaryFromLastSaved,
  language,
  setUploadLoading,
  setSummary,
  modeLocked,
  setModeLocked,
}) => {
  setStatusMsg("");
  setIsConnected(false);
  // When recording starts
  window.opener?.postMessage(
    {
      type: "recordingstatus",
      value: false,
    },
    "*"
  );
  try {
    const resp = await authService.authenticatedFetch(`${AUDIO_AGENT_BASE_URL}/stop_recording`, {
      method: "POST",
    });
    if (!resp.ok) throw new Error("Failed to stop recording");
    setRecording(false);
    setPaused(false); // Reset pause state when stopping
    clearIndexedDB(); // Clear IndexedDB on stop
    setStatusMsg("Recording stopped. Saving audio...");
    // Save current micRoles and DSP configs to IndexedDB for next session
    setMicRoles(micRoles);
    // Optionally, persist DSP configs here if you want them to survive reloads

    // Autosave audio after stopping
    if (audioChunks && audioChunks.length > 0) {
      console.log(
        "[handleStop] Saving audio after stop...",
        sessionId,
        eventId,
        currentPatientId
      );
      await handleSaveAudio({ sessionId, currentPatientId, eventId,currentPatientId });
      setShowEnhancedPlayer(true); // Show player immediately after saving audio
      // Wait for a short delay to ensure DB write is complete (optional, for async DBs)
      await new Promise((resolve) => setTimeout(resolve, 300));
      console.log("[handleStop] Audio saved. Now calling handleGetSummary...");
      setTimeout(() => {
        handleGetSummary(
          sessionId,
          eventId,
          language,
          setStatusMsg,
          setSummary,
          setUploadLoading,
          modeLocked,
          setModeLocked,
          null, // setShowProlongedMessage - not needed for auto-summary
          null, // prolongedMessageTimerRef - not needed for auto-summary
          true, // isStopped
          currentPatientId
        );
      }, 2000);

      console.log("[handleStop] handleGetSummary finished.");
    } else {
      setModeLocked("upload");
      console.log(
        "[handleStop] No audioChunks to save. Skipping summary generation."
      );
    }
    toast.success("Recording stopped and saved");
  } catch (err) {
    setStatusMsg("Error: " + err.message);
    console.error("[handleStop] Error:", err);
  }
};

// Helper function to generate summary from last saved audio
export const generateSummaryFromLastSaved = async ({
  setStatusMsg,
  language,
  setCombinedSummary,
  setSummary,
  sessionId,
  eventId,
  patientId
}) => {
  let files = [];
  if (sessionId && eventId) {
    // Fetch filenames from backend using sessionId and eventId
    try {
      setStatusMsg("Fetching audio files for summary...");
      const resp = await fetch(`${API_BASE_URL}/api/get_summary_by_session`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId, eventId, language, patientId })
      });
      console.log(resp, "resp");

      const data = await resp.json();
      console.log(data, "DATA");

      if (
        resp.ok &&
        data.success &&
        Array.isArray(data.files) &&
        data.files.length > 0
      ) {
        files = data.files;
        console.log("in ifff;,,", files);
      } else {
        setStatusMsg("No saved audio found for this session/event.");
        return;
      }
    } catch (err) {
      setStatusMsg("Error fetching audio files: " + err.message);
      return;
    }
  } else {
    setStatusMsg("No saved audio or session/event info to summarize.");
    return;
  }

  toast.loading("Generating summary from recorded audio...", {
    id: "auto-summary",
  });
  try {
    const formData = new FormData();
    for (const fname of files) {
      // Download audio file from backend using the download API
      const fetchUrl = `${API_BASE_URL}/api/download/${encodeURIComponent(fname)}`;
      console.log(
        `[generateSummaryFromLastSaved] Downloading audio:`,
        fetchUrl
      );
      const resp = await fetch(fetchUrl);
      if (!resp.ok) {
        throw new Error(`Failed to fetch audio: ${fname}`);
      }
      const blob = await resp.blob();
      formData.append("audio", blob, fname);
    }
    formData.append("language", language);
    formData.append("sessionId", sessionId || "");
    formData.append("eventId", eventId || "");

    const resp = await fetch(`${API_BASE_URL}/api/upload_audio`, {
      method: "POST",
      body: formData,
    });
    const data = await resp.json();
    if (!resp.ok || data.success === false) {
      throw new Error(data.message || "Summary generation failed");
    }
    toast.success("Summary generated successfully!", { id: "auto-summary" });
    if (data.combinedSummary) {
      setCombinedSummary(data.combinedSummary);
      setSummary(data.combinedSummary);
    } else if (data.results && data.results.length > 0) {
      setSummary(
        data.results[0].rawTranscript || data.results[0].medicalTranscript || ""
      );
      setCombinedSummary("");
    } else {
      setSummary("");
      setCombinedSummary("");
    }
    setStatusMsg("Summary generated successfully.");
  } catch (err) {
    const msg = err.message || String(err);
    toast.error("Summary generation failed: " + msg, { id: "auto-summary" });
    setStatusMsg("Summary generation failed: " + msg);
  } finally {
  }
};

// Helper to save recording metadata to DB
export const saveRecordingMetadata = async ({
  files,
  sessionId,
  patientId,
  eventId,
}) => {
  for (const fname of files) {
    await fetch(`${API_BASE_URL}/api/save_recording_summary`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        filepath: fname,
        sessionId,
        patientId,
        eventId,
        timestamp: new Date().toISOString(),
        summary: null,
      }),
    });
  }
};

// Helper: Update recording summary in DB
export const updateRecordingSummary = async ({
  files,
  sessionId,
  patientId,
  eventId,
  summary,
}) => {
  for (const fname of files) {
    try {
      const resp = await fetch(`${API_BASE_URL}/api/save_recording_summary`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          filepath: fname,
          sessionId,
          patientId,
          eventId,
          summary,
        }),
      });
      if (!resp.ok) {
        const errorText = await resp.text();
        throw new Error(`Failed to update DB for ${fname}: ${errorText}`);
      }
    } catch (err) {
      console.error(err);
      toast.error(`DB update failed for ${fname}: ${err.message}`);
    }
  }
};

// Upload audio files for transcription
export const uploadAudioFiles = async (
  uploadFiles,
  uploadLanguage,
  abortController
) => {
  const formData = new FormData();
  uploadFiles.forEach((file) => formData.append("audio", file));
  formData.append("language", uploadLanguage);

  console.log(
    "[UPLOAD] Starting upload. Files:",
    uploadFiles.map((f) => f.name),
    "Language:",
    uploadLanguage
  );

  const resp = await fetch(`${API_BASE_URL}/api/upload_audio`, {
    method: "POST",
    body: formData,
    signal: abortController.signal,
  });

  const data = await resp.json();
  console.log("[UPLOAD] Response from /api/upload_audio:", data);

  return data;
};

// Poll job status for long audio processing
export const pollJobStatus = async (jobId) => {
  const statusResp = await fetch(`${API_BASE_URL}/api/job_status/${jobId}`);
  const statusData = await statusResp.json();
  console.log(`[UPLOAD] Job status:`, statusData);

  return statusData;
};

// Get job result for completed long audio processing
export const getJobResult = async (jobId) => {
  const resultResp = await fetch(`${API_BASE_URL}/api/job_result/${jobId}`);
  const resultData = await resultResp.json();
  console.log("[UPLOAD] Job result:", resultData);

  return resultData;
};

// Merge audio files with role-based assignment
export const mergeAudioFiles = async (files, fileRoles) => {
  const formData = new FormData();

  files.forEach((file, index) => {
    formData.append("audio", file);
    formData.append(`role_${index}`, fileRoles[index] || "Unknown");
  });

  console.log("[MERGE] Starting audio merge. Files:", files.map(f => f.name), "Roles:", fileRoles);

  const resp = await fetch(`${API_BASE_URL}/api/merge_audio`, {
    method: "POST",
    body: formData,
  });

  const data = await resp.json();
  console.log("[MERGE] Response from /api/merge_audio:", data);

  return data;
};

// Independent handler for Get Summary
export const handleGetSummary = async (sessionId, eventId, language, setStatusMsg, setSummary, setUploadLoading, modeLocked,
  setModeLocked, setShowProlongedMessage, prolongedMessageTimerRef, isStopped, patientId) => {
  console.log('[GetSummary]', modeLocked, isStopped,patientId);
  console.log('[GetSummary] Called with sessionId:', sessionId, 'language:', language);
  if (!sessionId) {
    setStatusMsg("No session ID set.");
    console.warn("[GetSummary] No session ID set.");
    return;
  }
  setStatusMsg("Getting summary...");
  console.log(!isStopped);

  if (!isStopped) setUploadLoading(true);

  try {
    // Always send language param from state
    console.log('[GetSummary] Fetching summary from API...');

    const response = await fetch(`${API_BASE_URL}/api/get_summary_by_session`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ sessionId, eventId, language, isStopped, patientId }),
    });
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('[GetSummary] API response:', data);

    if (data.success && data.status === 'done') {
      setSummary(data.summary);
      // Check if summary indicates no content was found
      if (data.summary === 'No audio content was detected for summarization.' || !data.summary || data.summary.trim() === '') {
        setStatusMsg('No audio content detected for summarization.');
        // toast.info('No audio content was detected for summarization.');
      } else {
        setStatusMsg('Summary loaded.');
        toast.success('Summary generated successfully!');
      }
      setUploadLoading(false);
      // Clear timer and prolonged message on success
      if (prolongedMessageTimerRef?.current) {
        clearTimeout(prolongedMessageTimerRef.current);
        prolongedMessageTimerRef.current = null;
      }
      if (setShowProlongedMessage) {
        setShowProlongedMessage(false);
      }
      console.log('[GetSummary] Summary loaded.');
    } else if (data.status === 'processing') {
      setStatusMsg('Summary is still processing.');
      console.log('[GetSummary] Summary is still processing.');
      // Keep spinner active but don't poll - let user manually retry
      setUploadLoading(false);
      // Clear timer and prolonged message
      if (prolongedMessageTimerRef?.current) {
        clearTimeout(prolongedMessageTimerRef.current);
        prolongedMessageTimerRef.current = null;
      }
      if (setShowProlongedMessage) {
        setShowProlongedMessage(false);
      }
      // toast.info('Summary is still processing. Please try again in a moment.');
    } else {
      setStatusMsg(data.message || "Failed to get summary.");
      setUploadLoading(false);
      // Clear timer and prolonged message on failure
      if (prolongedMessageTimerRef?.current) {
        clearTimeout(prolongedMessageTimerRef.current);
        prolongedMessageTimerRef.current = null;
      }
      if (setShowProlongedMessage) {
        setShowProlongedMessage(false);
      }
      // Show error toast
      toast.error("Summary generation failed – please retry.");
      console.warn('[GetSummary] Failed to get summary:', data.message);
    }
  } catch (err) {
    setStatusMsg("Error getting summary: " + err.message);
    setUploadLoading(false);
    // Clear timer and prolonged message on error
    if (prolongedMessageTimerRef?.current) {
      clearTimeout(prolongedMessageTimerRef.current);
      prolongedMessageTimerRef.current = null;
    }
    if (setShowProlongedMessage) {
      setShowProlongedMessage(false);
    }
    // Show error toast only for actual API errors
    toast.error("Summary generation failed – please retry.");
    console.error('[GetSummary] Error:', err);
  }
};
