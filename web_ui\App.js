import React, { useState } from 'react';
import ConversationPanel from './components/ConversationPanel';
import AIFeedbackPanel from './components/AIFeedbackPanel';
import FeedbackInput from './components/FeedbackInput';
import './App.css';

function App() {
  const [language, setLanguage] = useState('Malayalam');
  const [recording, setRecording] = useState(false);
  const [summary, setSummary] = useState('');

  return (
    <div className="main-bg">
      <header className="header">
        <div className="header-session">
          <span className="session-id">160099992 - Mrs. <PERSON>asivan</span>
          <span className="session-id">Session Id : 1000000112 | Event Id : 1745575862</span>
          <select className="lang-select" value={language} onChange={e => setLanguage(e.target.value)}>
            <option value="Malayalam">Malayalam</option>
            <option value="English">English</option>
          </select>
          <button className={recording ? 'btn-stop' : 'btn-start'} onClick={() => setRecording(!recording)}>{recording ? 'Stop' : 'Start'}</button>
          <button className="btn-summary">Get Summary</button>
          <button className="btn-close">Close</button>
        </div>
      </header>
      <div className="content-row">
        <ConversationPanel />
        <AIFeedbackPanel summary={summary} setSummary={setSummary} />
      </div>
      <FeedbackInput />
      <footer className="footer-note">
        Note: This plugin (ARCA AI) is in testing. Responses may be inaccurate — please review for correctness and context
      </footer>
    </div>
  );
}

export default App;
