<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Clinical App Control Panel</title>
  <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
  <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
 
</head>
<body>
  <div class="container">
    <div class="button-container">
      <button class="button mic-button" onclick="openClinicalTab('recording')">
        <svg class="button-icon" viewBox="0 0 24 24">
          <path d="M12 2c1.1 0 2 .9 2 2v6c0 1.1-.9 2-2 2s-2-.9-2-2V4c0-1.1.9-2 2-2zm5.3 6c.4 0 .******* 0 3.25-2.25 5.97-5.3 6.7v2.05h3c.55 0 1 .45 1 1s-.45 1-1 1h-8c-.55 0-1-.45-1-1s.45-1 1-1h3v-2.05C7.45 14.67 5.2 11.95 5.2 8.7c0-.4.3-.7.7-.7s.*******c0 2.8 2.2 5 5 5s5-2.2 5-5c0-.4.3-.7.7-.7z"/>
        </svg>
        <span class="button-label">Microphone</span>
      </button>

      <button class="button ai-button" onclick="openClinicalTab('view')">
        <svg class="button-icon" viewBox="0 0 24 24">
          <path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7.01 5 5 7.01 5 9.5S7.01 14 9.5 14 14 11.99 14 9.5 11.99 5 9.5 5Z"/>
        </svg>
        <span class="button-label">View AI Data</span>
      </button>
    </div>
  </div>

  <script>
    let openTabs = {
      recording: null,
      view: null
    };
  
    const sessionData = {
      sessionId: '**********',
      eventId: '**********',
      patientId: '220525099',
      patientName: 'Mrs. Nirmala Sadasivan',
    };
  
    function openClinicalTab(tabType) {
      try {
        const currentTab = openTabs[tabType];
  
        // If the tab is already open, focus on it
        if (currentTab && !currentTab.closed) {
          try {
            currentTab.focus();
          } catch (e) {
            alert('Please close the existing tab before opening a new one.', 'error');
          }
          return;
        }
  
        const params = new URLSearchParams({
          ...sessionData,
          openTab: tabType
        });
  
        const url = `http://localhost:5173/?${params.toString()}`;
        const newTab = window.open(url, '_blank');
  
        if (newTab) {
          openTabs[tabType] = newTab;
  
          // Start polling to detect when the tab is closed
          const checkTabClosed = setInterval(() => {
            if (openTabs[tabType]?.closed) {
              openTabs[tabType] = null;
              clearInterval(checkTabClosed);
            }
          }, 1000);
        } else {
          alert('Popup blocked. Please allow popups for this site and try again.', 'error');
        }
  
      } catch (error) {
        console.error('Error opening tab:', error);
        alert('Error opening tab: ' + error.message, 'error');
      }
    }
  </script>
  

  <hr />
</body>
</html>
