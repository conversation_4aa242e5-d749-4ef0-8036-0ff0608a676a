# ALaaS Modal Functional Specification

## 🔤 Language Selection Dropdown

- **Dropdown must be clearly visible** within the ALaaS modal.
- Populated with:
  - Common local languages (based on deployment region).
  - Must explicitly include **English**.
- **"Malayalam"** should be the **default selected** language.
- Selected language will:
  - Instruct STT engine on the expected audio language.
  - Be reflected in the **live transcript pane**.

---

## 🎛️ Modal Icons and Functionalities

### ▶️ Start

- **Enabled by default** when no recording exists for current session and patient.
- Starts audio capture using:
  - All admin-selected microphones.
  - Admin-defined DSP settings.
- On successful start:
  - Disable **Start**.
  - Enable **Pause** and **Stop**.
  - Provide **clear visual feedback** indicating recording is active.

### ⏹️ Stop

- **Enabled only** during active recording.
- Stops audio capture immediately and saves to **DB** with session and patient link.
- On click:
  - Disable **Start** and **Pause**.
  - Enable **Get Summary** icon.
  - Show confirmation that recording has stopped.

### ⏸️ Pause

- Enabled only during active recording.
- Temporarily **halts** audio capture without ending session.
- On pause:
  - Disable **Pause**.
  - Enable **Start** and **Stop**.
  - Indicate paused state clearly.

### 📋 Get Summary

- **Enabled only after Stop** and successful save to DB.
- On click:
  - Starts summarization.
  - Show spinner/loading indicator.
  - Display summary upon completion.

### 🎧 Playback Recorded Audio

- Enabled only when:
  - A full DB-saved recording OR a partial cached recording exists.
  - **Start icon is NOT active** (i.e., no recording ongoing).
- Should:
  - Allow **playback**.
  - Be integrated with **real-time audio visualization**.
  - Include **playback slider** for navigation.

---

## 🐞 Report Issue Modal

- Opens modal with:
  - `Title` (short input)
  - `Description` (detailed input)
- Allow **media uploads**:
  - screenshot, GIF, video, txt, doc, docx, csv, xlsx, pdf
- Include buttons:
  - **Report** → Sends data + metadata to developers (email or backend).
  - **Cancel** → Closes modal without action.
- Show **confirmation** on success.

---

## 🎙️ Upload Audio(s)

- **Visible only when** `role=admin` is present in URL.
- On click:
  - Show modal with:
    - Language selector (expected spoken language).
    - File picker for `.wav` files (**multi-file allowed**).
  - On upload:
    - Trigger summarization flow.
    - Show UI indicator for upload/progress.

---

## ⚙️ Admin-only Features

- **Visible only for `role=admin` in URL**:
  - Microphone selection pane
  - DSP configuration panel
  - Live transcript pane
  - Upload Audio(s) button
- Only admins can:
  - Control mic settings
  - See real-time transcripts
  - Upload external recordings

---

## 📝 Encounter Type Checkbox - "New-Visit"

- Checkbox labeled `"New-Visit"`:
  - Default = **unchecked** → indicates **follow-up visit**.
  - Checked = **new patient visit**.
- Summary template applied accordingly:
  - New patient → new visit template
  - Follow-up → follow-up template

---

## ❌ Close

- **Closes the ALaaS modal** and returns to Case Notes page.
- If **recording is ongoing or paused**:
  - Show confirmation: “This will discard unsaved recordings.”
- If **summarization is ongoing**:
  - Show toast: “Summarization is in progress and will complete in background.”

---

✅ **Acceptance Criteria Checklist**:
- [ ] Language dropdown shown and defaults to Malayalam
- [ ] Start/Stop/Pause/Get Summary/Playback work as expected
- [ ] Admin-only views controlled via URL param
- [ ] Upload and Issue reporting modal functional
- [ ] "New-Visit" checkbox changes summary template
- [ ] Confirmation and toast notifications are shown as per state

