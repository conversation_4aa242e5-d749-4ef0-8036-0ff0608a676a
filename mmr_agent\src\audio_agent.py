"""
Production-ready Audio Agent for Multi-mic Recording and Streaming
"""

import sounddevice as sd
import soundfile as sf
import threading
import queue
import time
import os
import sys
import signal
import socket
import json
from pedalboard import Pedalboard, HighpassFilter, LowpassFilter, NoiseGate, Compressor, Limiter

# Add parent directory to Python path to allow absolute imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))
from fastapi import FastAPI, Request, WebSocket, WebSocketDisconnect, Response, Depends, HTTPException
from fastapi.responses import FileResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel
import uvicorn
from fastapi.middleware.cors import CORSMiddleware
import logging
from contextlib import asynccontextmanager
import json
import asyncio
import numpy as np
import io
import configparser
from prometheus_client import start_http_server, generate_latest, CONTENT_TYPE_LATEST
from opentelemetry import trace
from fastapi.security import OAuth2PasswordRequestForm
from typing import Dict
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from fastapi import Request, Security
from slowapi.errors import RateLimitExceeded
from fastapi.responses import JSONResponse
from fastapi.requests import Request
from fastapi.exceptions import RequestValidationError
from fastapi.exceptions import RequestValidationError as FastAPIRequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from websockets.exceptions import ConnectionClosedError

from mmr_agent.src.configuration_manager import ConfigurationManager
from mmr_agent.src.metrics_manager import MetricsManager
from mmr_agent.src.opentelemetry_manager import OpenTelemetryManager
from mmr_agent.src.dsp_processor import DSPProcessor
from mmr_agent.src.agent_types import MicRole, StartRecordingRequest, DSPConfig
from mmr_agent.src.auth import authenticate_user, create_access_token, get_current_user, oauth2_scheme, fake_users_db

# Helper functions for safe config parsing

def safe_str(val, default):
    return str(val) if val is not None else default

def safe_int(val, default):
    try:
        return int(val)
    except (TypeError, ValueError):
        return default

def safe_bool(val, default):
    if isinstance(val, bool):
        return val
    if isinstance(val, str):
        return val.lower() in ("true", "1", "yes")
    return default

# --- Initialize Managers ---
config_manager = ConfigurationManager()
metrics_manager = MetricsManager()
dsp_processor = DSPProcessor()

# --- Configurable Settings ---
ENVIRONMENT = os.environ.get('AUDIO_AGENT_ENV') or safe_str(config_manager.get('environment', 'env', fallback='development'), 'development')
ALLOWED_ORIGIN = safe_str(config_manager.get('server', 'allowed_origin', fallback='http://localhost:5173'), 'http://localhost:5173')
if ENVIRONMENT == 'production':
    if not ALLOWED_ORIGIN or 'localhost' in ALLOWED_ORIGIN:
        raise RuntimeError('In production, you must set a secure allowed_origin in audio_agent_config.ini')
ALLOWED_ORIGINS = [origin.strip() for origin in ALLOWED_ORIGIN.split(',')]
PORT = safe_int(config_manager.get('server', 'port', fallback=5001), 5001)  
HOST = safe_str(config_manager.get('server', 'host', fallback='0.0.0.0'), '0.0.0.0')
ENABLE_OPENTELEMETRY = safe_bool(config_manager.get('server', 'enable_opentelemetry', fallback=False), False)
DRIVER_NAME = config_manager.get('server', 'driver_name', fallback=None)

# --- Utility function for path resolution ---
def get_application_path():
    """Get the directory where the application is running from."""
    if getattr(sys, 'frozen', False):
        # Running as PyInstaller executable
        return os.path.dirname(sys.executable)
    else:
        # Running as Python script
        return os.path.dirname(os.path.dirname(__file__))

# Logging config
LOG_LEVEL = safe_str(config_manager.get('logging', 'log_level', fallback='INFO'), 'INFO')
LOG_FILE = safe_str(config_manager.get('logging', 'log_file', fallback='audio_agent.log'), 'audio_agent.log')
LOG_FILEMODE = safe_str(config_manager.get('logging', 'log_filemode', fallback='a'), 'a')

# Update log file path
if not os.path.isabs(LOG_FILE):
    LOG_FILE = os.path.join(get_application_path(), LOG_FILE)

logging.basicConfig(
    level=getattr(logging, LOG_LEVEL.upper(), logging.INFO),
    format='%(asctime)s %(levelname)s %(message)s',
    filename=LOG_FILE,
    filemode=LOG_FILEMODE
)
logger = logging.getLogger("audio_agent")
logger.addHandler(logging.StreamHandler(sys.stdout))
logger.info('Audio agent starting up...')
logger.info(f'Process ID: {os.getpid()}')

# API metadata
API_TITLE = safe_str(config_manager.get('api', 'title', fallback='Multi-mic Audio Agent API'), 'Multi-mic Audio Agent API')
API_DESCRIPTION = safe_str(config_manager.get('api', 'description', fallback='Production-ready backend for multi-mic audio streaming and DSP.'), 'Production-ready backend for multi-mic audio streaming and DSP.')
API_VERSION = safe_str(config_manager.get('api', 'version', fallback='1.0.0'), '1.0.0')

# Rate limiting config
RATE_LIMIT_TOKEN = safe_str(config_manager.get('security', 'rate_limit_token', fallback='5/minute'), '5/minute')

# --- FastAPI App Setup ---
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    yield
    # Shutdown
    logger.info("FastAPI shutdown event: stopping all recordings and cleaning up.")
    shutdown_event.set()
    stop_recording()
    logger.info("Shutdown complete.")

app = FastAPI(
    title=API_TITLE,
    description=API_DESCRIPTION,
    version=API_VERSION,
    lifespan=lifespan
)

# --- CORS Middleware Setup ---
app.add_middleware(
    CORSMiddleware,
    allow_origins=ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# --- OpenTelemetry Setup ---
otel_manager = OpenTelemetryManager(app, ENABLE_OPENTELEMETRY)

# --- Static Files Setup ---
# Mount the parent directory to serve dspsettings.json
parent_dir = os.path.dirname(os.path.dirname(__file__))
if os.path.exists(parent_dir):
    app.mount("/static", StaticFiles(directory=parent_dir), name="static")

# Security headers middleware
@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    response = await call_next(request)
    
    # Skip security headers for Swagger UI endpoints to allow proper rendering
    if request.url.path.startswith("/docs") or request.url.path.startswith("/openapi.json") or request.url.path.startswith("/redoc"):
        return response
    
    # Enhanced security headers
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY" 
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains; preload"
    response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
    response.headers["Permissions-Policy"] = "geolocation=(), microphone=(), camera=()"
    
    # Enhanced CSP
    csp_default = safe_str(config_manager.get('security', 'csp_default_src', fallback="'self'"), "'self'")
    csp_script = safe_str(config_manager.get('security', 'csp_script_src', fallback="'self'"), "'self'")
    csp_style = safe_str(config_manager.get('security', 'csp_style_src', fallback="'self'"), "'self'")
    
    csp = f"default-src {csp_default}; script-src {csp_script}; style-src {csp_style}; object-src 'none'; base-uri 'self'; form-action 'self'"
    response.headers["Content-Security-Policy"] = csp
    
    # Remove server version disclosure
    if "server" in response.headers:
        del response.headers["server"]
    
    return response

# Input sanitization middleware
@app.middleware("http") 
async def sanitize_input_middleware(request: Request, call_next):
    """Sanitize input to prevent various injection attacks."""
    # Check for suspicious patterns in query parameters
    for key, value in request.query_params.items():
        if any(pattern in str(value).lower() for pattern in 
               ['<script', 'javascript:', 'data:', 'vbscript:', '../', '..\\', 'file://', 'ftp://']):
            logger.warning(f"Suspicious input detected in query param {key}: {value}")
            return JSONResponse(
                status_code=400,
                content={"error": "Invalid input detected"}
            )
    
    # Check request path for path traversal
    if any(pattern in str(request.url.path) for pattern in ['../', '..\\', '%2e%2e']):
        logger.warning(f"Path traversal attempt detected: {request.url.path}")
        return JSONResponse(
            status_code=400, 
            content={"error": "Invalid path"}
        )
    
    return await call_next(request)

# --- Prometheus Endpoint ---
@app.get("/metrics")
def metrics():
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)

# --- Global State ---
recording_threads = {}
recording_queues = {}
stream_queues = {}
active_dsp_configs = {}
pause_events = {}  # For pause/resume functionality
CHUNK_SECONDS = 1
shutdown_event = threading.Event()

# --- Persistence for mic settings ---
DSP_SETTINGS_FILE = os.path.join(get_application_path(), 'dspsettings.json')
PERSISTED_DSP_SETTINGS = {}

def load_dsp_settings():
    """Load persisted DSP settings from file."""
    global PERSISTED_DSP_SETTINGS
    try:
        logger.info(f"Attempting to load DSP settings from: {DSP_SETTINGS_FILE}")
        if os.path.exists(DSP_SETTINGS_FILE):
            with open(DSP_SETTINGS_FILE, 'r') as f:
                PERSISTED_DSP_SETTINGS = json.load(f)
            logger.info(f"Successfully loaded DSP settings from {DSP_SETTINGS_FILE}: {PERSISTED_DSP_SETTINGS}")
        else:
            # Create empty settings file if it doesn't exist
            with open(DSP_SETTINGS_FILE, 'w') as f:
                json.dump({}, f)
            logger.info(f"Created empty DSP settings file at {DSP_SETTINGS_FILE}")
            PERSISTED_DSP_SETTINGS = {}
    except Exception as e:
        logger.error(f"Error loading DSP settings: {e}")
        PERSISTED_DSP_SETTINGS = {}

def save_dsp_settings():
    """Save current DSP settings to file."""
    try:
        logger.info(f"Saving DSP settings to {DSP_SETTINGS_FILE}: {PERSISTED_DSP_SETTINGS}")
        with open(DSP_SETTINGS_FILE, 'w') as f:
            json.dump(PERSISTED_DSP_SETTINGS, f, indent=2)
        logger.info(f"Successfully saved DSP settings to {DSP_SETTINGS_FILE}")
    except Exception as e:
        logger.error(f"Error saving DSP settings: {e}")

# Load settings on startup
load_dsp_settings()

# --- DSP Processing ---
def apply_dsp(frames, dsp, samplerate, chunk_frames):
    """Apply DSP (EQ, noise gate, compressor, limiter) to audio frames using Pedalboard."""
    try:
        if not dsp:
            return frames

        board = Pedalboard()

        # EQ (High-pass and Low-pass filters)
        eq = dsp.get('eq', {})
        low_cut = eq.get('lowCutFreq', 0)
        if low_cut > 0:
            board.append(HighpassFilter(cutoff_frequency_hz=low_cut))
        high_cut = eq.get('highCutFreq', 0)
        if high_cut > 0:
            board.append(LowpassFilter(cutoff_frequency_hz=high_cut))

        # Noise Gate
        ng = dsp.get('noiseGate', {})
        ng_thresh = ng.get('threshold', 0)
        if ng_thresh > 0:
            board.append(NoiseGate(threshold_db=20 * np.log10(max(ng_thresh, 1e-8))))

        # Compressor
        comp = dsp.get('compressor', {})
        comp_thresh = comp.get('threshold', 1)
        comp_ratio = comp.get('ratio', 1)
        if comp_thresh < 1 and comp_ratio > 1:
            board.append(Compressor(threshold_db=20 * np.log10(max(comp_thresh, 1e-8)), ratio=comp_ratio))

        # Limiter
        lim = dsp.get('limiter', {})
        lim_thresh = lim.get('threshold', 1)
        if lim_thresh < 1:
            board.append(Limiter(threshold_db=20 * np.log10(max(lim_thresh, 1e-8))))

        # Process audio
        frames = np.asarray(frames, dtype=np.float32).reshape(-1)
        frames = frames[np.newaxis, :]  # Add channel dimension
        processed = board(frames, samplerate)
        frames = processed.flatten().astype(np.float32)

        # Ensure correct frame count
        if len(frames) != chunk_frames:
            logger.error("Final DSP output frame size mismatch. Resizing.")
            frames = np.resize(frames, chunk_frames)

    except Exception as e:
        # Silently handle DSP errors and return original frames
        frames = np.resize(frames, chunk_frames)

    return frames

# --- Utility: Port Check ---
def is_port_in_use(port):
    """Check if a TCP port is already in use."""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(("127.0.0.1", port)) == 0

# --- Utility: Mask Sensitive Data ---
def mask_sensitive(data):
    """Mask sensitive information in logs."""
    if isinstance(data, dict):
        data = data.copy()
        for key in data:
            if 'token' in key.lower() or 'password' in key.lower() or 'secret' in key.lower():
                data[key] = '***MASKED***'
    return data


def clean_device_name(device_name):
    """Extract clean device name from system path, especially for Bluetooth devices."""
    if not device_name:
        return device_name

    # Handle Bluetooth device names with system paths
    # Example: "Headset (@System32\drivers\bthhfenum.sys,#2;%1 Hands-Free%0 ;(JBL WAVE BEAM))"
    # Extract the part in the last parentheses
    if "(" in device_name:
        # Find the last occurrence of parentheses
        last_paren_start = device_name.rfind("(")
        if last_paren_start >= 0:
            # Extract content from last parentheses
            remaining = device_name[last_paren_start + 1:]
            # Remove all closing parentheses from the end
            clean_name = remaining.rstrip(")")
            if clean_name and clean_name != device_name:  # Only use if different and not empty
                return clean_name

    # Handle other system path patterns
    # Remove common system path prefixes
    if "@System32" in device_name or "drivers\\" in device_name:
        # Try to extract device name after semicolon patterns
        parts = device_name.split(";")
        for part in reversed(parts):  # Check from end to beginning
            part = part.strip()
            if part and not part.startswith("@") and not part.startswith("%"):
                # Remove parentheses if they wrap the entire string
                if part.startswith("(") and part.endswith(")"):
                    part = part[1:-1]
                if part:
                    return part

    return device_name

# --- Custom Error Handlers ---
@app.exception_handler(Exception)
def generic_exception_handler(request: Request, exc: Exception):
    logger.error(f"Internal server error: {exc}")
    return JSONResponse(
        status_code=500,
        content={"error": "Internal server error"}
    )

@app.exception_handler(StarletteHTTPException)
def http_exception_handler(request: Request, exc: StarletteHTTPException):
    return JSONResponse(
        status_code=exc.status_code,
        content={"error": exc.detail}
    )

@app.exception_handler(FastAPIRequestValidationError)
def validation_exception_handler(request: Request, exc: FastAPIRequestValidationError):
    logger.warning(f"Validation error: {exc.errors()}")
    return JSONResponse(
        status_code=422,
        content={"error": "Invalid request data"}
    )

# --- Rate Limiter Setup ---
limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(
    RateLimitExceeded,
    lambda request, exc: _rate_limit_exceeded_handler(request, exc)  # type: ignore
)

# --- API Endpoints ---
@app.get("/")
def read_root():
    """Root endpoint - API status check."""
    return {
        "message": "Multi-mic Audio Agent API",
        "version": API_VERSION,
        "status": "running",
        "docs": "/docs"
    }

@app.get("/health")
def health_check():
    """Health check endpoint."""
    return {"status": "healthy", "timestamp": time.time()}

@app.post("/token")
@limiter.limit("5/minute")
async def login_for_access_token(request: Request, form_data: OAuth2PasswordRequestForm = Depends()):
    user = authenticate_user(fake_users_db, form_data.username, form_data.password)
    if not user:
        logger.warning(f"Failed login attempt for username: {form_data.username}")
        raise HTTPException(
            status_code=401,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token = create_access_token(data={"sub": user["username"]})
    logger.info(f"User {form_data.username} logged in successfully.")
    return {"access_token": access_token, "token_type": "bearer"}

@app.get("/list_mics")
def list_mics():
    """List all available unique microphone devices, with optional driver filtering on Windows. Fix: Do not deduplicate by name; number identical names."""
    logger.info("/list_mics called (refreshing device list)")
    sd._terminate()
    sd._initialize()
    devices = sd.query_devices()
    mics = []
    import platform
    driver_filter = None
    if platform.system() == "Windows":
        driver_filter = "Windows WDM-KS"
        if driver_filter:
            driver_filter = [d.strip().strip("'").strip('"') for d in driver_filter.split(',') if d.strip()]
        hostapis = list(sd.query_hostapis())
    else:
        hostapis = []
    # Track counts of identical names for numbering
    name_counts = {}
    for i, d in enumerate(devices):
        try:
            if isinstance(d, dict):
                max_input = d.get("max_input_channels", 0)
                name = d.get("name", str(i))
                hostapi = d.get("hostapi", None)
            else:
                max_input = getattr(d, "max_input_channels", 0)
                name = getattr(d, "name", str(i))
                hostapi = getattr(d, "hostapi", None)
            driver_name = None
            if platform.system() == "Windows" and hostapi is not None and hostapi < len(hostapis):
                try:
                    hostapi_entry = hostapis[hostapi]
                    if isinstance(hostapi_entry, dict):
                        driver_name = hostapi_entry.get('name', None)
                    elif isinstance(hostapi_entry, tuple) and len(hostapi_entry) > 0 and isinstance(hostapi_entry[0], dict):
                        driver_name = hostapi_entry[0].get('name', None)
                except Exception:
                    driver_name = None
            logger.info(f"Device: {name}, HostAPI: {hostapi}, Driver: {driver_name}, Filter: {driver_filter}")
            if driver_filter and driver_name and driver_name not in driver_filter:
                continue
            if int(max_input) > 0:
                # Clean the device name to remove system paths
                clean_name = clean_device_name(name)
                # Number identical names
                base_name = clean_name
                count = name_counts.get(base_name, 0) + 1
                name_counts[base_name] = count
                if count > 1:
                    display_name = f"{base_name} {count}"
                else:
                    display_name = base_name
                mics.append({"id": i, "name": display_name, "driver": driver_name})
        except Exception:
            continue
    logger.info(f"Microphones found: {mics}")
    return {"mics": mics}

@app.post("/start_recording")
async def start_recording(request: Request, current_user: dict = Security(get_current_user, scopes=[])):
    """Start recording from selected microphones."""
    metrics_manager.inc_request_count("POST", "/start_recording")

    try:
        # Handle both direct list format and wrapped format
        body = await request.json()
        if isinstance(body, list):
            # Direct list format: [{"device": 1, "role": "Doctor", "dsp": {}}]
            mics_data = body
        elif isinstance(body, dict) and "mics" in body:
            # Wrapped format: {"mics": [{"device": 1, "role": "Doctor", "dsp": {}}]}
            mics_data = body["mics"]
        else:
            raise ValueError("Invalid request format")

        # Convert to MicRole objects
        mics = []
        for mic_data in mics_data:
            mic_role = MicRole(**mic_data)
            mics.append(mic_role)

        logger.info(f"Parsed mics: {mask_sensitive([mic.dict() for mic in mics])}")

        for mic in mics:
            device = mic.device
            role = mic.role
            dsp = mic.dsp.dict() if mic.dsp else {}

            if device is None:
                logger.warning(f"Skipping mic with device=None: {mic}")
                continue
            if device in recording_threads:
                logger.info(f"Mic {device} already recording.")
                continue

            stop_q = queue.Queue()
            stream_q = queue.Queue()
            pause_event = threading.Event()  # For pause/resume functionality
            recording_queues[device] = stop_q
            stream_queues[device] = stream_q
            pause_events[device] = pause_event
            active_dsp_configs[device] = dsp

            # Get mic name for persistence
            mic_name = None
            for mic_entry in PERSISTED_DSP_SETTINGS:
                if PERSISTED_DSP_SETTINGS[mic_entry].get("deviceId") == device:
                    mic_name = mic_entry
                    break

            t = threading.Thread(target=record_mic, args=(device, role, stop_q, stream_q, pause_event, dsp, mic_name), daemon=True)
            recording_threads[device] = t
            t.start()
            metrics_manager.RECORDING_ACTIVE.inc()

        logger.info("Recording started for all requested mics.")
        return {"status": "started"}

    except ValueError as e:
        logger.error(f"Validation Error: {e}")
        return Response(content=json.dumps({"error": "Invalid input data"}), status_code=400, media_type="application/json")
    except Exception as e:
        logger.exception("An unexpected error occurred.")
        return Response(content=json.dumps({"error": "Internal server error"}), status_code=500, media_type="application/json")

@app.post("/stop_recording")
def stop_recording(current_user: dict = Security(get_current_user, scopes=[])):
    """Stop all active recordings and clean up resources."""
    metrics_manager.inc_request_count("POST", "/stop_recording")
    logger.info("/stop_recording called")
    for q in recording_queues.values():
        q.put(None)
    for t in list(recording_threads.values()):
        if t.is_alive():
            t.join(timeout=2)
    recording_threads.clear()
    recording_queues.clear()
    stream_queues.clear()
    pause_events.clear()
    active_dsp_configs.clear()
    metrics_manager.set_recording_active(0)
    logger.info("All recordings stopped.")
    return {"status": "stopped"}

@app.post("/pause_recording")
def pause_recording(current_user: dict = Security(get_current_user, scopes=[])):
    """Pause all active recordings."""
    metrics_manager.inc_request_count("POST", "/pause_recording")
    logger.info("/pause_recording called")
    for pause_event in pause_events.values():
        pause_event.set()
    logger.info("All recordings paused.")
    return {"status": "paused"}

@app.post("/resume_recording")
def resume_recording(current_user: dict = Security(get_current_user, scopes=[])):
    """Resume all paused recordings."""
    metrics_manager.inc_request_count("POST", "/resume_recording")
    logger.info("/resume_recording called")
    for pause_event in pause_events.values():
        pause_event.clear()
    logger.info("All recordings resumed.")
    return {"status": "resumed"}

class MicConfig(BaseModel):
    micname: str
    deviceId: str
    selectedRole: str
    settingsParams: dict

@app.post("/update_dsp")
async def update_dsp(request: Request, current_user: dict = Security(get_current_user, scopes=[])):
    """Update DSP settings for selected microphones and persist to dspsettings.json."""
    metrics_manager.inc_request_count("POST", "/update_dsp")
    try:
        # Parse the raw JSON payload to handle the frontend structure
        payload = await request.json()
        logger.info(f"/update_dsp called. Payload: {payload}")

        # Convert frontend payload to the expected format for active configs
        merged = {}
        # Prepare new settings for persistence (only selected mics)
        new_settings = {}

        for mic_name, mic_config in payload.items():
            device_id = int(mic_config.get("deviceId", 0))
            settings_params = mic_config.get("settingsParams", {})
            selected_role = mic_config.get("selectedRole", "Other")

            # Convert settingsParams to DSPConfig format for active configs
            merged[device_id] = settings_params

            # Prepare for persistence - only save selected microphones
            if mic_name and settings_params:
                new_settings[mic_name] = {
                    "micname": mic_name,
                    "deviceId": str(device_id),
                    "selectedRole": selected_role,
                    "settingsParams": settings_params
                }

        # Update active DSP configs for immediate use
        active_dsp_configs.clear()
        active_dsp_configs.update(merged)

        # Save only selected microphones' settings to persistent storage
        # Clear existing settings and save only current selection
        PERSISTED_DSP_SETTINGS.clear()
        PERSISTED_DSP_SETTINGS.update(new_settings)
        save_dsp_settings()

        logger.info(f"Updated active_dsp_configs: {active_dsp_configs}")
        logger.info(f"Persisted DSP settings for selected mics: {PERSISTED_DSP_SETTINGS}")
        return {"status": "DSP configurations updated and persisted for selected microphones"}
    except Exception as e:
        logger.exception(f"Unexpected error in /update_dsp: {e}")
        return Response(content=json.dumps({"error": "Internal server error"}), status_code=500, media_type="application/json")

class SaveDSPRequest(BaseModel):
    mic_name: str
    settings_params: dict

@app.post("/save_dsp_settings")
async def save_dsp_settings_endpoint(request: SaveDSPRequest, current_user: dict = Security(get_current_user, scopes=[])):
    """Save DSP settings for a specific microphone to persistent storage."""
    metrics_manager.inc_request_count("POST", "/save_dsp_settings")
    try:
        mic_name = request.mic_name
        settings_params = request.settings_params

        if mic_name in PERSISTED_DSP_SETTINGS:
            PERSISTED_DSP_SETTINGS[mic_name]["settingsParams"] = settings_params
        else:
            PERSISTED_DSP_SETTINGS[mic_name] = {
                "deviceId": None,  # Will be set when device is found
                "settingsParams": settings_params
            }

        save_dsp_settings()
        logger.info(f"Saved DSP settings for {mic_name}: {settings_params}")
        return {"status": "DSP settings saved", "mic_name": mic_name}
    except Exception as e:
        logger.exception(f"Unexpected error in /save_dsp_settings: {e}")
        return Response(content=json.dumps({"error": "Internal server error"}), status_code=500, media_type="application/json")

@app.get("/get_dsp_settings")
def get_dsp_settings_endpoint(current_user: dict = Security(get_current_user, scopes=[])):
    """Get all persisted DSP settings."""
    metrics_manager.inc_request_count("GET", "/get_dsp_settings")
    return {"dsp_settings": PERSISTED_DSP_SETTINGS}

@app.get("/dspsettings.json")
async def get_dsp_settings_file():
    """Serve the dspsettings.json file directly without authentication."""
    metrics_manager.inc_request_count("GET", "/dspsettings.json")
    logger.info("Serving dspsettings.json file")

    # If the file doesn't exist, return an empty JSON object
    if not os.path.exists(DSP_SETTINGS_FILE):
        logger.warning(f"DSP settings file not found at {DSP_SETTINGS_FILE}, returning empty object")
        return {}

    try:
        with open(DSP_SETTINGS_FILE, 'r') as f:
            settings = json.load(f)
        logger.info(f"Loaded DSP settings from file: {settings}")
        return settings
    except Exception as e:
        logger.error(f"Error reading dspsettings.json: {e}")
        return {}

@app.websocket("/audio_stream")
async def audio_stream(websocket: WebSocket):
    """WebSocket endpoint for live audio streaming to the frontend."""
    client_id = id(websocket)
    client_ip = websocket.client.host if websocket.client else "unknown"
    
    # --- JWT Auth for WebSocket ---
    token = websocket.query_params.get("token")
    logger.info(f"[WS] Client {client_id} from {client_ip} attempting connection")
    
    if not token:
        logger.warning(f"[WS] Client {client_id} rejected: Missing token")
        await websocket.close(code=1008, reason="Missing token")
        return
    try:
        user = get_current_user(token)
        logger.info(f"[WS] Client {client_id} authenticated as user: {user.get('username', 'unknown')}")
    except Exception as e:
        logger.warning(f"[WS] Client {client_id} authentication failed: {e}")
        await websocket.close(code=1008, reason="Invalid or expired token")
        return

    await websocket.accept()
    logger.info(f"[WS] Client {client_id} connected for audio stream")
    
    client_active = True
    ws_closed = False
    last_cleanup = time.time()
    packets_sent = 0
    bytes_sent = 0
    
    try:
        active_devices = list(stream_queues.keys())
        logger.info(f"[WS] Active devices for streaming: {active_devices}")
        
        if not active_devices:
            logger.error(f"[WS] Client {client_id}: No active mics for streaming!")
            await websocket.close()
            ws_closed = True
            return
            
        while not shutdown_event.is_set() and client_active:
            # Periodic queue cleanup every 30 seconds
            if time.time() - last_cleanup > 30:
                logger.debug(f"[WS] Client {client_id}: Performing periodic queue cleanup")
                for device in active_devices:
                    stream_q = stream_queues.get(device)
                    if stream_q and stream_q.qsize() > 50:
                        queue_size_before = stream_q.qsize()
                        # Clear half the queue
                        for _ in range(stream_q.qsize() // 2):
                            try:
                                stream_q.get_nowait()
                            except queue.Empty:
                                break
                        logger.info(f"[WS] Client {client_id}: Cleaned queue for device {device}, size: {queue_size_before} -> {stream_q.qsize()}")
                last_cleanup = time.time()
                
            sent_any = False
            for mic_idx in active_devices:
                stream_q = stream_queues.get(mic_idx)
                if stream_q is None:
                    continue
                    
                try:
                    chunk = stream_q.get_nowait()
                except queue.Empty:
                    continue
                    
                if chunk is None:
                    continue
                    
                samplerate, wav_bytes = chunk
                header = mic_idx.to_bytes(1, 'big') + int(samplerate).to_bytes(4, 'big') + len(wav_bytes).to_bytes(4, 'big')
                packet = header + wav_bytes
                
                try:
                    # Check connection state before sending
                    if websocket.client_state.name == 'DISCONNECTED':
                        logger.info(f"[WS] Client {client_id}: Connection state is DISCONNECTED")
                        client_active = False
                        break
                        
                    await websocket.send_bytes(packet)
                    packets_sent += 1
                    bytes_sent += len(packet)
                    sent_any = True
                    
                    # Log every 100 packets to avoid spam
                    if packets_sent % 100 == 0:
                        logger.debug(f"[WS] Client {client_id}: Sent {packets_sent} packets, {bytes_sent} bytes total")
                        
                except (ConnectionResetError, ConnectionAbortedError, ConnectionClosedError) as e:
                    logger.info(f"[WS] Client {client_id} disconnected during send: {e}")
                    client_active = False
                    break
                except Exception as e:
                    logger.error(f"[WS] Client {client_id} error sending audio: {e}")
                    client_active = False
                    break
                    
            if not sent_any:
                await asyncio.sleep(0.01)
                
    except WebSocketDisconnect:
        logger.info(f"[WS] Client {client_id} disconnected from audio stream")
    except (ConnectionResetError, ConnectionAbortedError) as e:
        logger.info(f"[WS] Client {client_id} connection forcibly closed: {e}")
    except Exception as e:
        logger.error(f"[WS] Client {client_id} unexpected error in audio_stream: {e}")
    finally:
        logger.info(f"[WS] Client {client_id} cleanup: packets_sent={packets_sent}, bytes_sent={bytes_sent}")
        
        # Only close if not already closed
        if not ws_closed and websocket.client_state and websocket.client_state.name != 'DISCONNECTED':
            try:
                await websocket.close()
                logger.debug(f"[WS] Client {client_id}: WebSocket closed successfully")
            except Exception as e:
                logger.warning(f"[WS] Client {client_id} exception during websocket.close(): {e}")
        
        # Cleanup orphaned threads/queues if no clients are connected
        logger.info(f"[WS] Client {client_id}: Checking for orphaned threads after disconnect...")
        if not websocket.client_state or websocket.client_state.name == 'DISCONNECTED':
            stop_recording()
            logger.info(f"[WS] Client {client_id}: Orphaned threads/queues cleaned up after disconnect")

# --- Recording Thread ---
def record_mic(device, role, stop_q, stream_q, pause_event, dsp=None, micname=None):
    """Thread target: Record audio from a single mic, apply DSP, and stream/save chunks."""
    import time
    start_time = time.time()
    metrics_manager.RECORDING_ACTIVE.inc()
    logger.info(f"Starting record_mic for device {device}, role {role}, DSP: {dsp}, micname: {micname}")
    
    try:
        # Validate device exists before attempting to use it
        devices = sd.query_devices()
        if device >= len(devices):
            logger.error(f"Device {device} does not exist (only {len(devices)} devices available)")
            return
            
        dev_info = sd.query_devices(device)
        if dev_info.get('max_input_channels', 0) == 0:
            logger.error(f"Device {device} has no input channels available")
            return
            
    except Exception as e:
        logger.error(f"Error querying device {device}: {e}")
        return
        
    samplerate = int(dev_info["default_samplerate"] if isinstance(dev_info, dict) else getattr(dev_info, "default_samplerate", 16000))
    chunk_frames = int(samplerate * CHUNK_SECONDS)
    max_buffer_size = chunk_frames * 30  # 30 seconds worth of audio
    max_queue_size = 100  # Limit queue size
    os.makedirs("recordings", exist_ok=True)
    all_frames = []
    audio_buffer = []
    buffer_lock = threading.Lock()
    stop_flag = threading.Event()

    def callback(indata, frames, time_info, status):
        if status:
            logger.warning(f"InputStream status: {status}")
        
        if not pause_event.is_set():
            with buffer_lock:
                # Prevent buffer overflow
                current_size = sum(arr.shape[0] for arr in audio_buffer)
                if current_size > max_buffer_size:
                    # Remove oldest chunks to make room
                    while audio_buffer and current_size > max_buffer_size * 0.7:
                        removed = audio_buffer.pop(0)
                        current_size -= removed.shape[0]
                    logger.warning(f"Audio buffer overflow for device {device}, dropped old chunks")
                
                audio_buffer.append(indata.copy())
        # Check for stop signal
        if not stop_q.empty():
            signal_val = stop_q.get()
            if signal_val is None:
                stop_flag.set()

    try:
        with sd.InputStream(device=device, channels=1, samplerate=samplerate, dtype='float32', callback=callback):
            logger.info(f"InputStream (callback) opened for device {device}")
            while not shutdown_event.is_set() and not stop_flag.is_set():
                # If paused, just sleep and continue
                if pause_event.is_set():
                    time.sleep(0.1)
                    continue

                # Wait for enough frames to form a chunk
                with buffer_lock:
                    total_frames = sum(arr.shape[0] for arr in audio_buffer)
                if total_frames < chunk_frames:
                    time.sleep(0.01)
                    continue
                # Gather enough frames for a chunk
                with buffer_lock:
                    frames_list = []
                    frames_needed = chunk_frames
                    while audio_buffer and frames_needed > 0:
                        arr = audio_buffer[0]
                        if arr.shape[0] <= frames_needed:
                            frames_list.append(arr)
                            frames_needed -= arr.shape[0]
                            audio_buffer.pop(0)
                        else:
                            frames_list.append(arr[:frames_needed])
                            audio_buffer[0] = arr[frames_needed:]
                            frames_needed = 0
                    if not frames_list:
                        continue
                    frames = np.concatenate(frames_list, axis=0)

                # DSP processing - use persisted settings if available
                dsp_cfg = None
                if micname and micname in PERSISTED_DSP_SETTINGS:
                    dsp_cfg = PERSISTED_DSP_SETTINGS[micname].get("settingsParams", dsp)
                elif device in active_dsp_configs:
                    dsp_cfg = active_dsp_configs[device]
                elif dsp is not None:
                    dsp_cfg = dsp

                if dsp_cfg:
                    # Use the DSP processor directly instead of the class method
                    frames = apply_dsp(frames, dsp_cfg, samplerate, chunk_frames)

                all_frames.append(frames)
                buf = io.BytesIO()
                sf.write(buf, frames, samplerate, format='WAV', subtype='PCM_16')
                wav_bytes = buf.getvalue()
                # Put chunk in stream queue with size check
                try:
                    if stream_q.qsize() > max_queue_size:
                        # Remove oldest chunks from queue
                        try:
                            while stream_q.qsize() > max_queue_size * 0.7:
                                stream_q.get_nowait()
                        except queue.Empty:
                            pass
                        logger.warning(f"Stream queue overflow for device {device}, dropped old chunks")
                    
                    stream_q.put((samplerate, wav_bytes), timeout=0.1)
                except (queue.Full, queue.Empty):
                    logger.warning(f"Failed to queue audio chunk for device {device}")
                time.sleep(0.01)
        if all_frames:
            all_frames = [f.flatten() if f.ndim > 1 else f for f in all_frames]
            full_audio = np.concatenate(all_frames, axis=0)
            ts = int(time.time())
            full_path = f"recordings/mic{device}_{role}_{ts}_full.wav"
            sf.write(full_path, full_audio, samplerate)
            logger.info(f"Saved full session to {full_path}")
    except Exception as e:
        metrics_manager.inc_recording_errors()
        logger.error(f"Error in record_mic for device {device}, role {role}: {e}")
    finally:
        metrics_manager.RECORDING_ACTIVE.dec()
        duration = time.time() - start_time
        metrics_manager.observe_recording_duration(duration)


if __name__ == "__main__":
    # Check if port is already in use
    if is_port_in_use(PORT):
        logger.error(f"Port {PORT} is already in use. Please stop the other process or choose a different port.")
        # Only print to console when not running as executable
        if not getattr(sys, 'frozen', False):
            print(f"ERROR: Port {PORT} is already in use. Another audio agent may already be running.")
        # Don't use input() in executable as it causes "lost sys.stdin" error
        sys.exit(1)

    logger.info(f"Starting Audio Agent on {HOST}:{PORT}")
    logger.info(f"Environment: {ENVIRONMENT}")
    logger.info(f"Allowed origins: {ALLOWED_ORIGINS}")

    try:
        uvicorn.run(
            app,
            host=HOST,
            port=PORT,
            log_level="info",
            access_log=True,
            log_config=None  # Disable uvicorn's default logging config to avoid formatter conflicts
        )
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error(f"Error starting server: {e}")
        sys.exit(1)
