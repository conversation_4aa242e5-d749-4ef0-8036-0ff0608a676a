import React from "react";

/**
 * TimelineSlider
 * Props:
 *   - currentTime: number (seconds)
 *   - duration: number (seconds)
 *   - onChange: function (called with new time in seconds as user drags)
 *   - onChangeEnd: function (called with new time in seconds on drag end)
 *   - visible: boolean
 */
function formatTime(time = 0) {
  const minutes = Math.floor(time / 60);
  const seconds = Math.floor(time % 60);
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
}

const TimelineSlider = ({ currentTime = 0, duration = 0, onChange, onChangeEnd, visible = false }) => {
  if (!visible || duration <= 0) return null;
  const percent = duration ? (currentTime / duration) * 100 : 0;
  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: 12, margin: '16px 0 8px 0', width: '100%' }}>
      <input
        type="text"
        value={formatTime(currentTime)}
        readOnly
        style={{ width: 48, textAlign: 'center', border: 'none', background: 'transparent', fontWeight: 500 }}
        tabIndex={-1}
      />
      <input
        type="range"
        min={0}
        max={100}
        step={0.1}
        value={percent}
        onChange={e => {
          if (onChange) onChange((parseFloat(e.target.value) / 100) * duration);
        }}
        onMouseUp={e => {
          if (onChangeEnd) onChangeEnd((parseFloat(e.target.value) / 100) * duration);
        }}
        style={{ flex: 1, accentColor: '#007bff', height: 4 }}
        aria-label="Seek timeline"
      />
      <input
        type="text"
        value={formatTime(duration)}
        readOnly
        style={{ width: 48, textAlign: 'center', border: 'none', background: 'transparent', fontWeight: 500 }}
        tabIndex={-1}
      />
    </div>
  );
};

export default TimelineSlider;
