// Text formatting utilities for different languages

const formatEnglishText = (text) => {
  // Remove extra spaces
  let formatted = text.replace(/\s+/g, ' ').trim();
  
  // Ensure proper capitalization at the start of sentences
  formatted = formatted.replace(/([.!?]\s+)([a-z])/g, (match, p1, p2) => p1 + p2.toUpperCase());
  
  // Ensure first character is capitalized
  formatted = formatted.charAt(0).toUpperCase() + formatted.slice(1);
  
  return formatted;
};

const formatMalayalamText = (text) => {
  // Remove extra spaces
  let formatted = text.replace(/\s+/g, ' ').trim();
  
  // Add proper Malayalam full stop if missing
  if (!/[.!?।॥]$/.test(formatted)) {
    formatted += '.';
  }
  
  return formatted;
};

export const formatTranscriptText = (text, language) => {
  if (!text) return '';
  
  // Clean up common issues first
  let cleaned = text
    .replace(/\s+/g, ' ')  // normalize spaces
    .trim();
    
  // Apply language-specific formatting
  if (language.startsWith('en')) {
    cleaned = formatEnglishText(cleaned);
  } else if (language === 'ml-IN') {
    cleaned = formatMalayalamText(cleaned);
  }
  
  return cleaned;
};
