from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OAuth2PasswordRequestForm
from jose import JW<PERSON>rror, jwt
from passlib.context import Crypt<PERSON>ontext
from datetime import datetime, timed<PERSON>ta
from typing import Optional
from .configuration_manager import ConfigurationManager
import os
import sys
import uuid

config_manager = ConfigurationManager()

# --- JWT Config ---
# Get environment mode first to determine security requirements
ENVIRONMENT = os.environ.get('AUDIO_AGENT_ENV') or config_manager.get('environment', 'env', fallback='development')

SECRET_KEY = os.environ.get("AUDIO_AGENT_SECRET_KEY") or config_manager.get('security', 'jwt_secret', fallback='dev-jwt-secret-key-for-audio-agent-2024')
if not SECRET_KEY or not isinstance(SECRET_KEY, str):
    raise RuntimeError("SECRET_KEY must be set for JWT encoding and must be a string.")

# Only enforce secure secret in production
if ENVIRONMENT == 'production' and (SECRET_KEY == 'CHANGE_THIS_SECRET_KEY' or SECRET_KEY == 'dev-secret-key-change-for-production-use' or SECRET_KEY == 'dev-jwt-secret-key-for-audio-agent-2024'):
    raise RuntimeError("SECRET_KEY must be set to a secure value in production. Do not use default value.")

# No warning needed for development with the updated dev key
if ENVIRONMENT != 'production' and (SECRET_KEY == 'CHANGE_THIS_SECRET_KEY' or SECRET_KEY == 'dev-secret-key-change-for-production-use'):
    # Only print warning when not running as executable
    if not getattr(sys, 'frozen', False):
        print("WARNING: Using default SECRET_KEY for development. Set AUDIO_AGENT_SECRET_KEY environment variable for production.")

ALGORITHM = str(config_manager.get('security', 'jwt_algorithm', fallback='HS256'))
if not ALGORITHM:
    ALGORITHM = 'HS256'
ACCESS_TOKEN_EXPIRE_MINUTES = config_manager.getint('security', 'jwt_expire_minutes', fallback=30) or 30

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/token")

# Get admin password from environment or config
ADMIN_PASSWORD = os.environ.get("ADMIN_PASSWORD") or config_manager.get('environment', 'admin_password', fallback='CHANGE_THIS_ADMIN_PASSWORD')

if ADMIN_PASSWORD == 'CHANGE_THIS_ADMIN_PASSWORD':
    # Only print warning when not running as executable
    if not getattr(sys, 'frozen', False):
        print("WARNING: Using default admin password. Set ADMIN_PASSWORD environment variable or update config file for production.")

# Ensure we have valid strings for all security parameters
SECRET_KEY = str(SECRET_KEY)
ADMIN_PASSWORD = str(ADMIN_PASSWORD)

# SECURITY WARNING: This is a development-only user database
# In production, replace with a proper user management system
fake_users_db = {
    "admin": {
        "username": "admin",
        "full_name": "Administrator",
        "hashed_password": pwd_context.hash(ADMIN_PASSWORD),
        "disabled": False,
    }
}

# Account lockout protection
login_attempts = {}
lockout_times = {}
MAX_LOGIN_ATTEMPTS = config_manager.getint('security', 'max_login_attempts', fallback=5) or 5
LOCKOUT_DURATION = config_manager.getint('security', 'lockout_duration_minutes', fallback=15) or 15

# Session timeout and token refresh
JWT_REFRESH_EXPIRE_MINUTES = config_manager.getint('security', 'jwt_refresh_expire_minutes', fallback=1440) or 1440  # 24 hours
IDLE_TIMEOUT_MINUTES = config_manager.getint('security', 'idle_timeout_minutes', fallback=30) or 30

# Session tracking for idle timeout
active_sessions = {}
session_last_activity = {}

def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def get_user(db, username: str):
    if username in db:
        user = db[username]
        return user
    return None

def is_account_locked(username: str) -> bool:
    """Check if account is currently locked due to failed login attempts."""
    if username in lockout_times:
        lockout_time = lockout_times[username]
        if datetime.utcnow() < lockout_time:
            return True
        else:
            # Lockout expired, reset
            del lockout_times[username]
            if username in login_attempts:
                del login_attempts[username]
    return False

def record_failed_login(username: str):
    """Record a failed login attempt and apply lockout if necessary."""
    if username not in login_attempts:
        login_attempts[username] = 0
    login_attempts[username] += 1
    
    if login_attempts[username] >= MAX_LOGIN_ATTEMPTS:
        lockout_times[username] = datetime.utcnow() + timedelta(minutes=LOCKOUT_DURATION)

def authenticate_user(db, username: str, password: str):
    # Check if account is locked
    if is_account_locked(username):
        return False
        
    user = get_user(db, username)
    if not user:
        record_failed_login(username)
        return False
    
    if not verify_password(password, user["hashed_password"]):
        record_failed_login(username)
        return False
    
    # Successful login - reset failed attempts
    if username in login_attempts:
        del login_attempts[username]
    if username in lockout_times:
        del lockout_times[username]
        
    return user

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    # Add session ID for tracking
    session_id = str(uuid.uuid4())
    to_encode.update({
        "exp": expire,
        "jti": session_id,  # JWT ID for session tracking
        "iat": datetime.utcnow()  # Issued at time
    })
    
    encoded_jwt = jwt.encode(to_encode, str(SECRET_KEY), algorithm=str(ALGORITHM))
    
    # Track the session
    track_session_activity(data.get("sub", "unknown"), encoded_jwt)
    
    return encoded_jwt

def get_current_user(token: str = Depends(oauth2_scheme)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        # Check if session has expired due to inactivity
        if is_session_expired(token):
            raise credentials_exception
            
        payload = jwt.decode(token, str(SECRET_KEY), algorithms=[str(ALGORITHM)])
        username: Optional[str] = payload.get("sub")
        if username is None:
            raise credentials_exception
            
        # Update session activity
        track_session_activity(username, token)
        
    except JWTError:
        raise credentials_exception
    user = get_user(fake_users_db, username)
    if user is None:
        raise credentials_exception
    return user

def track_session_activity(username: str, token: str):
    """Track user session activity for idle timeout."""
    import time
    try:
        session_id = jwt.decode(token, str(SECRET_KEY), algorithms=[str(ALGORITHM)], options={"verify_exp": False}).get("jti")
        if session_id:
            session_last_activity[session_id] = time.time()
            active_sessions[session_id] = username
    except Exception:
        pass  # Ignore errors in session tracking

def is_session_expired(token: str) -> bool:
    """Check if session has exceeded idle timeout."""
    import time
    try:
        payload = jwt.decode(token, str(SECRET_KEY), algorithms=[str(ALGORITHM)], options={"verify_exp": False})
        session_id = payload.get("jti")
        if session_id and session_id in session_last_activity:
            last_activity = session_last_activity[session_id]
            if time.time() - last_activity > (IDLE_TIMEOUT_MINUTES * 60):
                # Clean up expired session
                del session_last_activity[session_id]
                if session_id in active_sessions:
                    del active_sessions[session_id]
                return True
        return False
    except Exception:
        return True
