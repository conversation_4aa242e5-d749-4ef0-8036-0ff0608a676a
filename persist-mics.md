# Functional Requirements: Persisting Microphone Selection, DSP Settings, and Roles

## 📄 Overview
This document outlines the complete functionality for persisting microphone selection, roles (e.g., <PERSON>, <PERSON><PERSON>), and all DSP (Digital Signal Processing) configurations using a local `dspsettings.json` file. These settings should be used by `audio_agent.py` to ensure consistency across sessions and reboots.

---

## 📁 1. Initialization

- On startup, `audio_agent.py` must:
  - Check if the file `dspsettings.json` exists in the same directory.
  - If it does not exist, create an empty file with `{}`.

---

## 🖛 2. Microphone Selection & DSP Configuration (Frontend)

### User Flow
1. User selects microphones in the UI.
2. Assigns roles to each mic (Doctor, Nurse, Patient).
3. Configures DSP parameters:
   - Noise Gate
   - Compressor
   - Equalizer (EQ)
   - Limiter (if applicable)

### Payload Format
Frontend sends this payload to the backend:
```json
{
  "0": {
    "micname": "USB Microphone",
    "selectedRole": "speaker",
    "settingsParams": {
      "noiseGate": {"threshold": 0.1},
      "compressor": {"threshold": 0.8, "ratio": 2.0},
      "eq": {"lowCutFreq": 100, "highCutFreq": 8000}
    }
  },
  "1": {
    "micname": "Built-in Mic",
    "selectedRole": "listener",
    "settingsParams": {
      "noiseGate": {"threshold": 0.2},
      "compressor": {"threshold": 0.9, "ratio": 1.5},
      "eq": {"lowCutFreq": 80, "highCutFreq": 7000}
    }
  }
}
```

---

## 🚧 3. Backend: `POST /update_dsp`

### Responsibilities
- Parse and validate JSON payload.
- Ensure required fields are present:
  - `micname`, `selectedRole`, `settingsParams`
- Insert or update entries in `dspsettings.json` keyed by `micname`
- Remove entries not in the current payload
- Save updated configuration to disk

### Sample Python Logic (Pseudo)
```python
if not os.path.exists('dspsettings.json'):
    with open('dspsettings.json', 'w') as f:
        json.dump({}, f)

# Load, update, and persist
with open('dspsettings.json') as f:
    current_settings = json.load(f)

for mic in payload:
    current_settings[micname] = payload[mic]

# Remove obsolete entries
for micname in list(current_settings):
    if micname not in payload:
        del current_settings[micname]

with open('dspsettings.json', 'w') as f:
    json.dump(current_settings, f, indent=2)
```

---

## 🗒️ 4. JSON File Structure

### File: `dspsettings.json`
```json
{
  "USB Microphone": {
    "micname": "USB Microphone",
    "deviceId":"0",
    "selectedRole": "speaker",
    "settingsParams": {
      "noiseGate": {"threshold": 0.1},
      "compressor": {"threshold": 0.8, "ratio": 2.0},
      "eq": {"lowCutFreq": 100, "highCutFreq": 8000}
    }
  },
  "Built-in Mic": {
    "micname": "Built-in Mic",
     "deviceId":"1",
    "selectedRole": "listener",
    "settingsParams": {
      "noiseGate": {"threshold": 0.2},
      "compressor": {"threshold": 0.9, "ratio": 1.5},
      "eq": {"lowCutFreq": 80, "highCutFreq": 7000}
    }
  }
}
```

---

## 🧰 5. Runtime Usage in Agent

### On Start or Activation:
- Load file using `load_dsp_settings()`
- Identify the mic by `micname`
- Extract `selectedRole` and `settingsParams`
- Apply DSP chains with specified parameters for that mic

---

## 🚀 6. Additional API Endpoints

| Endpoint             | Method | Purpose                                             |
|----------------------|--------|-----------------------------------------------------|
| `/update_dsp`        | POST   | Update mic + DSP settings and persist them         |
| `/dspsettings.json`  | GET    | Return the entire persisted DSP settings file      |
| `/list_mics`         | GET    | List available mics with disambiguated names       |

---

## ✅ 7. Functional Flow Summary

1. User selects/configures microphones from frontend
2. Configuration is sent via POST `/update_dsp`
3. Backend validates and updates `dspsettings.json`
4. File is kept in sync with current frontend selection
5. On agent startup or session start:
   - File is read
   - Settings are applied to microphone capture chain
6. Persisted data survives restarts and agent reboots

---

## 📅 8. Future Enhancements
- Timestamped change history (audit trail)
- Mic ID tracking (in addition to name)
- Versioning of settings per session
- Auto-restore last used configuration on crash recovery

