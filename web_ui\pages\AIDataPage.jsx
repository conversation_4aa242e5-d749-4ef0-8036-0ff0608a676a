import React, { useEffect, useState } from 'react';
import AIDataModal from '../components/AIDataModal.jsx';

function AIDataPage() {
  const [patientId, setPatientId] = useState('');
  const [patientName, setPatientName] = useState('');

  useEffect(() => {
    const query = new URLSearchParams(window.location.search);

    setPatientId(query.get('patientId') || '');
    setPatientName(query.get('patientName') || '');

    // Add beforeunload event listener for tab closing
    const handleBeforeUnload = (event) => {
      // For AI Data page, we can just allow closing without warnings
      // since there's no recording state to worry about
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  const handleClose = () => {
    try {
      // Try to close the tab (this will work if the tab was opened by script)
      window.close();
    } catch (error) {
      console.log('Cannot close tab programmatically, redirecting to entry point');
      // If we can't close the tab, redirect back to the trigger page
      window.location.href = '/EntryPoint.html';
    }
  };

  return (
    <div style={{ 
      width: '100vw', 
      height: '100vh', 
      overflow: 'hidden',
      background: '#f5f5f5'
    }}>
      <AIDataModal
        isOpen={true}
        onClose={handleClose}
        patientId={patientId}
        patientName={patientName}
      />
    </div>
  );
}

export default AIDataPage;
