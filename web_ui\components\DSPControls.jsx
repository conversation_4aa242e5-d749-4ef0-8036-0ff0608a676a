import React from 'react';

function DSPControls({ dsp, onChange }) {
  // Ensure all DSP sections and parameters have default values
  // Set default values as the first values of each slider/range
 const defaultDSP = {
  noiseGate: { threshold: 0.1, attack: 0.01, hold: 0.01, release: 0.01, ratio: 2 },
  compressor: { threshold: 0.1, attack: 0.01, hold: 0.01, release: 0.05, ratio: 2 },
  limiter: { threshold: 0.1, ratio: 2 },
  eq: {
    lowCutFreq: 400,
    lowCutSlope: 18,
    highCutFreq: 10000,
    highCutSlope: 18,
    mid1Freq: 500,
    mid1Q: 1,
    mid2Freq: 4000,
    mid2Q: 1
  }
};

  const safeDSP = {
    noiseGate: { ...defaultDSP.noiseGate, ...dsp.noiseGate },
    compressor: { ...defaultDSP.compressor, ...dsp.compressor },
    limiter: { ...defaultDSP.limiter, ...dsp.limiter },
    eq: { ...defaultDSP.eq, ...dsp.eq }
  };

  // Helper to render a slider + number input pair
  const renderSliderWithBox = (section, param, value, min, max, step, label, unit) => (
    <label style={{ display: 'flex', alignItems: 'center', gap: 4, marginBottom: 4 }}>
      {label}
      <input
        type="range"
        min={min}
        max={max}
        step={step}
        value={value}
        style={{ flex: 1 }}
        onChange={e => onChange(section, param, +e.target.value)}
      />
      <input
        type="number"
        min={min}
        max={max}
        step={step}
        value={value}
        style={{
          width: 48,
          // padding: '2px 6px',
          border: '1px solid #ccc',
          borderRadius: 4,
          background: '#fafbfc',
          fontSize: 12,
          textAlign: 'right',
          // boxSizing: 'border-box',
          outline: 'none',
          transition: 'border 0.2s',
        }}
        onFocus={e => e.target.style.border = '1.5px solid #888'}
        onBlur={e => {
          e.target.style.border = '1px solid #ccc';
          // If left empty, reset to min
          if (e.target.value === "") onChange(section, param, min);
        }}
        onChange={e => {
          let v = e.target.value;
          // Allow empty string for editing
          if (v === "") onChange(section, param, "");
          else onChange(section, param, +v);
        }}
      />
      {unit && <span style={{ marginLeft: 0, fontSize: 12 }}>{unit}</span>}
    </label>
  );

  return (
    <div className="dsp-controls">
      <details>
        <summary>📏 Noise Gate</summary>
        {renderSliderWithBox('noiseGate', 'threshold', safeDSP.noiseGate.threshold, 0, 1, 0.01, 'Threshold')}
        {renderSliderWithBox('noiseGate', 'attack', safeDSP.noiseGate.attack, 0, 0.2, 0.01, 'Attack (s)', 's')}
        {renderSliderWithBox('noiseGate', 'hold', safeDSP.noiseGate.hold, 0, 0.5, 0.01, 'Hold (s)', 's')}
        {renderSliderWithBox('noiseGate', 'release', safeDSP.noiseGate.release, 0, 0.5, 0.01, 'Release (s)', 's')}
        {renderSliderWithBox('noiseGate', 'ratio', safeDSP.noiseGate.ratio, 1, 20, 0.01, 'Ratio')}
      </details>
      <details>
        <summary>📉 Soft-Knee Compressor</summary>
        {renderSliderWithBox('compressor', 'threshold', safeDSP.compressor.threshold, 0, 1, 0.01, 'Threshold')}
        {renderSliderWithBox('compressor', 'attack', safeDSP.compressor.attack, 0, 0.2, 0.01, 'Attack (s)', 's')}
        {renderSliderWithBox('compressor', 'hold', safeDSP.compressor.hold, 0, 0.5, 0.01, 'Hold (s)', 's')}
        {renderSliderWithBox('compressor', 'release', safeDSP.compressor.release, 0, 1.0, 0.01, 'Release (s)', 's')}
        {renderSliderWithBox('compressor', 'ratio', safeDSP.compressor.ratio, 1, 20, 0.01, 'Ratio')}
      </details>
      <details>
        <summary>🔊 Hard Limiter</summary>
        {renderSliderWithBox('limiter', 'threshold', safeDSP.limiter.threshold, 0, 1, 0.01, 'Limiter Threshold')}
        {/* Limiter Ratio removed: not supported by backend */}
      </details>
      <details>
        <summary>🎛️ 4-Band Equalizer</summary>
        {renderSliderWithBox('eq', 'lowCutFreq', safeDSP.eq.lowCutFreq, 20, 1000, 1, 'Low Cut Freq', 'Hz')}
        {renderSliderWithBox('eq', 'lowCutSlope', safeDSP.eq.lowCutSlope, 6, 48, 6, 'Low Cut Slope', 'dB/oct')}
        {renderSliderWithBox('eq', 'highCutFreq', safeDSP.eq.highCutFreq, 2000, 20000, 100, 'High Cut Freq', 'Hz')}
        {renderSliderWithBox('eq', 'highCutSlope', safeDSP.eq.highCutSlope, 6, 48, 6, 'High Cut Slope', 'dB/oct')}
        {renderSliderWithBox('eq', 'mid1Freq', safeDSP.eq.mid1Freq, 200, 2000, 10, 'Mid1 Freq', 'Hz')}
        {renderSliderWithBox('eq', 'mid1Q', safeDSP.eq.mid1Q, 0.1, 10, 0.1, 'Mid1 Q')}
        {renderSliderWithBox('eq', 'mid2Freq', safeDSP.eq.mid2Freq, 2000, 8000, 10, 'Mid2 Freq', 'Hz')}
        {renderSliderWithBox('eq', 'mid2Q', safeDSP.eq.mid2Q, 0.1, 10, 0.1, 'Mid2 Q')}
      </details>
    </div>
  );
}

export default DSPControls;
