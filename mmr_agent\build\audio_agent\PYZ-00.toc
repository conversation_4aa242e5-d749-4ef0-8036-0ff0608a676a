('/Users/<USER>/Documents/VoidDevSpace/Project '
 'Hope/mmr_agent/build/audio_agent/PYZ-00.pyz',
 [('__future__',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/__future__.py',
   'PYMODULE'),
  ('_colorize',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_colorize.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_compression.py',
   'PYMODULE'),
  ('_opcode_metadata',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_opcode_metadata.py',
   'PYMODULE'),
  ('_py_abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_py_abc.py',
   'PYMODULE'),
  ('_pydatetime',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pydecimal.py',
   'PYMODULE'),
  ('_strptime',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_threading_local.py',
   'PYMODULE'),
  ('argparse',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/argparse.py',
   'PYMODULE'),
  ('ast',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ast.py',
   'PYMODULE'),
  ('base64',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/base64.py',
   'PYMODULE'),
  ('bisect',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/bisect.py',
   'PYMODULE'),
  ('bz2',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/bz2.py',
   'PYMODULE'),
  ('calendar',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/calendar.py',
   'PYMODULE'),
  ('contextlib',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py',
   'PYMODULE'),
  ('contextvars',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextvars.py',
   'PYMODULE'),
  ('copy',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/copy.py',
   'PYMODULE'),
  ('csv',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/csv.py',
   'PYMODULE'),
  ('dataclasses',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/dataclasses.py',
   'PYMODULE'),
  ('datetime',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/datetime.py',
   'PYMODULE'),
  ('decimal',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/decimal.py',
   'PYMODULE'),
  ('dis',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/dis.py',
   'PYMODULE'),
  ('email',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/base64mime.py',
   'PYMODULE'),
  ('email.charset',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/encoders.py',
   'PYMODULE'),
  ('email.errors',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/errors.py',
   'PYMODULE'),
  ('email.feedparser',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/feedparser.py',
   'PYMODULE'),
  ('email.generator',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/generator.py',
   'PYMODULE'),
  ('email.header',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/header.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/iterators.py',
   'PYMODULE'),
  ('email.message',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/message.py',
   'PYMODULE'),
  ('email.parser',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/parser.py',
   'PYMODULE'),
  ('email.policy',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/utils.py',
   'PYMODULE'),
  ('fnmatch',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/fnmatch.py',
   'PYMODULE'),
  ('fractions',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/fractions.py',
   'PYMODULE'),
  ('getopt',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/getopt.py',
   'PYMODULE'),
  ('gettext',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/gettext.py',
   'PYMODULE'),
  ('glob',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/glob.py',
   'PYMODULE'),
  ('gzip',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/gzip.py',
   'PYMODULE'),
  ('hashlib',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/hashlib.py',
   'PYMODULE'),
  ('importlib',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_text.py',
   'PYMODULE'),
  ('importlib.readers',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_functional.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.util',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/util.py',
   'PYMODULE'),
  ('inspect',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/inspect.py',
   'PYMODULE'),
  ('ipaddress',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ipaddress.py',
   'PYMODULE'),
  ('json',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/__init__.py',
   'PYMODULE'),
  ('json.decoder',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/decoder.py',
   'PYMODULE'),
  ('json.encoder',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/encoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/scanner.py',
   'PYMODULE'),
  ('logging',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/logging/__init__.py',
   'PYMODULE'),
  ('lzma',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lzma.py',
   'PYMODULE'),
  ('numbers',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/numbers.py',
   'PYMODULE'),
  ('opcode',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/opcode.py',
   'PYMODULE'),
  ('pathlib',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pathlib/__init__.py',
   'PYMODULE'),
  ('pathlib._abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pathlib/_abc.py',
   'PYMODULE'),
  ('pathlib._local',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pathlib/_local.py',
   'PYMODULE'),
  ('pickle',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pickle.py',
   'PYMODULE'),
  ('pprint',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pprint.py',
   'PYMODULE'),
  ('py_compile',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/py_compile.py',
   'PYMODULE'),
  ('pyarmor_runtime_000000',
   '/Users/<USER>/Documents/VoidDevSpace/Project '
   'Hope/mmr_agent/dist_obj/pyarmor_runtime_000000/__init__.py',
   'PYMODULE'),
  ('quopri',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/quopri.py',
   'PYMODULE'),
  ('random',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/random.py',
   'PYMODULE'),
  ('selectors',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/selectors.py',
   'PYMODULE'),
  ('shutil',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/shutil.py',
   'PYMODULE'),
  ('signal',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/signal.py',
   'PYMODULE'),
  ('socket',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socket.py',
   'PYMODULE'),
  ('statistics',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/statistics.py',
   'PYMODULE'),
  ('string',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/string.py',
   'PYMODULE'),
  ('stringprep',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/stringprep.py',
   'PYMODULE'),
  ('subprocess',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/subprocess.py',
   'PYMODULE'),
  ('tarfile',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tarfile.py',
   'PYMODULE'),
  ('tempfile',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tempfile.py',
   'PYMODULE'),
  ('textwrap',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/textwrap.py',
   'PYMODULE'),
  ('threading',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/threading.py',
   'PYMODULE'),
  ('token',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/token.py',
   'PYMODULE'),
  ('tokenize',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tracemalloc.py',
   'PYMODULE'),
  ('typing',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/typing.py',
   'PYMODULE'),
  ('urllib',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/__init__.py',
   'PYMODULE'),
  ('urllib.parse',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/parse.py',
   'PYMODULE'),
  ('zipfile',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/zipfile/__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/zipfile/_path/__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/zipfile/_path/glob.py',
   'PYMODULE')])
