import React, { useEffect } from 'react';
import ClinicalPage from './pages/ClinicalPage.jsx';
import AIDataPage from './pages/AIDataPage.jsx';

function App() {
  useEffect(() => {
    const query = new URLSearchParams(window.location.search);
    const openTab = query.get('openTab');

    // If no openTab param, this is the main app - redirect to entry point
    if (!openTab) {
      window.location.href = '/EntryPoint.html';
      return;
    }
  }, []);

  // Check URL parameters to determine which page to show
  const query = new URLSearchParams(window.location.search);
  const openTab = query.get('openTab');

  if (openTab === 'recording') {
    return <ClinicalPage />;
  } else if (openTab === 'view') {
    return <AIDataPage />;
  }

  // Default fallback - redirect to entry point
  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      flexDirection: 'column',
      gap: '20px'
    }}>
      <h2>Redirecting to Entry Point...</h2>
      <p>If you are not redirected automatically, <a href="/EntryPoint.html">click here</a>.</p>
    </div>
  );
}

export default App;
