.feedback-input-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 16px;
}

.feedback-textarea-container {
  width: 100%;
}

.feedback-textarea {
  width: 100%;
  min-height: 100px;
  padding: 12px;
  border: 1px solid #DDDDDD;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
  box-sizing: border-box;
}

.rating-button-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.star-rating {
  display: flex;
  align-items: center;
}

.star {
  font-size: 24px;
  color: #DDDDDD;
  cursor: pointer;
  transition: color 0.2s;
  margin-right: 5px;
}

.star:hover, .star.filled {
  color: #FFD700;
}

.btn-submit {
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.btn-submit:hover {
  background-color: #3D9140;
}