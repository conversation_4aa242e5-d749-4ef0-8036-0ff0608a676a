{"name": "clinical-conversation-ui", "version": "0.1.0", "private": true, "description": "AI-Powered Ambient Listening Technology for Clinical Documentation UI", "main": "index.js", "scripts": {"start": "vite --open", "dev": "vite --open", "build": "vite build", "test": "echo \"No tests yet\""}, "dependencies": {"@azure/storage-blob": "^12.27.0", "@vitejs/plugin-react": "^4.4.1", "archiver": "^7.0.1", "axios": "^1.9.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "lucide-react": "^0.511.0", "multer": "^2.0.0", "nodemailer": "^7.0.3", "pg": "^8.16.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.6.0", "reflect-metadata": "^0.2.2", "socket.io-client": "^4.8.1", "typeorm": "^0.3.25", "wavesurfer.js": "^7.9.5"}, "devDependencies": {"ts-node": "^10.9.2", "typescript": "^5.8.3", "vite": "^6.3.5"}, "keywords": [], "author": "", "license": "ISC"}