(['/Users/<USER>/Documents/VoidDevSpace/Project '
  'Hope/mmr_agent/dist_obj/src/audio_agent.py'],
 ['/Users/<USER>/Documents/VoidDevSpace/Project Hope/mmr_agent/dist_obj',
  '/Users/<USER>/Documents/VoidDevSpace/Project Hope/mmr_agent/dist_obj/src'],
 [],
 [('/Users/<USER>/Documents/VoidDevSpace/Project '
   'Hope/mmr_agent/venv/lib/python3.13/site-packages/numpy/_pyinstaller',
   0),
  ('/Users/<USER>/Documents/VoidDevSpace/Project '
   'Hope/mmr_agent/venv/lib/python3.13/site-packages/_pyinstaller_hooks_contrib/stdhooks',
   -1000),
  ('/Users/<USER>/Documents/VoidDevSpace/Project '
   'Hope/mmr_agent/venv/lib/python3.13/site-packages/_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('audio_agent_config.ini',
   '/Users/<USER>/Documents/VoidDevSpace/Project '
   'Hope/mmr_agent/audio_agent_config.ini',
   'DATA'),
  ('pyarmor_runtime_000000/__init__.py',
   '/Users/<USER>/Documents/VoidDevSpace/Project '
   'Hope/mmr_agent/dist_obj/pyarmor_runtime_000000/__init__.py',
   'DATA'),
  ('pyarmor_runtime_000000/__pycache__/__init__.cpython-313.pyc',
   '/Users/<USER>/Documents/VoidDevSpace/Project '
   'Hope/mmr_agent/dist_obj/pyarmor_runtime_000000/__pycache__/__init__.cpython-313.pyc',
   'DATA'),
  ('pyarmor_runtime_000000/pyarmor_runtime.so',
   '/Users/<USER>/Documents/VoidDevSpace/Project '
   'Hope/mmr_agent/dist_obj/pyarmor_runtime_000000/pyarmor_runtime.so',
   'DATA')],
 '3.13.3 (main, Apr  8 2025, 13:54:08) [Clang 16.0.0 (clang-1600.0.26.6)]',
 [('pyi_rth_inspect',
   '/Users/<USER>/Documents/VoidDevSpace/Project '
   'Hope/mmr_agent/venv/lib/python3.13/site-packages/PyInstaller/hooks/rthooks/pyi_rth_inspect.py',
   'PYSOURCE'),
  ('audio_agent',
   '/Users/<USER>/Documents/VoidDevSpace/Project '
   'Hope/mmr_agent/dist_obj/src/audio_agent.py',
   'PYSOURCE')],
 [('zipfile',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/zipfile/__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/zipfile/_path/__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/zipfile/_path/glob.py',
   'PYMODULE'),
  ('pathlib',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pathlib/__init__.py',
   'PYMODULE'),
  ('pathlib._local',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pathlib/_local.py',
   'PYMODULE'),
  ('urllib.parse',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/parse.py',
   'PYMODULE'),
  ('urllib',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/urllib/__init__.py',
   'PYMODULE'),
  ('ipaddress',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ipaddress.py',
   'PYMODULE'),
  ('glob',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/glob.py',
   'PYMODULE'),
  ('fnmatch',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/fnmatch.py',
   'PYMODULE'),
  ('pathlib._abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pathlib/_abc.py',
   'PYMODULE'),
  ('contextlib',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextlib.py',
   'PYMODULE'),
  ('argparse',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/argparse.py',
   'PYMODULE'),
  ('textwrap',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/textwrap.py',
   'PYMODULE'),
  ('copy',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/copy.py',
   'PYMODULE'),
  ('gettext',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/gettext.py',
   'PYMODULE'),
  ('py_compile',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/py_compile.py',
   'PYMODULE'),
  ('importlib.machinery',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/machinery.py',
   'PYMODULE'),
  ('importlib',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/__init__.py',
   'PYMODULE'),
  ('csv',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/csv.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_text.py',
   'PYMODULE'),
  ('email.message',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/message.py',
   'PYMODULE'),
  ('email.policy',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/quoprimime.py',
   'PYMODULE'),
  ('string',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/string.py',
   'PYMODULE'),
  ('email.headerregistry',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_header_value_parser.py',
   'PYMODULE'),
  ('email.iterators',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/iterators.py',
   'PYMODULE'),
  ('email.generator',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/generator.py',
   'PYMODULE'),
  ('random',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/random.py',
   'PYMODULE'),
  ('statistics',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/statistics.py',
   'PYMODULE'),
  ('decimal',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/contextvars.py',
   'PYMODULE'),
  ('fractions',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/fractions.py',
   'PYMODULE'),
  ('numbers',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/numbers.py',
   'PYMODULE'),
  ('hashlib',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/hashlib.py',
   'PYMODULE'),
  ('logging',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/logging/__init__.py',
   'PYMODULE'),
  ('pickle',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pickle.py',
   'PYMODULE'),
  ('pprint',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/pprint.py',
   'PYMODULE'),
  ('dataclasses',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/dataclasses.py',
   'PYMODULE'),
  ('_compat_pickle',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_compat_pickle.py',
   'PYMODULE'),
  ('bisect',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/bisect.py',
   'PYMODULE'),
  ('email._encoded_words',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_encoded_words.py',
   'PYMODULE'),
  ('base64',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/base64.py',
   'PYMODULE'),
  ('getopt',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/getopt.py',
   'PYMODULE'),
  ('email.charset',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/charset.py',
   'PYMODULE'),
  ('email.encoders',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_policybase.py',
   'PYMODULE'),
  ('email.header',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/header.py',
   'PYMODULE'),
  ('email.errors',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/errors.py',
   'PYMODULE'),
  ('email.utils',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/utils.py',
   'PYMODULE'),
  ('socket',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socket.py',
   'PYMODULE'),
  ('selectors',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/selectors.py',
   'PYMODULE'),
  ('email._parseaddr',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/calendar.py',
   'PYMODULE'),
  ('datetime',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/datetime.py',
   'PYMODULE'),
  ('_pydatetime',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_pydatetime.py',
   'PYMODULE'),
  ('_strptime',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_strptime.py',
   'PYMODULE'),
  ('quopri',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/quopri.py',
   'PYMODULE'),
  ('typing',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/typing.py',
   'PYMODULE'),
  ('importlib.abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/abc.py',
   'PYMODULE'),
  ('importlib.resources',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/__init__.py',
   'PYMODULE'),
  ('importlib.resources._functional',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_functional.py',
   'PYMODULE'),
  ('importlib.resources._common',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_adapters.py',
   'PYMODULE'),
  ('tempfile',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tempfile.py',
   'PYMODULE'),
  ('importlib._abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/metadata/_meta.py',
   'PYMODULE'),
  ('email',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/__init__.py',
   'PYMODULE'),
  ('email.parser',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/parser.py',
   'PYMODULE'),
  ('email.feedparser',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/email/feedparser.py',
   'PYMODULE'),
  ('json',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/__init__.py',
   'PYMODULE'),
  ('json.encoder',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/encoder.py',
   'PYMODULE'),
  ('json.decoder',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/decoder.py',
   'PYMODULE'),
  ('json.scanner',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/json/scanner.py',
   'PYMODULE'),
  ('__future__',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/__future__.py',
   'PYMODULE'),
  ('importlib.readers',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/resources/_itertools.py',
   'PYMODULE'),
  ('tokenize',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tokenize.py',
   'PYMODULE'),
  ('token',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/token.py',
   'PYMODULE'),
  ('lzma',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lzma.py',
   'PYMODULE'),
  ('_compression',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_compression.py',
   'PYMODULE'),
  ('bz2',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/bz2.py',
   'PYMODULE'),
  ('threading',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/threading.py',
   'PYMODULE'),
  ('_threading_local',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_threading_local.py',
   'PYMODULE'),
  ('struct',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/struct.py',
   'PYMODULE'),
  ('shutil',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/shutil.py',
   'PYMODULE'),
  ('tarfile',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tarfile.py',
   'PYMODULE'),
  ('gzip',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/gzip.py',
   'PYMODULE'),
  ('importlib.util',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/importlib/util.py',
   'PYMODULE'),
  ('inspect',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/inspect.py',
   'PYMODULE'),
  ('dis',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/dis.py',
   'PYMODULE'),
  ('opcode',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/opcode.py',
   'PYMODULE'),
  ('_opcode_metadata',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_opcode_metadata.py',
   'PYMODULE'),
  ('ast',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ast.py',
   'PYMODULE'),
  ('stringprep',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/stringprep.py',
   'PYMODULE'),
  ('_colorize',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_colorize.py',
   'PYMODULE'),
  ('tracemalloc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/tracemalloc.py',
   'PYMODULE'),
  ('subprocess',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/subprocess.py',
   'PYMODULE'),
  ('signal',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/signal.py',
   'PYMODULE'),
  ('_py_abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_py_abc.py',
   'PYMODULE'),
  ('pyarmor_runtime_000000',
   '/Users/<USER>/Documents/VoidDevSpace/Project '
   'Hope/mmr_agent/dist_obj/pyarmor_runtime_000000/__init__.py',
   'PYMODULE')],
 [('pyarmor_runtime_000000/pyarmor_runtime.so',
   '/Users/<USER>/Documents/VoidDevSpace/Project '
   'Hope/mmr_agent/dist_obj/pyarmor_runtime_000000/pyarmor_runtime.so',
   'BINARY'),
  ('Python.framework/Versions/3.13/Python',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/Python',
   'BINARY'),
  ('lib-dynload/unicodedata.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/unicodedata.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/math.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/math.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/grp.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/grp.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_csv.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_csv.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_statistics.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_statistics.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_contextvars.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_contextvars.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_decimal.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_decimal.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_pickle.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_pickle.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_hashlib.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_hashlib.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha3.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_sha3.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_blake2.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_blake2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_md5.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_md5.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha1.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_sha1.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_sha2.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_sha2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_random.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_random.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bisect.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_bisect.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/array.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/array.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/select.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/select.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_socket.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_socket.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_datetime.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_datetime.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_json.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_json.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/resource.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/resource.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_lzma.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_lzma.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_bz2.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_bz2.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/zlib.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/zlib.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_struct.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_struct.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/binascii.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/binascii.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_opcode.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_opcode.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_multibytecodec.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_multibytecodec.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_jp.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_jp.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_kr.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_kr.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_iso2022.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_iso2022.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_cn.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_cn.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_tw.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_tw.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_codecs_hk.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_codecs_hk.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_posixsubprocess.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_posixsubprocess.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/fcntl.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/fcntl.cpython-313-darwin.so',
   'EXTENSION'),
  ('lib-dynload/_heapq.cpython-313-darwin.so',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/lib-dynload/_heapq.cpython-313-darwin.so',
   'EXTENSION'),
  ('libmpdec.4.dylib',
   '/opt/homebrew/opt/mpdecimal/lib/libmpdec.4.dylib',
   'BINARY'),
  ('libcrypto.3.dylib',
   '/opt/homebrew/opt/openssl@3/lib/libcrypto.3.dylib',
   'BINARY'),
  ('liblzma.5.dylib', '/opt/homebrew/opt/xz/lib/liblzma.5.dylib', 'BINARY')],
 [],
 [],
 [('audio_agent_config.ini',
   '/Users/<USER>/Documents/VoidDevSpace/Project '
   'Hope/mmr_agent/audio_agent_config.ini',
   'DATA'),
  ('pyarmor_runtime_000000/__init__.py',
   '/Users/<USER>/Documents/VoidDevSpace/Project '
   'Hope/mmr_agent/dist_obj/pyarmor_runtime_000000/__init__.py',
   'DATA'),
  ('pyarmor_runtime_000000/__pycache__/__init__.cpython-313.pyc',
   '/Users/<USER>/Documents/VoidDevSpace/Project '
   'Hope/mmr_agent/dist_obj/pyarmor_runtime_000000/__pycache__/__init__.cpython-313.pyc',
   'DATA'),
  ('Python', 'Python.framework/Versions/3.13/Python', 'SYMLINK'),
  ('base_library.zip',
   '/Users/<USER>/Documents/VoidDevSpace/Project '
   'Hope/mmr_agent/build/audio_agent/base_library.zip',
   'DATA'),
  ('Python.framework/Python', 'Versions/Current/Python', 'SYMLINK'),
  ('Python.framework/Resources', 'Versions/Current/Resources', 'SYMLINK'),
  ('Python.framework/Versions/3.13/Resources/Info.plist',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/Resources/Info.plist',
   'DATA'),
  ('Python.framework/Versions/Current', '3.13', 'SYMLINK')],
 [('re._parser',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/re/_parser.py',
   'PYMODULE'),
  ('re._constants',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/re/_constants.py',
   'PYMODULE'),
  ('re._compiler',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/re/_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/re/_casefix.py',
   'PYMODULE'),
  ('re',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/re/__init__.py',
   'PYMODULE'),
  ('enum',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/enum.py',
   'PYMODULE'),
  ('keyword',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/keyword.py',
   'PYMODULE'),
  ('ntpath',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ntpath.py',
   'PYMODULE'),
  ('genericpath',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/genericpath.py',
   'PYMODULE'),
  ('_collections_abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_collections_abc.py',
   'PYMODULE'),
  ('operator',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/operator.py',
   'PYMODULE'),
  ('types',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/types.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/idna.py',
   'PYMODULE'),
  ('encodings.hz',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/aliases.py',
   'PYMODULE'),
  ('encodings',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/encodings/__init__.py',
   'PYMODULE'),
  ('traceback',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/traceback.py',
   'PYMODULE'),
  ('functools',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/functools.py',
   'PYMODULE'),
  ('copyreg',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/copyreg.py',
   'PYMODULE'),
  ('locale',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/locale.py',
   'PYMODULE'),
  ('reprlib',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/reprlib.py',
   'PYMODULE'),
  ('sre_compile',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/sre_compile.py',
   'PYMODULE'),
  ('io',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/io.py',
   'PYMODULE'),
  ('warnings',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/warnings.py',
   'PYMODULE'),
  ('sre_constants',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/sre_constants.py',
   'PYMODULE'),
  ('linecache',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/linecache.py',
   'PYMODULE'),
  ('sre_parse',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/sre_parse.py',
   'PYMODULE'),
  ('stat',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/stat.py',
   'PYMODULE'),
  ('weakref',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/weakref.py',
   'PYMODULE'),
  ('collections',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/collections/__init__.py',
   'PYMODULE'),
  ('os',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/os.py',
   'PYMODULE'),
  ('_weakrefset',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/_weakrefset.py',
   'PYMODULE'),
  ('posixpath',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/posixpath.py',
   'PYMODULE'),
  ('codecs',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/codecs.py',
   'PYMODULE'),
  ('heapq',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/heapq.py',
   'PYMODULE'),
  ('abc',
   '/opt/homebrew/Cellar/python@3.13/3.13.3/Frameworks/Python.framework/Versions/3.13/lib/python3.13/abc.py',
   'PYMODULE')])
