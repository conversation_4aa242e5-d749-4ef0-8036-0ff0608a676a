import React, { useState, useRef, useEffect } from 'react';
import { Copy, Check, Loader2 } from 'lucide-react';
import toast from 'react-hot-toast';
import { updateRecordingSummary } from '../services/audioService.js';
import './RichTextEditor.css';

const RichTextEditor = ({
  content = '',
  onContentChange,
  isLoading = false,
  placeholder = 'No summary available...',
  readOnly = false,
  className = '',
  sessionId,
  patientId,
  eventId,
  lastSavedAudio,
  API_BASE_URL = '',
  disableDBUpdateOnCopy = false, // <-- existing prop
  hideUntilContent = false, // <-- new prop to control visibility
  onEditingStateChange // <-- new prop to notify parent of editing state
}) => {
  const [isCopied, setIsCopied] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const editorRef = useRef(null);
  const contentRef = useRef(content);
  const lastCursorPosition = useRef(null);

  // Track whether content was ever received to prevent hiding after editing to empty
  const contentEverReceived = useRef(false);

  // Update the flag when we receive non-empty content
  useEffect(() => {
    if (content && content.trim() !== '') {
      contentEverReceived.current = true;
    }
  }, [content]);

  // Check if we should hide the component until content is available
  const hasContent = content && content.trim() !== '';
  // Only hide if we've never received content and there's no content and not loading
  const shouldHide = hideUntilContent && !hasContent && !isLoading && !contentEverReceived.current&& !isEditing;

  // Initialize content when component mounts
  useEffect(() => {
    if (editorRef.current) {
      if (content) {
        editorRef.current.innerHTML = formatContent(content);
      } else {
        editorRef.current.innerHTML = `<span style="color: #9ca3af; font-style: italic;">${placeholder}</span>`;
      }
    }
  }, []); // Only run on mount

  // Update content ref when content prop changes
  useEffect(() => {
    contentRef.current = content;
    if (editorRef.current && !isEditing) {
      // Only update innerHTML if the content is significantly different
      const currentHtml = editorRef.current.innerHTML;
      const newHtml = formatContent(content);
      if (currentHtml !== newHtml) {
        editorRef.current.innerHTML = newHtml;
      }
    }
  }, [content, isEditing]);

  // Debug: log API_BASE_URL to verify value
  // console.log('RichTextEditor API_BASE_URL:', API_BASE_URL);

  // Utility function to clean HTML content to plain text
  const htmlToCleanText = (htmlContent) => {
    return htmlContent
      // Replace <br> tags with newlines
      .replace(/<br\s*\/?>/gi, '\n')
      // Replace &nbsp; with regular spaces
      .replace(/&nbsp;/g, ' ')
      // Convert <strong> and <b> tags to **bold**
      .replace(/<(strong|b)[^>]*>(.*?)<\/(strong|b)>/gi, '**$2**')
      // Convert <em> and <i> tags to *italic*
      .replace(/<(em|i)[^>]*>(.*?)<\/(em|i)>/gi, '*$2*')
      // Remove any remaining HTML tags
      .replace(/<[^>]*>/g, '')
      // Decode common HTML entities
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
     
  };

  // Save cursor position
  const saveCursorPosition = () => {
    if (!editorRef.current) return;

    const selection = window.getSelection();
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);

      // Store both the container node and offset for more precise restoration
      lastCursorPosition.current = {
        container: range.endContainer,
        offset: range.endOffset,
        // Also store text-based position as fallback
        textPosition: getTextPosition(range.endContainer, range.endOffset)
      };
    }
  };

  // Get text position including line breaks
  const getTextPosition = (container, offset) => {
    if (!editorRef.current) return 0;

    const range = document.createRange();
    range.selectNodeContents(editorRef.current);
    range.setEnd(container, offset);

    // Count characters including line breaks
    let position = 0;
    const walker = document.createTreeWalker(
      range.cloneContents(),
      NodeFilter.SHOW_TEXT | NodeFilter.SHOW_ELEMENT,
      null,
      false
    );

    let node;
    while (node = walker.nextNode()) {
      if (node.nodeType === Node.TEXT_NODE) {
        position += node.textContent.length;
      } else if (node.nodeName === 'BR') {
        position += 1; // Count line breaks
      }
    }

    return position;
  };

  // Restore cursor position
  const restoreCursorPosition = () => {
    if (!editorRef.current || !lastCursorPosition.current) return;

    const selection = window.getSelection();
    const range = document.createRange();

    try {
      // First try to use the stored container and offset if they're still valid
      if (lastCursorPosition.current.container &&
          editorRef.current.contains(lastCursorPosition.current.container)) {
        range.setStart(lastCursorPosition.current.container, lastCursorPosition.current.offset);
        range.setEnd(lastCursorPosition.current.container, lastCursorPosition.current.offset);
        selection.removeAllRanges();
        selection.addRange(range);
        return;
      }
    } catch (e) {
      // If direct restoration fails, fall back to text position method
    }

    // Fallback: use text position to find the correct location
    const targetPosition = lastCursorPosition.current.textPosition || 0;
    let currentPosition = 0;
    let foundPosition = false;

    const walker = document.createTreeWalker(
      editorRef.current,
      NodeFilter.SHOW_TEXT | NodeFilter.SHOW_ELEMENT,
      null,
      false
    );

    let node;
    while (node = walker.nextNode() && !foundPosition) {
      if (node.nodeType === Node.TEXT_NODE) {
        const nodeLength = node.textContent.length;
        if (currentPosition + nodeLength >= targetPosition) {
          const offset = Math.min(targetPosition - currentPosition, nodeLength);
          range.setStart(node, offset);
          range.setEnd(node, offset);
          foundPosition = true;
        }
        currentPosition += nodeLength;
      } else if (node.nodeName === 'BR') {
        if (currentPosition >= targetPosition) {
          // Position cursor after the BR element
          range.setStartAfter(node);
          range.setEndAfter(node);
          foundPosition = true;
        }
        currentPosition += 1;
      }
    }

    if (foundPosition) {
      selection.removeAllRanges();
      selection.addRange(range);
    }
  };

  // Format content for display (preserve line breaks and basic formatting)
  const formatContent = (text) => {
    if (!text) return '';
    return text
      .replace(/\n/g, '<br>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>');
  };

  // Handle content changes in the editor
  const handleContentChange = () => {
    if (editorRef.current && onContentChange) {
      const htmlContent = editorRef.current.innerHTML;
      const textContent = htmlToCleanText(htmlContent);

      contentRef.current = textContent;
      onContentChange(textContent);
    }
  };

  // Handle copy to clipboard (plain text only)
  const handleCopy = async () => {
    try {
      let plainText = '';

      if (editorRef.current) {
        // Get the HTML content from the editor
        const htmlContent = editorRef.current.innerHTML;

        // Convert HTML to clean plain text
        plainText = htmlToCleanText(htmlContent);

        // Debug logging
        console.log('Original HTML:', htmlContent);
        console.log('Plain text:', plainText);
      } else {
        // Fallback to contentRef if editor is not available
        plainText = contentRef.current || '';
      }

      // Try multiple copy strategies for maximum compatibility
      let copySuccess = false;

      // Strategy 1: Modern Clipboard API (plain text)
      if (navigator.clipboard && navigator.clipboard.writeText) {
        try {
          await navigator.clipboard.writeText(plainText);
          copySuccess = true;
          console.log('Plain text copy successful');
        } catch (plainTextError) {
          console.warn('Plain text copy failed:', plainTextError);
        }
      }

      // Strategy 2: Legacy execCommand method
      if (!copySuccess) {
        try {
          const textArea = document.createElement('textarea');
          textArea.value = plainText;
          textArea.style.position = 'fixed';
          textArea.style.left = '-999999px';
          textArea.style.top = '-999999px';
          document.body.appendChild(textArea);
          textArea.focus();
          textArea.select();

          const success = document.execCommand('copy');
          document.body.removeChild(textArea);

          if (success) {
            copySuccess = true;
            console.log('Legacy copy successful');
          }
        } catch (legacyError) {
          console.warn('Legacy copy failed:', legacyError);
        }
      }

      if (copySuccess) {
        setIsCopied(true);
        console.log(disableDBUpdateOnCopy,'disableDBUpdateOnCopy',!disableDBUpdateOnCopy);
        
        toast.success('Copied to clipboard!', {
          duration: 2000,
          position: 'top-right',
        });
        setTimeout(() => setIsCopied(false), 2000);

        // Only update DB if not disabled
        if (!disableDBUpdateOnCopy && sessionId) {
          console.log('entered in copy update');
          
          fetch(`${API_BASE_URL}/api/update_summary_by_session/${sessionId}/${eventId}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ summary: plainText })
          })
            .then(res => res.json())
            .then(data => {
              if (!data.success) {
                toast.error('Failed to update summary in DB', { duration: 3000, position: 'top-right' });
              }
            })
            .catch(() => {
              toast.error('Failed to update summary in DB', { duration: 3000, position: 'top-right' });
            });
        }
      } else {
        throw new Error('All copy methods failed');
      }
    } catch (err) {
      console.error('Failed to copy text: ', err);
      toast.error('Failed to copy to clipboard', {
        duration: 3000,
        position: 'top-right',
      });
    }
  };

  // Handle focus and blur for editing state
  const handleFocus = () => {
    if (!readOnly) {
      setIsEditing(true);
      // Notify parent component of editing state change
      if (onEditingStateChange) {
        onEditingStateChange(true);
      }

      // Clear placeholder if present
      if (editorRef.current && editorRef.current.innerHTML.includes(placeholder)) {
        editorRef.current.innerHTML = '';
        // Don't restore cursor position if we cleared placeholder
        lastCursorPosition.current = null;
      } else {
        // Restore cursor position after a brief delay to ensure DOM is ready
        setTimeout(() => {
          restoreCursorPosition();
        }, 0);
      }
    }
  };

  const handleBlur = () => {
    // Save cursor position before any processing
    saveCursorPosition();

    setIsEditing(false);
    // Notify parent component of editing state change
    if (onEditingStateChange) {
      onEditingStateChange(false);
    }

    handleContentChange();

    // Restore placeholder if content is empty
    if (editorRef.current && (!editorRef.current.textContent || editorRef.current.textContent.trim() === '')) {
      editorRef.current.innerHTML = `<span style="color: #9ca3af; font-style: italic;">${placeholder}</span>`;
    }
  };

  // Handle keyboard shortcuts
  const handleKeyDown = (e) => {
    if (readOnly) return;

        // Handle Enter key for new lines
        if (e.key === 'Enter' && !e.ctrlKey) {
          e.preventDefault();

          // Get current selection
          const selection = window.getSelection();
          if (selection.rangeCount > 0) {
            const range = selection.getRangeAt(0);

            // Create line break element
            const br = document.createElement('br');

            // Insert the line break
            range.deleteContents();
            range.insertNode(br);

            // Position cursor after the line break
            range.setStartAfter(br);
            range.setEndAfter(br);
            selection.removeAllRanges();
            selection.addRange(range);

            // Save the new cursor position immediately
            saveCursorPosition();
          }
          return;
        }

    // Ctrl+B for bold
    if (e.ctrlKey && e.key === 'b') {
      e.preventDefault();
      document.execCommand('bold');
    }
    // Ctrl+I for italic
    if (e.ctrlKey && e.key === 'i') {
      e.preventDefault();
      document.execCommand('italic');
    }
    // Ctrl+Enter to finish editing
    if (e.ctrlKey && e.key === 'Enter') {
      e.preventDefault();
      editorRef.current?.blur();
    }
  };

  // Handle key up events to save cursor position after navigation keys
  const handleKeyUp = (e) => {
    if (readOnly) return;

    // Save cursor position after navigation keys or backspace/delete
    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'Backspace', 'Delete', 'Home', 'End'].includes(e.key)) {
      saveCursorPosition();
    }
  };

  // If hideUntilContent is true and there's no content and not loading, don't render anything
  if (shouldHide) {
    return null;
  }

  return (
    <div className={`rich-text-editor ${className}`} style={{
      position: 'relative',
      border: '1px solid #e2e8f0',
      borderRadius: '8px',
      backgroundColor: '#ffffff',
      minHeight: '120px',
      fontFamily: 'inherit'
    }}>
      {/* Header with copy button */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        padding: '8px 12px',
        borderBottom: '1px solid #e2e8f0',
        backgroundColor: '#f8f9fa'
      }}>
        <span style={{
          fontSize: '14px',
          fontWeight: '600',
          color: '#374151'
        }}>
          AI Summary
        </span>
        
        <button
          onClick={handleCopy}
          disabled={isLoading || !contentRef.current || contentRef.current.trim() === ''}
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '4px',
            padding: '4px 8px',
            border: 'none',
            borderRadius: '4px',
            backgroundColor: isCopied ? '#10b981' : '#6b7280',
            color: 'white',
            fontSize: '12px',
            cursor: isLoading || !contentRef.current || contentRef.current.trim() === '' ? 'not-allowed' : 'pointer',
            opacity: isLoading || !contentRef.current || contentRef.current.trim() === '' ? 0.5 : 1,
            transition: 'all 0.2s ease'
          }}
          onMouseOver={(e) => {
            if (!isLoading && contentRef.current && contentRef.current.trim() !== '') {
              e.target.style.backgroundColor = isCopied ? '#059669' : '#4b5563';
            }
          }}
          onMouseOut={(e) => {
            if (!isLoading && contentRef.current && contentRef.current.trim() !== '') {
              e.target.style.backgroundColor = isCopied ? '#10b981' : '#6b7280';
            }
          }}
        >
          {isCopied ? <Check size={12} /> : <Copy size={12} />}
          {isCopied ? 'Copied!' : 'Copy'}
        </button>
      </div>

      {/* Content area */}
      <div style={{
        position: 'relative',
        padding: '16px',
        minHeight: '80px'
      }}>
        {isLoading ? (
          <div style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '80px',
            color: '#6b7280'
          }}>
            <Loader2 size={24} className="animate-spin" style={{
              animation: 'spin 1s linear infinite',
              '@keyframes spin': {
                '0%': { transform: 'rotate(0deg)' },
                '100%': { transform: 'rotate(360deg)' }
              }
            }} />
            <span style={{ marginTop: '8px', fontSize: '14px' }}>
              Generating summary...
            </span>
          </div>
        ) : (
          <div
            ref={editorRef}
            contentEditable={!readOnly}
            suppressContentEditableWarning={true}
            onFocus={handleFocus}
            onBlur={handleBlur}
            onKeyDown={handleKeyDown}
            onKeyUp={handleKeyUp}
            onInput={handleContentChange}
            style={{
              outline: 'none',
              minHeight: '60px',
              lineHeight: '1.6',
              fontSize: '14px',
              color: '#374151',
              cursor: readOnly ? 'default' : 'text',
              padding: readOnly ? '0' : '8px',
              border: readOnly ? 'none' : isEditing ? '2px solid #3b82f6' : '2px solid transparent',
              borderRadius: '4px',
              transition: 'border-color 0.2s ease',
              textAlign: 'left'
            }}
          />
        )}
        
        {/* Editing hint */}
        {!readOnly && !isLoading && content && (
          <div style={{
            position: 'absolute',
            bottom: '4px',
            right: '8px',
            fontSize: '11px',
            color: '#9ca3af',
            fontStyle: 'italic'
          }}>
            {isEditing ? 'Ctrl+Enter to finish editing' : 'Click to edit'}
          </div>
        )}
      </div>
    </div>
  );
};

export default RichTextEditor;
