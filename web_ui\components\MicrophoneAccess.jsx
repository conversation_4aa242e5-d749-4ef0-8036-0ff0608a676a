import React, { useState, useEffect } from 'react';
import './MicrophoneAccess.css';

const MicrophoneAccess = ({ onMicrophonesDetected }) => {
  const [devices, setDevices] = useState([]);
  const [permissionGranted, setPermissionGranted] = useState(false);
  const [error, setError] = useState(null);

  const requestMicrophoneAccess = async () => {
    try {
      await navigator.mediaDevices.getUserMedia({ audio: true });
      setPermissionGranted(true);
      
      const deviceList = await navigator.mediaDevices.enumerateDevices();
      
      const microphones = deviceList
        .filter(device => device.kind === 'audioinput')
        .map((device, index) => ({
          id: device.deviceId,
          name: device.label || `Microphone ${index + 1}`,
          groupId: device.groupId
        }));
      
      setDevices(microphones);
      
      if (onMicrophonesDetected) {
        onMicrophonesDetected(microphones);
      }
      
      navigator.mediaDevices.addEventListener('devicechange', handleDeviceChange);
      
      return microphones;
    } catch (err) {
      console.error('Error accessing microphones:', err);
      setError(err.message || 'Failed to access microphones');
      return [];
    }
  };

  const handleDeviceChange = async () => {
    try {
      const deviceList = await navigator.mediaDevices.enumerateDevices();
      const microphones = deviceList
        .filter(device => device.kind === 'audioinput')
        .map((device, index) => ({
          id: device.deviceId,
          name: device.label || `Microphone ${index + 1}`,
          groupId: device.groupId
        }));
      
      setDevices(microphones);
      
      if (onMicrophonesDetected) {
        onMicrophonesDetected(microphones);
      }
    } catch (err) {
      console.error('Error handling device change:', err);
    }
  };

  useEffect(() => {
    return () => {
      navigator.mediaDevices.removeEventListener('devicechange', handleDeviceChange);
    };
  }, []);

  return (
    <div className="microphone-access">
      {!permissionGranted && (
        <div className="permission-request">
          <h3>Microphone Access Required</h3>
          <p>This application needs access to your microphones to function properly.</p>
          <button 
            className="request-access-button"
            onClick={requestMicrophoneAccess}
          >
            Allow Microphone Access
          </button>
          {error && <p className="error-message">{error}</p>}
        </div>
      )}
      
      {permissionGranted && devices.length === 0 && (
        <div className="no-devices">
          <p>No microphones detected. Please connect a microphone and try again.</p>
          <button 
            className="refresh-button"
            onClick={requestMicrophoneAccess}
          >
            Refresh Devices
          </button>
        </div>
      )}
      
      {permissionGranted && devices.length > 0 && (
        <div className="devices-detected">
          <p>{devices.length} microphone(s) detected and ready to use.</p>
        </div>
      )}
    </div>
  );
};

export default MicrophoneAccess;
