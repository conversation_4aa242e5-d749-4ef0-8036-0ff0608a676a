[server]
# The allowed origin for CORS (web_ui server URL, e.g. https://webui.local or http://localhost:5173)
allowed_origin = http://localhost:5173

# The port to run the audio agent on
port = 5001

# The host to bind the agent (0.0.0.0 for all interfaces, 127.0.0.1 for local only)
host = 0.0.0.0

# The driver name to filter the audio devices
driver_name = "WDM Driver Audio"

# Enable OpenTelemetry tracing (true/false)
enable_opentelemetry = false