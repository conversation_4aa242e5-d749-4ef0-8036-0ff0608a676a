from pydantic import BaseModel, field_validator

class NoiseGateConfig(BaseModel):
    threshold: float

    @field_validator("threshold")
    @classmethod
    def threshold_must_be_valid(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError("Threshold must be between 0.0 and 1.0")
        return v

class CompressorConfig(BaseModel):
    threshold: float
    ratio: float

    @field_validator("threshold")
    @classmethod
    def threshold_must_be_valid(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError("Threshold must be between 0.0 and 1.0")
        return v

    @field_validator("ratio")
    @classmethod
    def ratio_must_be_valid(cls, v):
        if not 1.0 <= v <= 20.0:
            raise ValueError("Ratio must be between 1.0 and 20.0")
        return v

class LimiterConfig(BaseModel):
    threshold: float

    @field_validator("threshold")
    @classmethod
    def threshold_must_be_valid(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError("Threshold must be between 0.0 and 1.0")
        return v

class EQConfig(BaseModel):
    lowCutFreq: int
    highCutFreq: int

    @field_validator("lowCutFreq")
    @classmethod
    def lowCutFreq_must_be_valid(cls, v):
        if not 0 <= v <= 20000:
            raise ValueError("Low Cut Frequency must be between 0 and 20000 Hz")
        return v

    @field_validator("highCutFreq")
    @classmethod
    def highCutFreq_must_be_valid(cls, v):
        if not 0 <= v <= 20000:
            raise ValueError("High Cut Frequency must be between 0 and 20000 Hz")
        return v

class DSPConfig(BaseModel):
    noiseGate: NoiseGateConfig | None = None
    compressor: CompressorConfig | None = None
    limiter: LimiterConfig | None = None
    eq: EQConfig | None = None
    lowCutFreq: int | None = None

    @field_validator("lowCutFreq")
    @classmethod
    def lowCutFreq_must_be_valid(cls, v):
        if v is not None and not 0 <= v <= 20000:
            raise ValueError("Low Cut Frequency must be between 0 and 20000 Hz")
        return v

class MicRole(BaseModel):
    device: int
    role: str
    dsp: DSPConfig | None = None

class StartRecordingRequest(BaseModel):
    mics: list[MicRole]