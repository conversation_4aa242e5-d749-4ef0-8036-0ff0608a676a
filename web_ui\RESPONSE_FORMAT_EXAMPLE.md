# Enhanced API Response Format

## Store Patient Data Response

The `/api/storePatientData` endpoint now returns comprehensive information about case notes summary generation along with the standard patient storage status.

### Response Structure

```json
{
  "success": true,
  "message": "Patient data stored successfully in database",
  "patientRegNo": "API-PATIENT-002",
  "patientName": "<PERSON> (From API)",
  "caseNotesSummary": {
    "status": "generated",
    "encountersFound": 2,
    "summary": "## Medical History Summary\n\n### Key Findings:\n- Patient presented with COVID-19 pneumonia in June 2025\n- Developed post-COVID cardiac symptoms requiring cardiology follow-up\n- Treatment progression from supportive care to cardiac monitoring\n\n### Treatment Timeline:\n**June 30, 2025 - COVID-19 Pneumonia:**\n- Presented with persistent cough, fever, and breathing difficulties\n- Physical exam revealed bilateral lower lobe crackles\n- Treated with oxygen therapy, azithromycin, and dexamethasone\n- Advised home isolation with 3-day follow-up\n\n**July 10, 2025 - Post-COVID Cardiac Follow-up:**\n- Complete resolution of respiratory symptoms\n- New complaints of chest discomfort and palpitations during activity\n- ECG showed occasional premature beats\n- Diagnosed with possible post-viral myocarditis\n- Started on low-dose beta-blocker therapy\n- Ordered echocardiogram for further evaluation\n\n### Current Status:\n- Respiratory recovery complete\n- Cardiac monitoring ongoing\n- Gradual return to physical activity advised",
    "statusDescriptions": {
      "generated": "Case notes summary successfully generated and saved",
      "no_encounters": "No encounters with case notes found for this patient",
      "generation_failed": "Summary generation failed or returned empty result",
      "error": "Error occurred during summary generation",
      "not_generated": "Summary generation was not attempted"
    }
  }
}
```

### Status Values

| Status | Description |
|--------|-------------|
| `generated` | Summary successfully created and saved to database |
| `no_encounters` | Patient has no encounters with case notes |
| `generation_failed` | AI summary generation failed or returned empty |
| `error` | Error occurred during the summary generation process |
| `not_generated` | Summary generation was not attempted |

### Response Fields

- **success**: Boolean indicating overall operation success
- **message**: Human-readable status message
- **patientRegNo**: Patient registration number
- **patientName**: Patient's full name
- **caseNotesSummary**: Object containing summary generation details
  - **status**: Current status of summary generation
  - **encountersFound**: Number of encounters with case notes found
  - **summary**: Generated AI summary (null if not generated)
  - **statusDescriptions**: Explanations for each possible status

### Example Usage

```javascript
// Frontend handling of the enhanced response
const response = await fetch('/api/storePatientData', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(patientData)
});

const result = await response.json();

if (result.success) {
  console.log(`Patient ${result.patientName} stored successfully`);
  
  const summaryInfo = result.caseNotesSummary;
  
  switch (summaryInfo.status) {
    case 'generated':
      console.log('✅ Case notes summary generated');
      console.log('Summary:', summaryInfo.summary);
      break;
      
    case 'no_encounters':
      console.log('ℹ️ No previous encounters found');
      break;
      
    case 'error':
      console.log('⚠️ Summary generation failed');
      break;
  }
  
  console.log(`Found ${summaryInfo.encountersFound} encounters`);
}
```

### Benefits

1. **Immediate Feedback**: Know instantly if summary was generated
2. **Detailed Status**: Understand why summary generation succeeded or failed
3. **Complete Information**: Get patient data and summary in single response
4. **Error Transparency**: Clear indication of any issues during processing
5. **Frontend Integration**: Easy to handle different scenarios in UI
