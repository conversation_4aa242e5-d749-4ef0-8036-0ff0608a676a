import React, { useState, useEffect, useRef, forwardRef, useImperativeHandle } from "react";
import { API_BASE_URL, USE_MOCK_DATA, EXTERNAL_API_URL, VITE_EMR_BEARER_TOKEN } from "./src/constants";
import toast, { Toaster } from "react-hot-toast";
import axios from "axios";
import { transcriptLogger } from "./utils/transcriptLogger";
import ConversationPanel from "./components/ConversationPanel.jsx";
import AIFeedbackPanel from "./components/AIFeedbackPanel.jsx";
import ExternalAudioService from "./components/ExternalAudioService.jsx";
import EnhancedAudioPlayer from "./components/EnhancedAudioPlayer.jsx";
import MultiMicAudioPlayer from "./components/MultiMicAudioPlayer.jsx";
import Sidebar from "./components/Sidebar.jsx";
import "./App.css";
import { FileAudio } from "lucide-react";
import { isAdmin } from "./utils/roleUtils";
import {
  fetchMicsFromAgent as fetchMicsFromAgentService,
  handleStart as handleStartService,
  handlePause as handlePauseService,
  handleResume as handleResumeService,
  handleReportIssue as handleReportIssueService,
  handleConfirmClose as handleConfirmCloseService,
  handleStop as handleStopService,
  generateSummaryFromLastSaved as generateSummaryFromLastSavedService,
  handleApplyDSP as handleApplyDSPService,
  handleGetSummary as handleGetSummaryService,
  uploadAudioFiles,
  pollJobStatus,
  getJobResult,
} from "./services/audioService.js";
import authService from "./services/authService.js";
import VolumeMeter from "./components/VolumeMeter.jsx";

// --- Test Environment Detection ---
const isTestEnv = typeof window !== 'undefined' &&
  (window.location.hostname === 'https://arcaai-staging.bcmch.org' ||
    window.location.hostname === 'localhost' ||
    window.location.hostname === '127.0.0.1');

const ClinicalUIPopUp = forwardRef(({
  onClose,
  sessionId: propSessionId,
  eventId: propEventId,
  patientId: propPatientId,
  role: propRole,
}, ref) => {
  // --- Upload Modal State ---
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [uploadFiles, setUploadFiles] = useState([]); // files selected in modal
  const [uploadLanguage, setUploadLanguage] = useState("ml-IN");
  const [uploadLoading, setUploadLoading] = useState(false); // <-- Fix: add uploadLoading state
  const [showUploadCancelConfirmation, setShowUploadCancelConfirmation] =
    useState(false);
  const [uploadAbortController, setUploadAbortController] = useState(null);
  // Store combined summary for multi-file upload
  const [combinedSummary, setCombinedSummary] = useState("");
  // Store uploaded file info for display in AI feedback panel
  const [uploadedFileInfo, setUploadedFileInfo] = useState(null);

  // --- Report Issue Modal State ---
  const [showReportModal, setShowReportModal] = useState(false);
  const [reportTitle, setReportTitle] = useState("");
  const [reportDescription, setReportDescription] = useState("");
  const [reportFiles, setReportFiles] = useState([]);
  const [reportLoading, setReportLoading] = useState(false);
  const reportTitleInputRef = useRef(null);
  const [disableDBUpdateOnCopy, setDisableUpdateOnCopy] = useState(false)

  // --- Encounter Type State ---
  const [isNewVisit, setIsNewVisit] = useState(false); // Default to false (follow-up visit)

  // --- Patient Data Sync State ---
  const [patientData, setPatientData] = useState(null);
  const [patientDataLoading, setPatientDataLoading] = useState(false);
  const [patientDataError, setPatientDataError] = useState(null);
  const [patientDataSyncStatus, setPatientDataSyncStatus] = useState('idle'); // 'idle', 'loading', 'success', 'error'

  // --- Dynamic Patient Info from API Response ---
  const [currentPatientId, setCurrentPatientId] = useState("");
  const [currentPatientName, setCurrentPatientName] = useState("");

  // --- Close Modal State ---
  const [showCloseConfirmation, setShowCloseConfirmation] = useState(false);

  // --- Enhanced Audio Player State ---
  const [showEnhancedPlayer, setShowEnhancedPlayer] = useState(false);

  // Modal state for download list
  const [showDownloadModal, setShowDownloadModal] = useState(false);
  const [recordingFiles, setRecordingFiles] = useState([]);
  const [loadingRecordings, setLoadingRecordings] = useState(false);

  // Upload handler for modal
  const handleUpload = async () => {
    if (!uploadFiles.length) {
      console.log("[UPLOAD] No files selected for upload.");
      return;
    }
    setUploadLoading(true);
    setDisableUpdateOnCopy(true)
    window.opener?.postMessage({
      type: 'recordingstatus',
      value: true
    }, '*');
    toast.loading("Uploading audio for transcription...", {
      id: "upload-audio",
    });

    // Create AbortController for cancellation
    const abortController = new AbortController();
    setUploadAbortController(abortController);

    const formData = new FormData();
    uploadFiles.forEach((file) => formData.append("audio", file));
    formData.append("language", uploadLanguage);
    console.log(
      "[UPLOAD] Starting upload. Files:",
      uploadFiles.map((f) => f.name),
      "Language:",
      uploadLanguage
    );
    try {
      const data = await uploadAudioFiles(uploadFiles, uploadLanguage, abortController);
      if (data.success && data.jobId) {
        // Long audio: poll for job status
        console.log(
          "[UPLOAD] Long audio detected, polling for jobId:",
          data.jobId
        );
        toast.loading(
          "Processing long audio... (this may take a few minutes)",
          { id: "upload-audio" }
        );
        let status = "processing",
          error = null;
        let pollCount = 0,
          maxPolls = 120; // up to 20 minutes
        while (pollCount < maxPolls) {
          await new Promise((r) => setTimeout(r, 10000));
          const statusData = await pollJobStatus(data.jobId);
          console.log(
            `[UPLOAD] Poll #${pollCount + 1} job status:`,
            statusData
          );
          if (!statusData.success) {
            error = statusData.error || "Unknown error";
            break;
          }
          status = statusData.status;
          if (status === "done") {
            break;
          } else if (status === "error") {
            error = statusData.error || "Unknown error";
            break;
          }
          pollCount++;
        }
        if (status === "done") {
          // Fetch results
          const resultData = await getJobResult(data.jobId);
          if (resultData.success) {
            toast.success("Upload and transcription complete!", {
              id: "upload-audio",
            });
            if (resultData.combinedSummary) {
              console.log(
                "[UPLOAD] Received combinedSummary:",
                resultData.combinedSummary
              );
              setCombinedSummary(resultData.combinedSummary);
              setSummary(resultData.combinedSummary);
            } else {
              console.log(
                "[UPLOAD] No combinedSummary in result, using empty summary."
              );
              setSummary("");
              setCombinedSummary("");
            }

            // Store uploaded file info for display in AI feedback panel
            const fileInfos = uploadFiles.map(file => ({
              name: file.name,
              size: file.size
            }));
            console.log("[UPLOAD] Final file infos:", fileInfos);
            const languageMap = {
              "ml-IN": "Malayalam",
              "en-US": "English",
              "hi-IN": "Hindi",
              "ta-IN": "Tamil"
            };
            setUploadedFileInfo({
              files: fileInfos,
              language: languageMap[uploadLanguage] || uploadLanguage,
              uploadTimestamp: new Date().toISOString()
            });

            setUploadFiles([]);
            setShowUploadModal(false);
            setUploadLoading(false); // Turn off loading spinner when long audio processing is complete
          } else {
            setUploadLoading(false);
            console.error(
              "[UPLOAD] Failed to fetch job result:",
              resultData.message
            );
            toast.error(
              "Failed to fetch job result: " +
              (resultData.message || "Unknown error"),
              { id: "upload-audio" }
            );
            setUploadLoading(false); // Turn off loading spinner on error
          }
        } else {
          setUploadLoading(false);
          console.error("[UPLOAD] Upload failed or timed out:", error);
          toast.error(
            "Upload failed: " +
            (error || "Timed out waiting for transcription"),
            { id: "upload-audio" }
          );
          setUploadLoading(false); // Turn off loading spinner on timeout/error
        }
      } else if (data.success) {
        // Short audio: immediate result
        console.log("[UPLOAD] Short audio, immediate result.");
        toast.success("Upload and transcription complete!", {
          id: "upload-audio",
        });
        if (data.combinedSummary) {
          console.log(
            "[UPLOAD] Received combinedSummary:",
            data.combinedSummary
          );
          setCombinedSummary(data.combinedSummary);
          setSummary(data.combinedSummary);
        } else if (data.results && data.results.length > 0) {
          console.log(
            "[UPLOAD] No combinedSummary, using first transcript:",
            data.results[0]
          );
          setSummary(
            data.results[0].rawTranscript ||
            data.results[0].medicalTranscript ||
            ""
          );
          setCombinedSummary("");
        } else {
          console.log("[UPLOAD] No summary or results returned.");
          setSummary("");
          setCombinedSummary("");
        }

        // Store uploaded file info for display in AI feedback panel
        const fileInfos = uploadFiles.map(file => ({
          name: file.name,
          size: file.size
        }));
        console.log("[UPLOAD] Final file infos:", fileInfos);
        const languageMap = {
          "ml-IN": "Malayalam",
          "en-US": "English",
          "hi-IN": "Hindi",
          "ta-IN": "Tamil"
        };
        setUploadedFileInfo({
          files: fileInfos,
          language: languageMap[uploadLanguage] || uploadLanguage,
          uploadTimestamp: new Date().toISOString()
        });

        setUploadFiles([]);
        setShowUploadModal(false);
        setUploadLoading(false); // Turn off loading spinner when short audio processing is complete
      } else {
        setUploadLoading(false);
        console.error("[UPLOAD] Upload failed:", data.message);
        toast.error("Upload failed: " + (data.message || "Unknown error"), {
          id: "upload-audio",
        });
        setUploadLoading(false); // Turn off loading spinner on upload failure
      }
    } catch (err) {
      setUploadLoading(false);
      console.error("[UPLOAD] Upload failed:", err);
      if (err.name === "AbortError") {
        toast.error("Upload cancelled", { id: "upload-audio" });
        console.log("[UPLOAD] Upload was cancelled by user");
      } else {
        toast.error("Upload failed: " + err.message, { id: "upload-audio" });
      }
      setUploadLoading(false); // Turn off loading spinner on exception
    } finally {

      setUploadAbortController(null);
    }
  };

  // Handle upload cancellation
  const handleUploadCancel = () => {
    if (uploadLoading) {
      setShowUploadCancelConfirmation(true);
    } else {
      setShowUploadModal(false);
    }
  };

  // Confirm upload cancellation
  const handleConfirmUploadCancel = () => {
    if (uploadAbortController) {
      uploadAbortController.abort();
      console.log("[UPLOAD] Upload cancelled by user");
    }
    setShowUploadCancelConfirmation(false);
    setShowUploadModal(false);
    setUploadFiles([]);
    setUploadLoading(false);
    setUploadAbortController(null);
    setUploadedFileInfo(null); // Clear uploaded file info on cancel
    toast.dismiss("upload-audio");
  };



  // Fetch available recordings for this session/event
  const fetchRecordingFiles = async () => {
    setLoadingRecordings(true);
    try {
      const url = `${API_BASE_URL}/api/list_recordings?session=${encodeURIComponent(
        sessionId || "unknown"
      )}${eventId ? `&event=${encodeURIComponent(eventId)}` : ""}`;
      const resp = await fetch(url);
      const data = await resp.json();
      if (data.success) {
        setRecordingFiles(data.files);
      } else {
        setRecordingFiles([]);
      }
    } catch (err) {
      setRecordingFiles([]);
    }
    setLoadingRecordings(false);
  };

  // --- Admin Test Environment Button ---
  const handleOpenTestEnv = () => {
    // Open the staging environment in a new tab
    window.open(
      'https://arcaai-staging.bcmch.org',
      '_blank',
      'noopener,noreferrer'
    );
  };
  // Track last saved audio filename for download
  const [lastSavedAudio, setLastSavedAudio] = useState(null);
  // Save audioChunks as per-mic WAV files to backend
  // --- Consultant/Department fields from patientData (if available) ---
  const getConsultantAndDepartment = () => {
    // Try to extract from patientData structure
    const consultant = patientData?.data?.consultant || {};
    const department = patientData?.data?.department || {};
    return {
      consultantId: consultant.id || consultant.consultantId || '',
      consultantName: consultant.name || '',
      departmentId: consultant.departmentId || department.departmentId || '',
      departmentName: consultant.departmentName || '',
    };
  };

  const handleSaveAudio = async ({ sessionId, patientId, eventId }) => {
    console.log('[handleSaveAudio] Called with:', { sessionId, patientId, eventId, currentPatientId });

    const { consultantId, consultantName, departmentId, departmentName } = getConsultantAndDepartment();

    const savePromise = new Promise(async (resolve, reject) => {
      if (!audioChunks || audioChunks.length === 0) {
        setStatusMsg("No audio to save.");
        console.warn('[handleSaveAudio] No audioChunks to save.');
        reject("No audio to save");
        return;
      }

      // Group chunks by deviceId
      const chunksByMic = {};
      audioChunks.forEach((c) => {
        const mic = c.deviceId ?? "unknown";
        if (!chunksByMic[mic]) chunksByMic[mic] = [];
        chunksByMic[mic].push(c.chunk);
      });
      console.log('[handleSaveAudio] Grouped audio chunks by mic:', Object.keys(chunksByMic));

      setStatusMsg("Uploading audio...");
      const savedFiles = [];
      for (const mic of Object.keys(chunksByMic)) {
        const micChunks = chunksByMic[mic];
        if (!micChunks.length) {
          console.warn(`[handleSaveAudio] No chunks for mic ${mic}, skipping.`);
          continue;
        }
        // Concatenate WAV chunks for this mic
        const headerSize = 44;
        let totalLength = micChunks.reduce(
          (acc, c, i) =>
            acc + (i === 0 ? c.byteLength : c.byteLength - headerSize),
          0
        );
        const result = new Uint8Array(totalLength);
        let offset = 0;
        // Copy the first chunk entirely (including header)
        const firstChunk = new Uint8Array(micChunks[0]);
        result.set(firstChunk, offset);
        offset += firstChunk.length;
        // For the rest, skip the header
        for (let i = 1; i < micChunks.length; i++) {
          const chunk = new Uint8Array(micChunks[i]);
          result.set(chunk.slice(headerSize), offset);
          offset += chunk.length - headerSize;
        }
        // Update the data size in the WAV header
        const dataSize = totalLength - headerSize;
        const dataView = new DataView(result.buffer);
        dataView.setUint32(4, totalLength - 8, true);
        dataView.setUint32(40, dataSize, true);

        // Prepare form data
        const formData = new FormData();
        const filename = `session_${sessionId || "unknown"}_${Date.now()}_mic${mic}.wav`;
        formData.append(
          "audio",
          new Blob([result.buffer], { type: "audio/wav" }),
          filename
        );
        formData.append("mic", mic);
        formData.append("sessionId", sessionId);
        formData.append("eventId", eventId);
        formData.append("patientId", currentPatientId);
        // Add consultant/department fields
        formData.append("consultantId", consultantId);
        formData.append("consultantName", consultantName);
        formData.append("departmentId", departmentId);
        formData.append("departmentName", departmentName);

        console.log(`[handleSaveAudio] Uploading audio for mic: ${mic}, filename: ${filename}`);

        try {
          const resp = await fetch(`${API_BASE_URL}/api/save_wav`, {
            method: "POST",
            body: formData,
          });
          const data = await resp.json();
          const contextResp = await fetch(`${API_BASE_URL}/api/patient_context/${currentPatientId}`);
          const contextData = await contextResp.json();
          // Log context results
          console.log('[PATIENT_CONTEXT] Recent encounters with valid case notes:', contextData.encountersWithCaseNotes);
          console.log('[PATIENT_CONTEXT] Formatted recent vitals:', contextData.recentVitals);
          if (data.success) {
            console.log(`[handleSaveAudio] Audio saved for mic ${mic}:`, data.filename);
            savedFiles.push(data.filename);
            console.log('Saved Files:', savedFiles);

          } else {
            setStatusMsg(
              "Failed to save audio for mic " +
              mic +
              ": " +
              (data.message || "Unknown error")
            );
            console.error(`[handleSaveAudio] Failed to save audio for mic ${mic}:`, data.message);
          }
        } catch (err) {
          setStatusMsg(
            "Error uploading audio for mic " + mic + ": " + err.message
          );
          console.error(`[handleSaveAudio] Error uploading audio for mic ${mic}:`, err);
        }
      }
      if (savedFiles.length > 0) {
        setStatusMsg(`Audio saved: ${savedFiles.join(", ")}`);
        setLastSavedAudio(savedFiles.length === 1 ? savedFiles[0] : savedFiles);
        console.log('[handleSaveAudio] Audio saved:', savedFiles);
        resolve("Audio saved successfully");
      } else {
        setStatusMsg("No audio files were saved.");
        reject("No audio files were saved");
        setLastSavedAudio(null);
        console.warn('[handleSaveAudio] No audio files were saved.');
      }
    });

    toast.promise(savePromise, {
      loading: "Saving audio...",
      success: "Audio saved successfully!",
      error: (err) => `Error: ${err}`,
    });
  };
  const [language, setLanguage] = useState("ml-IN");
  const [recording, setRecording] = useState(false);
  const [modeLocked, setModeLocked] = useState(null);
  const [paused, setPaused] = useState(false);
  const [summary, setSummary] = useState("");
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [mics, setMics] = useState([]);
  const [micRoles, setMicRoles] = useState([]);
  const [statusMsg, setStatusMsg] = useState("");
  const [selectedMics, setSelectedMics] = useState([]);
  const [dspConfigs, setDSPConfigs] = useState({});
  const [languageDisabled, setLanguageDisabled] = useState(false);
  // Use props instead of local state for session data
  const sessionId = propSessionId || "";
  const eventId = propEventId || "";
  const patientId = propPatientId || "";
  const role = propRole || "";

  const [audioChunks, setAudioChunks] = useState([]);
  const [transcriptionChunks, setTranscriptionChunks] = useState([]);
  const [mergedTranscript, setMergedTranscript] = useState("");
  const [isConnected, setIsConnected] = useState(true);
  const [volumeLevel, setVolumeLevel] = useState(0); // For live volume meter
  const volumeMeterRef = useRef(null);
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const sourceRef = useRef(null);
  const animationFrameRef = useRef(null);

  // Expose recording state to parent component
  useImperativeHandle(ref, () => ({
    isRecordingActive: () => recording || paused
  }));

  const fetchMicsFromAgent = async () => {
    await fetchMicsFromAgentService(setMics, micRoles, setMicRoles);
  };

  // Patient Data Sync Functions
  const fetchPatientData = async () => {
    console.log('[fetchPatientData] Called');
    console.log('[fetchPatientData] Environment variables:', {
      VITE_USE_MOCK_DATA: import.meta.env.VITE_USE_MOCK_DATA,
      VITE_EXTERNAL_API: import.meta.env.VITE_EXTERNAL_API,
      USE_MOCK_DATA,
      EXTERNAL_API_URL
    });

    try {
      let data;
      if (USE_MOCK_DATA) {
        console.log('[fetchPatientData] Fetching mock data from /mock/patientResponse.json');
        const resp = await fetch('/mock/patientResponse.json');
        if (!resp.ok) {
          console.error('[fetchPatientData] Failed to fetch mock data:', resp.status);
          throw new Error(`Failed to fetch mock data: ${resp.status}`);
        }
        const json = await resp.json();
        console.log('[fetchPatientData] Mock response:', json);
        data = json;
      } else {
        console.log('[fetchPatientData] Fetching data from external API:', EXTERNAL_API_URL);
        const payload = {
          regNo: currentPatientId || patientId || "",
          sessionId: sessionId || "",
          _sessionId: ""
        };
        console.log('[fetchPatientData] Built payload:', payload);
        const response = await axios.post(
          `${EXTERNAL_API_URL}/api/Encounter/GetCurrentEncounter`,
          payload,
          {
            timeout: 10000, // 10 second timeout
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${VITE_EMR_BEARER_TOKEN}`
            }
          }
        );
        console.log('[fetchPatientData] API response:', response.data);
        data = response.data;
      }
      setPatientData(data);

      console.log('[fetchPatientData] setPatientData called with:', data);
    } catch (error) {
      console.error('[fetchPatientData] Error fetching patient data:', error);
      setPatientDataError(error.message);
      setPatientDataSyncStatus('error');
      toast.error(`Failed to sync patient data: ${error.message}`);
    } finally {
      setPatientDataLoading(false);
      console.log('[fetchPatientData] Done');
    }
  };

  useEffect(() => {
    console.log('[patientData useEffect] Triggered. patientData:', patientData);
    if (patientData === null) return;
    // Always use patient name/id from API response (mock or real)
    const patient = patientData.patient || {};
    const patientIdFromResponse = patientData.data.patient.regNo || "";
    const patientNameFromResponse = patient.name || "";

    console.log('[patientData useEffect] Extracted patient info:', {
      patientId: patientData.data.patient.regNo,
      patientName: patientData.data.patient.name,
      isNewVisit: patientData.isNewVisit,
      source: USE_MOCK_DATA ? 'Mock Data' : 'External API'
    });

    setCurrentPatientId(patientData.data.patient.regNo);
    setCurrentPatientName(patientData.data.patient.name);
    setHeaderPatientName(patientData.data.patient.name);
    setIsNewVisit(patientData.data.isNewVisit === true);

    // Show toast if patient data changed
    if (patientIdFromResponse !== currentPatientId || patientNameFromResponse !== currentPatientName) {
      console.log('[patientData useEffect] Patient data changed, showing toast');
      toast.success(`Patient data loaded: ${patientNameFromResponse} (${patientIdFromResponse})`);
    }

    // Send data to backend for storage
    (async () => {
      try {
        console.log('[patientData useEffect] Storing patient data to backend...');
        await storePatientData(patientData.data);
        toast.success('Patient data synced successfully');
      } catch (err) {
        console.error('[patientData useEffect] Error storing patient data:', err);
      }
    })();
    console.log('patientDta', patientData);

    // Generate previous case notes summary asynchronously (non-blocking)
    if (patientIdFromResponse) {
      console.log(`[patientData useEffect] Starting async case notes summary generation for patient: ${patientIdFromResponse} (delayed 2s)`);
      setTimeout(() => {
        generatePreviousCaseNotesSummary(patientIdFromResponse);
      }, 2000);
    } else {
      console.log(`[patientData useEffect] No patientId available, skipping case notes generation`);
    }

    setPatientDataSyncStatus('success');
  }, [patientData]);

  const storePatientData = async (data) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/api/storePatientData`, data, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.data.success) {
        console.log('[EMR API] Patient data stored successfully:', response.data.message);
      } else {
        throw new Error(response.data.message || 'Failed to store patient data');
      }
    } catch (error) {
      console.error('Error storing patient data:', error);
      throw error;
    }
  };

  useEffect(() => {
    if (sidebarOpen) fetchMicsFromAgent();
  }, [sidebarOpen]);

  // Fetch patient data only once per unique patient/session to avoid duplicate API calls
  const lastFetchRef = useRef({ patientId: null, sessionId: null });
  useEffect(() => {
    // Only fetch if patientId or sessionId changed
    if (
      patientId &&
      (lastFetchRef.current.patientId !== patientId ||
        lastFetchRef.current.sessionId !== sessionId)
    ) {
      lastFetchRef.current = { patientId, sessionId };
      fetchPatientData();
    }
    // Only run on mount or when patientId/sessionId changes
  }, [patientId, sessionId]);
  // Handle responsive sidebar behavior
  useEffect(() => {
    const handleResize = () => {
      // On screens > 1440px, sidebar is always visible (not collapsible)
      // On screens ≤ 1440px, sidebar becomes collapsible overlay
      // On xs screens (≤ 480px), start with sidebar collapsed to save space
      if (window.innerWidth > 1440) {
        setSidebarOpen(true);
      } else if (window.innerWidth <= 480) {
        setSidebarOpen(false);
      }
      // For screens between 481px and 1440px, let user control the state
    };

    // Set initial state only
    if (window.innerWidth > 1440) {
      setSidebarOpen(true);
    } else if (window.innerWidth <= 480) {
      setSidebarOpen(false);
    }
    // For screens between 481px and 1440px, default to open but allow user control

    // Add event listener for resize only (not for initial state changes)
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // --- ROUTEMAP: Step 2: Microphone(s) chosen ---
  useEffect(() => {
    const availableIds = mics.map((m) => String(m.id));
    const filtered = micRoles.filter((m) =>
      availableIds.includes(String(m.deviceId))
    );
    if (filtered.length !== micRoles.length) {
      setMicRoles(filtered);
    }
    if (filtered.length > 0) {
      const chosen = filtered
        .map((m) => `${m.role} (${m.deviceId})`)
        .join(", ");
      console.log(`[ROUTEMAP] Step 2: Microphone(s) chosen: ${chosen}`);
    }
  }, [mics]);

  useEffect(() => {
    const unique = {};
    micRoles.forEach((m) => {
      if (
        m.deviceId !== undefined &&
        m.deviceId !== null &&
        m.deviceId !== ""
      ) {
        unique[m.deviceId] = {
          device: m.deviceId,
          role: m.role,
          micname: m.micname,
        };
      }
    });
    setSelectedMics(Object.values(unique));
    console.log(selectedMics, "selected mics");
  }, [micRoles]);

  // Sync mergedTranscript with transcriptLogger's formattedText for the session
  useEffect(() => {
    if (sessionId) {
      const sessionData = transcriptLogger.getSessionData(sessionId);
      if (
        sessionData &&
        sessionData[sessionId] &&
        Array.isArray(sessionData[sessionId].formattedText)
      ) {
        // Map logger's formattedText to ConversationPanel's expected format
        const merged = sessionData[sessionId].formattedText.map((entry) => ({
          text: entry.text,
          role: entry.speakerRole,
          timestamp: entry.timestamp,
          deviceId: entry.deviceId,
          initials: entry.speakerRole ? entry.speakerRole[0] : "?",
        }));
        setMergedTranscript(merged);
      } else {
        setMergedTranscript("");
      }
    } else {
      setMergedTranscript("");
    }
  }, [transcriptionChunks, sessionId]);

  // On Start: clear IndexedDB and save only current config
  const handleStart = async () => {
    // Disable language selection when starting recording
    setLanguageDisabled(true);
    // Clear uploaded file info when starting new recording
    setUploadedFileInfo(null);
    await handleStartService({
      setStatusMsg,
      sessionId,
      eventId,
      selectedMics,
      setIsConnected,
      micRoles,
      setMicRoles,
      dspConfigs,
      setRecording,
      setTranscriptionChunks,
      language,
      setLanguage,
      modeLocked,
      setModeLocked
    });
  };

  // On Pause: temporarily halt audio capture without ending session
  const handlePause = async () => {
    await handlePauseService(setStatusMsg, setPaused);
  };

  // On Resume: resume audio capture from paused state
  const handleResume = async () => {
    await handleResumeService(setStatusMsg, setPaused);
  };

  // Handle opening the report modal with proper focus management
  const handleOpenReportModal = () => {
    // First, blur any active contentEditable elements (like RichTextEditor)
    const activeElement = document.activeElement;
    if (activeElement && activeElement.contentEditable === 'true') {
      activeElement.blur();
    }

    // Open the modal
    setShowReportModal(true);

    // Focus on the title input after a short delay to ensure modal is rendered
    setTimeout(() => {
      if (reportTitleInputRef.current) {
        reportTitleInputRef.current.focus();
      }
    }, 100);
  };

  // Report Issue handler
  const handleReportIssue = async () => {
    await handleReportIssueService({
      reportTitle,
      reportDescription,
      setReportLoading,
      reportFiles,
      sessionId,
      eventId,
      patientId,
      headerPatientName,
      consultantData: patientData?.data?.consultant,
      setShowReportModal,
      setReportTitle,
      setReportDescription,
      setReportFiles,
    });
  };
  // Enhanced close modal handler with confirmation and toast
  const handleCloseModal = () => {
    if (recording || paused) {
      const confirmClose = window.confirm(
        "A recording is ongoing or paused. Closing will remove partial recordings. Do you want to proceed?"
      );
      if (!confirmClose) return;
    }
    // If summarization is ongoing, show a toast (replace with your actual check)
    if (uploadLoading) {
      toast("Summarization is progressing in the background.", { icon: "ℹ️" });
    }

    // Check if this is running in an iframe
    if (window.parent !== window) {
      // This is an iframe, communicate with parent to close modal
      try {
        window.parent.postMessage({ action: 'closeModal', modalType: 'mic' }, '*');
      } catch (error) {
        console.error('Error communicating with parent window:', error);
        // Fallback to onClose if postMessage fails
        onClose();
      }
    } else if (window.opener && !window.opener.closed) {
      // This is a popup window, close it
      window.close();
    } else {
      // This is a regular modal, use the onClose callback
      onClose();
    }
  };

  const handleConfirmClose = async () => {
    await handleConfirmCloseService(
      recording,
      paused,
      handleStop,
      setShowCloseConfirmation,
      uploadLoading,
      onClose
    );
  };

  // On Stop: update IndexedDB with latest config and autosave audio
  const handleStop = async () => {
    console.log(currentPatientId, 'CURRENTPATIENTID');

    await handleStopService({
      setStatusMsg,
      setIsConnected,
      setRecording,
      setPaused,
      setMicRoles,
      micRoles,
      audioChunks,
      handleSaveAudio,
      sessionId,
      currentPatientId,
      eventId,
      setShowEnhancedPlayer,
      generateSummaryFromLastSaved,
      language,
      setUploadLoading,
      setSummary,
      modeLocked,
      setModeLocked
    });
    // Kick off summarization immediately after stop
  };

  // Helper function to generate summary from last saved audio
  const generateSummaryFromLastSaved = async () => {
    await generateSummaryFromLastSavedService({
      setStatusMsg,
      language,
      setCombinedSummary,
      setSummary,
      sessionId,
      eventId,
      patientId: currentPatientId,
    });
  };

  // Helper function to generate previous case notes summary asynchronously
  const generatePreviousCaseNotesSummary = async (regNo) => {
    try {
      console.log(`[EMR API] Starting async case notes summary generation for patient: ${regNo}`);
      console.log(`[EMR API] API_BASE_URL: ${API_BASE_URL}`);

      const response = await fetch(`${API_BASE_URL}/api/generatePreviousCaseNotesSummary`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ regNo }),
      });

      console.log(`[EMR API] Response status: ${response.status}`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`[EMR API] Response data:`, data);

      if (data.success) {
        if (data.data.summaryGenerated) {
          toast.success(`Previous case notes summary generated for ${data.data.name}`);
          console.log(`[EMR API] Summary generated successfully for ${regNo}:`, data.data.previousCaseNotesSummary);
        } else {
          console.log(`[EMR API] No case notes found for patient ${regNo}:`, data.message);
          // toast.info(`No previous case notes found for patient ${data.data.name}`);
        }
      } else {
        console.warn(`[EMR API] Failed to generate summary for ${regNo}:`, data.message);
        toast.warn(`Failed to generate case notes summary: ${data.message}`);
      }
    } catch (error) {
      console.error(`[EMR API] Error generating case notes summary for ${regNo}:`, error);
      toast.error(`Error generating case notes summary: ${error.message}`);
      // Don't show error toast as this is a background operation
    }
  };

  const handleApplyDSP = async () => {
    await handleApplyDSPService(
      dspConfigs,
      setDSPConfigs,
      selectedMics,
      micRoles,
      setStatusMsg
    );
  };

  // Independent handler for Get Summary
  async function handleGetSummary() {
    console.log("[GetSummary] triggered");

    // Clear any existing timer
    if (prolongedMessageTimerRef.current) {
      clearTimeout(prolongedMessageTimerRef.current);
      prolongedMessageTimerRef.current = null;
    }

    // Reset prolonged message state
    setShowProlongedMessage(false);

    // Set timer for prolonged processing message (15 seconds)
    prolongedMessageTimerRef.current = setTimeout(() => {
      setShowProlongedMessage(true);
    }, 15000);

    try {
      await handleGetSummaryService(
        sessionId,
        eventId,
        language,
        setStatusMsg,
        setSummary,
        setUploadLoading,
        modeLocked,
        setModeLocked,
        setShowProlongedMessage,
        prolongedMessageTimerRef,
        false, // isStopped
        currentPatientId
      );
      setGetSummaryDisabled(true); // Disable button after summary is loaded
    } catch (error) {
      // Handle errors and show error toast
      console.error("[GetSummary] Error:", error);
      toast.error("Summary generation failed – please retry.");
    } finally {
      // Clear timer when operation completes
      if (prolongedMessageTimerRef.current) {
        clearTimeout(prolongedMessageTimerRef.current);
        prolongedMessageTimerRef.current = null;
      }
      setShowProlongedMessage(false);
    }
  }

  // Returns the default DSP config (should match DSPControls.jsx)
  const getDefaultDSP = () => ({
    noiseGate: {
      threshold: 0.1,
      attack: 0.01,
      hold: 0.01,
      release: 0.01,
      ratio: 2,
    },
    compressor: {
      threshold: 0.1,
      attack: 0.01,
      hold: 0.01,
      release: 0.05,
      ratio: 2,
    },
    limiter: { threshold: 0.1, ratio: 2 },
    eq: {
      lowCutFreq: 400,
      lowCutSlope: 18,
      highCutFreq: 10000,
      highCutSlope: 18,
      mid1Freq: 500,
      mid1Q: 1,
      mid2Freq: 4000,
      mid2Q: 1,
    },
  });

  const [getSummaryDisabled, setGetSummaryDisabled] = useState(false);

  // State for prolonged processing message
  const [showProlongedMessage, setShowProlongedMessage] = useState(false);
  const prolongedMessageTimerRef = useRef(null);

  // Auto-load persisted DSP/mic role settings from backend
  // useEffect(() => {
  //   fetch('http://localhost:5001/dspsettings.json')
  //     .then(res => res.json())
  //     .then(settings => {
  //       console.log('[DSP/ROLE] Fetched persisted settings:', settings);
  //       if (!settings || typeof settings !== 'object') return;
  //       // Map settings to micRoles and dspConfigs
  //       const loadedRoles = [];
  //       const loadedDSP = {};
  //       console.log('[DSP/ROLE] Current mics:', mics);
  //       mics.forEach(mic => {
  //         const micname = mic.name || mic.label;
  //         if (micname && settings[micname]) {
  //           // Use persisted config
  //           loadedRoles.push({
  //             deviceId: String(mic.id),
  //             role: settings[micname].selectedRole || 'Doctor',
  //             micname // <-- assign the actual display name
  //           });
  //           loadedDSP[mic.id] = settings[micname].settingsParams || getDefaultDSP();
  //         } else {
  //           // Use default config
  //           loadedRoles.push({
  //             deviceId: String(mic.id),
  //             role: 'Doctor',
  //             micname // <-- assign the actual display name
  //           });
  //           loadedDSP[mic.id] = getDefaultDSP();
  //         }
  //       });
  //       console.log('[DSP/ROLE] Loaded micRoles:', loadedRoles);
  //       console.log('[DSP/ROLE] Loaded dspConfigs:', loadedDSP);
  //       setMicRoles(loadedRoles);
  //       setDSPConfigs(loadedDSP);
  //     });
  //   // Only run when mics list changes
  // }, [JSON.stringify(mics)]);
  // async function fetchDSPSettingsAndMap(mics) {
  //   try {
  //     const resp = await fetch('http://localhost:5001/dspsettings.json');
  //     if (!resp.ok) return {};
  //     const data = await resp.json();
  //     // Build {micId: dspConfig} by matching micname to mic.name
  //     const mapped = {};
  //     console.log('Fetched DSP settings:', data);
  //     console.log('Available microphones:', mics);
  //     mics.forEach(mic => {
  //       // Try to find a DSP config where micname matches mic.name
  //       const dspEntry = Object.entries(data).find(
  //         ([micname]) => micname === mic.name
  //       );
  //       console.log(dspEntry, 'dspEntry for mic', mic.name);
  //       if (dspEntry && dspEntry[1].settingsParams) {
  //         mapped[mic.name] = {
  //           ...dspEntry[1],
  //           settingsParams: dspEntry[1].settingsParams
  //         };
  //       }
  //     });
  //     console.log(mapped, 'Mapped DSP settings for microphones');

  //     console.log('[ROUTEMAP 1] Mics loaded from agent/backend:', mics);
  //     return mapped;
  //   } catch (e) {
  //     return {};
  //   }
  // }
  // Pre-populate DSP configs and micRoles from backend when mics are loaded
  useEffect(() => {
    if (mics.length === 0) return;

    // Load DSP settings from mmr_agent dspsettings.json file
    authService.authenticatedFetch("http://localhost:5001/dspsettings.json")
      .then((res) => res.json())
      .then((settings) => {
        if (!settings || typeof settings !== "object") return;
        // Only select mics that have a persisted config (auto-check in sidebar)
        const loadedDSP = {};
        const loadedRoles = [];
        mics.forEach((mic) => {
          const micname = mic.name || mic.label;
          if (micname && settings[micname]) {
            // Only select mics that have a persisted config
            loadedDSP[mic.id] =
              settings[micname].settingsParams || getDefaultDSP();
            loadedRoles.push({
              deviceId: String(mic.id),
              role: settings[micname].selectedRole || "Doctor",
              micname,
            });
          }
        });
        setDSPConfigs(loadedDSP);
        setMicRoles(loadedRoles);
        // Optionally, log for debug
      })
      .catch((error) => {
        console.error("Error loading DSP settings:", error);
        // Continue with empty settings if loading fails
      });
  }, [JSON.stringify(mics)]);

  useEffect(() => {
    setGetSummaryDisabled(!!summary && summary.trim().length > 0);
  }, [summary]);

  // Clear prolonged message when loading stops
  useEffect(() => {
    if (!uploadLoading) {
      setShowProlongedMessage(false);
      if (prolongedMessageTimerRef.current) {
        clearTimeout(prolongedMessageTimerRef.current);
        prolongedMessageTimerRef.current = null;
      }
    }
  }, [uploadLoading]);

  // Live volume meter effect
  useEffect(() => {
    if (!recording) {
      setVolumeLevel(0);
      if (audioContextRef.current) {
        audioContextRef.current.close();
        audioContextRef.current = null;
      }
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      return;
    }
    let mounted = true;
    let stream;
    async function setupVolumeMeter() {
      try {
        stream = await navigator.mediaDevices.getUserMedia({ audio: true });
        audioContextRef.current = new (window.AudioContext ||
          window.webkitAudioContext)();
        sourceRef.current =
          audioContextRef.current.createMediaStreamSource(stream);
        analyserRef.current = audioContextRef.current.createAnalyser();
        analyserRef.current.fftSize = 256;
        sourceRef.current.connect(analyserRef.current);
        const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
        function updateMeter() {
          if (!mounted) return;
          analyserRef.current.getByteTimeDomainData(dataArray);
          let sum = 0;
          for (let i = 0; i < dataArray.length; i++) {
            const val = (dataArray[i] - 128) / 128;
            sum += val * val;
          }
          const rms = Math.sqrt(sum / dataArray.length);
          // console.log('[VolumeMeter Debug] Calculated RMS:', rms);
          setVolumeLevel(rms);
          animationFrameRef.current = requestAnimationFrame(updateMeter);
        }
        updateMeter();
      } catch (err) {
        setVolumeLevel(0);
      }
    }
    setupVolumeMeter();
    return () => {
      mounted = false;
      if (audioContextRef.current) {
        audioContextRef.current.close();
        audioContextRef.current = null;
      }
      if (stream) {
        stream.getTracks().forEach((track) => track.stop());
      }
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [recording]);

  // Resource cleanup on modal close
  useEffect(() => {
    return () => {
      if (audioContextRef.current) {
        audioContextRef.current.close();
        audioContextRef.current = null;
      }
      if (sourceRef.current) {
        sourceRef.current.disconnect();
        sourceRef.current = null;
      }
      if (analyserRef.current) {
        analyserRef.current.disconnect();
        analyserRef.current = null;
      }
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      // Clear prolonged message timer
      if (prolongedMessageTimerRef.current) {
        clearTimeout(prolongedMessageTimerRef.current);
        prolongedMessageTimerRef.current = null;
      }
      // Optionally: delete temp files here if needed
    };
  }, []);

  // State for patient name
  const [headerPatientName, setHeaderPatientName] = useState("");


  return (
    <div style={{
      width: "100vw",
      height: "100vh",
      display: "flex",
      flexDirection: "column",
      background: "#f5f5f5"
    }}>
      <div
        style={{
          width: "100%",
          height: "100%",
          background: "white",
          display: "flex",
          flexDirection: "column",
          overflow: "hidden"
        }}
      >
        {/* Main Content - Header removed */}
        <div style={{ flex: 1, overflow: "auto", position: "relative" }}>
          <div className="app-container" style={{ height: "100%" }}>
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 3000,
                style: {
                  background: "#363636",
                  color: "#fff",
                },
              }}
            />

            {isAdmin(role) && (
              <>
                {/* Backdrop for collapsible sidebar */}
                {sidebarOpen && window.innerWidth <= 1440 && (
                  <div
                    className={`sidebar-backdrop ${sidebarOpen ? 'active' : ''}`}
                    onClick={() => setSidebarOpen(false)}
                  />
                )}
                <aside className={`sidebar ${!sidebarOpen ? 'sidebar-collapsed' : ''}`}>
                  <Sidebar
                    mics={mics}
                    micRoles={micRoles}
                    setMicRoles={setMicRoles}
                    dspConfigs={dspConfigs}
                    setDSPConfigs={setDSPConfigs}
                    onRefresh={fetchMicsFromAgent}
                    onCollapse={() => {
                      console.log('Collapse clicked, window width:', window.innerWidth);
                      console.log('Current sidebarOpen state:', sidebarOpen);
                      setSidebarOpen(false);
                      console.log('Sidebar should now be collapsed');
                    }}
                    recording={recording}
                    onApplyDSP={handleApplyDSP}
                    isAdminRole={isAdmin(role)}
                    isCollapsed={!sidebarOpen}
                  />
                </aside>
              </>
            )}
            {!sidebarOpen && isAdmin(role) && (
              <button
                className="sidebar-toggle sidebar-toggle-collapsed"
                onClick={() => setSidebarOpen(true)}
                title="Open menu"
              >
                ☰
              </button>
            )}
            <div
              style={{
                flex: 1,
                display: "flex",
                flexDirection: "column",
                minWidth: 0,
              }}
            >
              <div className="header">
                <div className="header-session">
                  <span className="session-id">
                    <strong>Patient ID:</strong> {currentPatientId || patientId || (patientDataSyncStatus === 'loading' ? "Loading..." : "N/A")} &nbsp;
                    <strong>Patient Name:</strong> {headerPatientName || (patientDataSyncStatus === 'loading' ? "Loading..." : "N/A")}
                  </span>
                  {/* Log sessionId display */}
                  {console.log('[ClinicalUIPopUp] Displaying sessionId:', sessionId)}
                  {isAdmin(role) && (
                    <>
                      <span className="session-id">
                        <strong>
                          Session Id : </strong>{sessionId || "N/A"}  &nbsp;<strong> Event Id :</strong>{" "}
                        {eventId || "N/A"}
                      </span>
                      {/* <button
                        style={{ marginLeft: 12, padding: '2px 8px', fontSize: 12, cursor: 'pointer' }}
                        onClick={handleOpenTestEnv}
                        title="Open Test Environment"
                      >
                        Open Test Env
                      </button> */}
                    </>
                  )}
                  {patientDataSyncStatus === 'success' && (
                    <span className="session-id">
                      <strong>Is New Visit:</strong>
                      <input
                        type="checkbox"
                        id="is-new-visit-checkbox"
                        checked={isNewVisit}
                        disabled={true} // disable the checkbox to prevent changes
                      />

                    </span>
                  )}
                  {/* Patient Data Sync Status Indicator */}
                  <div style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    marginTop: '4px',
                    fontSize: '12px'
                  }}>
                    <span style={{ fontWeight: 'bold' }}>Patient Data Sync:</span>
                    {patientDataSyncStatus === 'loading' && (
                      <span style={{ color: '#007bff' }}>
                        🔄 Loading patient data...
                      </span>
                    )}

                    {patientDataSyncStatus === 'success' && (
                      <span style={{ color: '#28a745' }}>
                        ✅ Synced ({USE_MOCK_DATA ? 'Mock' : 'Live'} Data)
                      </span>
                    )}
                    {patientDataSyncStatus === 'error' && (
                      <span style={{ color: '#dc3545', cursor: 'pointer' }}
                        onClick={fetchPatientData}
                        title="Click to retry">
                        ❌ Failed (Click to retry)
                      </span>
                    )}
                    {patientDataSyncStatus === 'idle' && (
                      <span style={{ color: '#6c757d' }}>
                        ⏳ Initializing...
                      </span>
                    )}
                  </div>

                  {/* Recording Status Indicator */}
                  {(recording || paused) && (
                    <div
                      style={{
                        display: "flex",
                        alignItems: "center",
                        gap: "8px",
                        padding: "4px 12px",
                        backgroundColor: paused ? "#ffc107" : "#28a745",
                        color: "white",
                        borderRadius: "20px",
                        fontSize: "14px",
                        fontWeight: "500",
                        animation:
                          recording && !paused ? "pulse 2s infinite" : "none",
                      }}
                    >
                      <span
                        style={{
                          width: "8px",
                          height: "8px",
                          borderRadius: "50%",
                          backgroundColor: "white",
                          animation:
                            recording && !paused ? "blink 1s infinite" : "none",
                        }}
                      ></span>
                      {paused ? "PAUSED" : "RECORDING"}
                    </div>
                  )}
                  <div className="controls-container">
                    <div className="lang-select">
                      <label
                        htmlFor="language-select"
                        style={{
                          fontSize: "14px",
                          fontWeight: "500",
                          marginRight: "8px",
                          color: "#2c3e50",
                        }}
                      >
                        <strong> Language:</strong>
                      </label>
                      <select
                        id="language-select"
                        value={language}
                        onChange={(e) => setLanguage(e.target.value)}
                        disabled={
                          languageDisabled ||
                          !selectedMics ||
                          selectedMics.length === 0
                        }
                        style={{
                          padding: "6px 12px",
                          borderRadius: "6px",
                          border: "1px solid #ccc",
                          fontSize: "14px",
                          minWidth: "120px",
                          backgroundColor:
                            languageDisabled ||
                              !selectedMics ||
                              selectedMics.length === 0
                              ? "#f5f5f5"
                              : "white",
                          color:
                            languageDisabled ||
                              !selectedMics ||
                              selectedMics.length === 0
                              ? "#999"
                              : "black",
                          cursor:
                            languageDisabled ||
                              !selectedMics ||
                              selectedMics.length === 0
                              ? "not-allowed"
                              : "pointer",
                        }}
                      >
                        <option value="ml-IN">Malayalam</option>
                        <option value="en-US">English</option>
                        <option value="hi-IN">Hindi</option>
                        <option value="ta-IN">Tamil</option>
                      </select>
                    </div>
                    <button
                      className="btn btn-primary"
                      onClick={
                        paused
                          ? handleResume
                          : recording
                            ? handlePause
                            : handleStart
                      }
                      disabled={
                        (!recording &&
                          (!selectedMics || selectedMics.length === 0)) ||
                        lastSavedAudio ||
                        modeLocked === 'upload'
                      }
                      {...(!selectedMics || selectedMics.length === 0
                        ? { "data-tooltip": "Select at least one microphone" }
                        : {})}
                      style={{
                        backgroundColor: paused
                          ? "#28a745"
                          : recording
                            ? "#6c757d"
                            : "#007bff",
                        borderColor: paused
                          ? "#28a745"
                          : recording
                            ? "#6c757d"
                            : "#007bff",
                      }}
                    >
                      <span
                        className={`icon ${paused
                          ? "icon-play"
                          : recording
                            ? "icon-pause"
                            : "icon-mic"
                          }`}
                      ></span>
                      {paused ? "Resume" : recording ? "Pause" : "Start"}
                    </button>
                    <button
                      className="btn btn-light"
                      onClick={handleStop}
                      disabled={!recording}
                      style={{
                        backgroundColor: recording ? "#dc3545" : "#6c757d",
                        borderColor: recording ? "#dc3545" : "#6c757d",
                        color: "white",
                      }}
                    >
                      <span className="icon icon-stop"></span>
                      Stop
                    </button>
                    <button
                      className="btn btn-success"
                      disabled={
                        !lastSavedAudio ||
                        uploadLoading ||
                        recording ||
                        getSummaryDisabled
                      }
                      onClick={handleGetSummary}
                      style={{
                        position: "relative",
                        minWidth: "120px",
                      }}
                    >
                      {uploadLoading ? (
                        <span
                          style={{
                            display: "inline-flex",
                            alignItems: "center",
                            gap: 6,
                          }}
                        >
                          <span
                            className="spinner-border spinner-border-sm"
                            role="status"
                            aria-hidden="true"
                            style={{
                              width: 16,
                              height: 16,
                              borderWidth: 2,
                              marginRight: 6,
                            }}
                          ></span>
                          Getting Summary...
                        </span>
                      ) : (
                        <>
                          <span className="icon icon-document"></span>
                          Get Summary
                        </>
                      )}
                    </button>
                    {isAdmin(role) && (
                      <button
                        className="btn btn-info"
                        style={{
                          marginLeft: 0,
                          display: isAdmin(role) ? "inline-flex" : "none",
                          alignItems: "center",
                          gap: 6,
                        }}
                        onClick={() => {
                          setShowUploadModal(true);
                          setModeLocked('upload');
                        }}
                        disabled={recording || modeLocked === 'recording'}
                        data-tooltip="Stop recording before uploading"
                      >
                        <FileAudio className="h-5 w-5 text-purple-600" />
                        Upload Audios
                      </button>
                    )}
                    {/* Upload Audios Button triggers modal */}

                    {/* Upload Modal */}
                    {showUploadModal && (
                      <div className="upload-modal-overlay">
                        <div className="upload-modal-container">
                          {/* Header */}
                          <div className="upload-modal-header">
                            <h3 className="upload-modal-title">
                              Upload Audio Files
                            </h3>
                            <button
                              className="upload-modal-close-btn"
                              onClick={handleUploadCancel}
                              onMouseEnter={(e) =>
                                (e.target.style.background = "#f3f4f6")
                              }
                              onMouseLeave={(e) =>
                                (e.target.style.background = "none")
                              }
                            >
                              ×
                            </button>
                          </div>

                          {/* File Selection */}
                          <div>
                            <label className="upload-modal-label">
                              Choose audio files:
                            </label>
                            <label
                              className="upload-modal-file-drop"
                              onMouseEnter={(e) => {
                                e.target.style.borderColor = "#9ca3af";
                                e.target.style.background = "#f3f4f6";
                              }}
                              onMouseLeave={(e) => {
                                e.target.style.borderColor = "#d1d5db";
                                e.target.style.background = "#f9fafb";
                              }}
                            >
                              Choose file(s)
                              <input
                                type="file"
                                accept="audio/wav,audio/wave,audio/x-wav,audio/mp3,audio/mpeg"
                                multiple
                                style={{ display: "none" }}
                                onChange={(e) => {
                                  const files = Array.from(
                                    e.target.files || []
                                  );
                                  setUploadFiles((prev) => [...prev, ...files]);
                                }}
                                disabled={uploadLoading}
                              />
                            </label>
                          </div>

                          {/* Selected Files */}
                          {uploadFiles && uploadFiles.length > 0 && (
                            <div>
                              <label className="upload-modal-label">
                                Selected files ({uploadFiles.length}):
                              </label>
                              <div className="upload-modal-file-list">
                                {uploadFiles.map((file, idx) => (
                                  <div
                                    key={idx}
                                    className="upload-modal-file-item"
                                  >
                                    <span className="upload-modal-file-name">
                                      {file.name}
                                    </span>
                                    <button
                                      type="button"
                                      className="upload-modal-remove-btn"
                                      title="Remove file"
                                      onClick={() => {
                                        if (!uploadLoading) {
                                          setUploadFiles((prev) =>
                                            prev.filter((_, i) => i !== idx)
                                          );
                                        }
                                      }}
                                      disabled={uploadLoading}
                                      onMouseEnter={(e) =>
                                        !uploadLoading &&
                                        (e.target.style.background = "#fee2e2")
                                      }
                                      onMouseLeave={(e) =>
                                        (e.target.style.background = "none")
                                      }
                                    >
                                      ×
                                    </button>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                          {/* Language Selection */}
                          <div>
                            <label className="upload-modal-label">
                              Language:
                            </label>
                            <select
                              value={uploadLanguage}
                              onChange={(e) =>
                                setUploadLanguage(e.target.value)
                              }
                              className="upload-modal-select"
                              disabled={uploadLoading}
                            >
                              <option value="ml-IN">Malayalam</option>
                              <option value="en-US">English</option>
                              <option value="hi-IN">Hindi</option>
                              <option value="ta-IN">Tamil</option>
                            </select>
                          </div>

                          {/* Footer Buttons */}
                          <div className="upload-modal-footer">
                            <div className="upload-modal-footer-info">
                              {uploadFiles.length > 0 &&
                                `${uploadFiles.length} file${uploadFiles.length !== 1 ? "s" : ""
                                } selected`}
                            </div>
                            <div className="upload-modal-footer-actions">
                              <button
                                className="upload-modal-btn upload-modal-cancel-btn"
                                onClick={handleUploadCancel}
                                onMouseEnter={(e) =>
                                  (e.target.style.background = "#f3f4f6")
                                }
                                onMouseLeave={(e) =>
                                  (e.target.style.background = "white")
                                }
                              >
                                Cancel
                              </button>
                              <button
                                className={`upload-modal-btn upload-modal-upload-btn ${!uploadFiles.length || uploadLoading
                                  ? "disabled"
                                  : ""
                                  }`}
                                disabled={!uploadFiles.length || uploadLoading}
                                onClick={handleUpload}
                              >
                                {uploadLoading ? (
                                  <>
                                    <div className="upload-modal-spinner" />
                                    Uploading...
                                  </>
                                ) : (
                                  <>
                                    Upload
                                    {uploadFiles.length > 0 &&
                                      `(${uploadFiles.length})`}
                                  </>
                                )}
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Download Modal */}
                    {showDownloadModal && (
                      <div
                        style={{
                          position: "fixed",
                          top: 0,
                          left: 0,
                          width: "100vw",
                          height: "100vh",
                          background: "rgba(0,0,0,0.5)",
                          zIndex: 10000,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          backdropFilter: "blur(4px)",
                        }}
                      >
                        <div
                          style={{
                            background: "white",
                            borderRadius: "16px",
                            padding: "24px",
                            minWidth: "380px",
                            maxWidth: "520px",
                            boxShadow: "0 8px 32px rgba(0,0,0,0.12)",
                            animation: "modalFadeIn 0.3s ease",
                          }}
                        >
                          <div
                            style={{
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                              marginBottom: "20px",
                            }}
                          >
                            <h3
                              style={{
                                margin: 0,
                                fontSize: "20px",
                                fontWeight: "600",
                                color: "#2c3e50",
                              }}
                            >
                              Available Recordings
                            </h3>
                          </div>

                          {loadingRecordings ? (
                            <div
                              style={{
                                padding: "20px 0",
                                textAlign: "center",
                                color: "#64748b",
                              }}
                            >
                              Loading...
                            </div>
                          ) : (
                            <>
                              {recordingFiles.length === 0 ? (
                                <div
                                  style={{
                                    padding: "20px 0",
                                    textAlign: "center",
                                    color: "#64748b",
                                  }}
                                >
                                  No recordings available
                                </div>
                              ) : (
                                <ul
                                  style={{
                                    listStyle: "none",
                                    padding: 0,
                                    margin: "0 0 20px 0",
                                  }}
                                >
                                  {recordingFiles.map((fname, i) => (
                                    <li
                                      key={i}
                                      style={{
                                        display: "flex",
                                        alignItems: "center",
                                        padding: "12px 16px",
                                        marginBottom: "8px",
                                        backgroundColor: "#f8fafc",
                                        borderRadius: "12px",
                                        transition: "all 0.2s ease",
                                        border: "1px solid #e2e8f0",
                                      }}
                                    >
                                      <span
                                        style={{
                                          flex: 1,
                                          color: "#334155",
                                          fontSize: "14px",
                                        }}
                                      >
                                        {fname}
                                      </span>
                                      <button
                                        className="btn"
                                        style={{
                                          marginLeft: "12px",
                                          display: "flex",
                                          alignItems: "center",
                                          gap: "8px",
                                          padding: "8px 16px",
                                          borderRadius: "8px",
                                          border: "none",
                                          background: "#44546A",
                                          color: "white",
                                          fontSize: "14px",
                                          transition: "all 0.2s ease",
                                        }}
                                        onMouseEnter={(e) => {
                                          e.currentTarget.style.background =
                                            "#2c5282";
                                          e.currentTarget.style.transform =
                                            "translateY(-1px)";
                                        }}
                                        onMouseLeave={(e) => {
                                          e.currentTarget.style.background =
                                            "#1a365d";
                                          e.currentTarget.style.transform =
                                            "none";
                                        }}
                                        onClick={async () => {
                                          try {
                                            const response = await fetch(
                                              `${API_BASE_URL}/recordings_web/${encodeURIComponent(
                                                fname
                                              )}`
                                            );
                                            if (!response.ok)
                                              throw new Error(
                                                `HTTP error! status: ${response.status}`
                                              );
                                            const blob = await response.blob();
                                            const url =
                                              window.URL.createObjectURL(blob);
                                            const a =
                                              document.createElement("a");
                                            a.href = url;
                                            a.download = fname;
                                            document.body.appendChild(a);
                                            a.click();
                                            a.remove();
                                          } catch (err) {
                                            console.error(
                                              "Failed to download:",
                                              err
                                            );
                                            alert(
                                              "Failed to download audio: " +
                                              err.message
                                            );
                                          }
                                        }}
                                        title="Download audio file"
                                      >
                                        <FileAudio className="h-5 w-5 text-purple-600" />
                                        <span>Download</span>
                                      </button>
                                    </li>
                                  ))}
                                </ul>
                              )}
                              <div
                                style={{
                                  display: "flex",
                                  justifyContent: "flex-end",
                                  gap: "12px",
                                  marginTop: "20px",
                                  borderTop: "1px solid #e2e8f0",
                                  paddingTop: "20px",
                                }}
                              >
                                <button
                                  className="btn"
                                  style={{
                                    display: "flex",
                                    alignItems: "center",
                                    gap: "8px",
                                    padding: "8px 16px",
                                    borderRadius: "8px",
                                    border: "1px solid #e2e8f0",
                                    background: "white",
                                    color: "#64748b",
                                    fontSize: "14px",
                                    fontWeight: "500",
                                    transition: "all 0.2s ease",
                                  }}
                                  onMouseEnter={(e) => {
                                    e.currentTarget.style.background =
                                      "#f8fafc";
                                    e.currentTarget.style.transform =
                                      "translateY(-1px)";
                                  }}
                                  onMouseLeave={(e) => {
                                    e.currentTarget.style.background = "white";
                                    e.currentTarget.style.transform = "none";
                                  }}
                                  onClick={() => setShowDownloadModal(false)}
                                >
                                  Close
                                </button>
                                <button
                                  className="btn"
                                  style={{
                                    display: "flex",
                                    alignItems: "center",
                                    gap: "8px",
                                    padding: "8px 16px",
                                    borderRadius: "8px",
                                    border: "none",
                                    background: "#44546A",
                                    color: "white",
                                    fontSize: "14px",
                                    fontWeight: "500",
                                    transition: "all 0.2s ease",
                                  }}
                                  onMouseEnter={(e) => {
                                    e.currentTarget.style.background =
                                      "#2c5282";
                                    e.currentTarget.style.transform =
                                      "translateY(-1px)";
                                  }}
                                  onMouseLeave={(e) => {
                                    e.currentTarget.style.background =
                                      "#1a365d";
                                    e.currentTarget.style.transform = "none";
                                  }}
                                  onClick={async () => {
                                    try {
                                      const url = `${API_BASE_URL}/api/download_all?session=${encodeURIComponent(
                                        sessionId || "unknown"
                                      )}${eventId
                                        ? `&event=${encodeURIComponent(
                                          eventId
                                        )}`
                                        : ""
                                        }`;
                                      const response = await fetch(url);
                                      if (!response.ok)
                                        throw new Error(
                                          `HTTP error! status: ${response.status}`
                                        );
                                      const blob = await response.blob();
                                      const downloadUrl =
                                        window.URL.createObjectURL(blob);
                                      const a = document.createElement("a");
                                      a.href = downloadUrl;
                                      a.download = `session_${sessionId || "unknown"
                                        }_recordings.zip`;
                                      document.body.appendChild(a);
                                      a.click();
                                      a.remove();
                                    } catch (err) {
                                      console.error(
                                        "Failed to download all:",
                                        err
                                      );
                                      alert(
                                        "Failed to download all audio: " +
                                        err.message
                                      );
                                    }
                                  }}
                                  title="Download all as ZIP"
                                >
                                  <span
                                    className="icon icon-download"
                                    style={{ fontSize: "16px" }}
                                  ></span>
                                  <span>Download All</span>
                                </button>
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Report Issue Modal */}
                    {showReportModal && (
                      <div
                        style={{
                          position: "fixed",
                          top: 0,
                          left: 0,
                          width: "100vw",
                          height: "100vh",
                          background: "rgba(0,0,0,0.5)",
                          zIndex: 10000,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          backdropFilter: "blur(4px)",
                        }}
                      >
                        <div
                          style={{
                            background: "white",
                            borderRadius: "16px",
                            padding: "32px 32px 24px 32px",
                            minWidth: "600px",
                            maxWidth: "95vw",
                            maxHeight: "90vh",
                            overflowY: "auto",
                            boxShadow: "0 8px 32px rgba(0,0,0,0.12)",
                            animation: "modalFadeIn 0.3s ease",
                            display: "flex",
                            flexDirection: "column",
                            gap: 20,
                          }}
                        >
                          <div
                            style={{
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                              marginBottom: 8,
                            }}
                          >
                            <h3
                              style={{
                                margin: 0,
                                fontSize: "20px",
                                fontWeight: "600",
                                color: "#2c3e50",
                              }}
                            >
                              🐞 Report Issue
                            </h3>
                          </div>

                          <div
                            style={{
                              display: "flex",
                              flexDirection: "column",
                              gap: 20,
                            }}
                          >
                            <div>
                              <label
                                style={{
                                  fontWeight: 500,
                                  marginBottom: 8,
                                  display: "block",
                                  color: "#2c3e50",
                                  fontSize: "14px",
                                }}
                              >
                                Title:
                              </label>
                              <input
                                ref={reportTitleInputRef}
                                type="text"
                                value={reportTitle}
                                onChange={(e) => setReportTitle(e.target.value)}
                                placeholder="Brief description of the issue"
                                style={{
                                  width: "100%",
                                  padding: "12px 16px",
                                  borderRadius: 8,
                                  border: "1px solid #d1d5db",
                                  fontSize: 14,
                                  boxSizing: "border-box",
                                  transition: "border-color 0.2s ease",
                                }}
                                onFocus={(e) => e.target.style.borderColor = "#3b82f6"}
                                onBlur={(e) => e.target.style.borderColor = "#d1d5db"}
                              />
                            </div>

                            <div>
                              <label
                                style={{
                                  fontWeight: 500,
                                  marginBottom: 8,
                                  display: "block",
                                  color: "#2c3e50",
                                  fontSize: "14px",
                                }}
                              >
                                Description:
                              </label>
                              <textarea
                                value={reportDescription}
                                onChange={(e) =>
                                  setReportDescription(e.target.value)
                                }
                                placeholder="Detailed description of the issue, steps to reproduce, expected vs actual behavior..."
                                rows={6}
                                style={{
                                  width: "100%",
                                  padding: "12px 16px",
                                  borderRadius: 8,
                                  border: "1px solid #d1d5db",
                                  fontSize: 14,
                                  resize: "vertical",
                                  boxSizing: "border-box",
                                  transition: "border-color 0.2s ease",
                                  fontFamily: "inherit",
                                }}
                                onFocus={(e) => e.target.style.borderColor = "#3b82f6"}
                                onBlur={(e) => e.target.style.borderColor = "#d1d5db"}
                              />
                            </div>

                            <div>
                              <label
                                style={{
                                  fontWeight: 500,
                                  marginBottom: 8,
                                  display: "block",
                                  color: "#2c3e50",
                                  fontSize: "14px",
                                }}
                              >
                                Attachments (optional):
                              </label>
                              <label
                                style={{
                                  display: "inline-block",
                                  padding: "12px 16px",
                                  borderRadius: 8,
                                  border: "1px solid #d1d5db",
                                  background: "#f8f9fa",
                                  color: "#374151",
                                  cursor: "pointer",
                                  fontSize: 14,
                                  fontWeight: 500,
                                  transition: "all 0.2s ease",
                                }}
                                onMouseEnter={(e) => {
                                  e.target.style.background = "#e5e7eb";
                                  e.target.style.borderColor = "#9ca3af";
                                }}
                                onMouseLeave={(e) => {
                                  e.target.style.background = "#f8f9fa";
                                  e.target.style.borderColor = "#d1d5db";
                                }}
                              >
                                📎 Choose files
                                <input
                                  type="file"
                                  accept=".png,.jpg,.jpeg,.gif,.mp4,.mov,.txt,.doc,.docx,.csv,.xlsx,.pdf"
                                  multiple
                                  style={{ display: "none" }}
                                  onChange={(e) => {
                                    const files = Array.from(
                                      e.target.files || []
                                    );
                                    setReportFiles((prev) => [
                                      ...prev,
                                      ...files,
                                    ]);
                                  }}
                                />
                              </label>
                              <div
                                style={{
                                  fontSize: 12,
                                  color: "#666",
                                  marginTop: 4,
                                }}
                              >
                                Supported: screenshots, GIF, video, txt, doc,
                                docx, csv, xlsx, pdf
                              </div>

                              {reportFiles.length > 0 && (
                                <ul
                                  style={{
                                    margin: "8px 0 0 0",
                                    padding: 0,
                                    listStyle: "none",
                                    maxHeight: 120,
                                    overflowY: "auto",
                                  }}
                                >
                                  {reportFiles.map((file, idx) => (
                                    <li
                                      key={idx}
                                      style={{
                                        fontSize: 13,
                                        color: "#555",
                                        marginBottom: 4,
                                        display: "flex",
                                        alignItems: "center",
                                        justifyContent: "space-between",
                                        padding: "4px 8px",
                                        backgroundColor: "#f8f9fa",
                                        borderRadius: 4,
                                      }}
                                    >
                                      <span>📎 {file.name}</span>
                                      <button
                                        type="button"
                                        style={{
                                          marginLeft: 10,
                                          background: "none",
                                          border: "none",
                                          color: "#c00",
                                          cursor: "pointer",
                                          fontSize: 16,
                                          fontWeight: 600,
                                          padding: "2px 6px",
                                          borderRadius: 4,
                                        }}
                                        title="Remove file"
                                        onClick={() => {
                                          setReportFiles((prev) =>
                                            prev.filter((_, i) => i !== idx)
                                          );
                                        }}
                                      >
                                        ×
                                      </button>
                                    </li>
                                  ))}
                                </ul>
                              )}
                            </div>
                          </div>

                          <div
                            style={{
                              display: "flex",
                              justifyContent: "flex-end",
                              gap: 12,
                              marginTop: 16,
                              borderTop: "1px solid #e2e8f0",
                              paddingTop: 16,
                            }}
                          >
                            <button
                              className="btn btn-light"
                              onClick={() => {
                                setShowReportModal(false);
                                setReportTitle("");
                                setReportDescription("");
                                setReportFiles([]);
                              }}
                              disabled={reportLoading}
                            >
                              Cancel
                            </button>
                            <button
                              className="btn btn-warning"
                              disabled={
                                !reportTitle.trim() ||
                                !reportDescription.trim() ||
                                reportLoading
                              }
                              onClick={handleReportIssue}
                            >
                              {reportLoading ? (
                                <span
                                  style={{
                                    display: "inline-flex",
                                    alignItems: "center",
                                    gap: 6,
                                  }}
                                >
                                  <span
                                    className="spinner-border spinner-border-sm"
                                    role="status"
                                    aria-hidden="true"
                                    style={{
                                      width: 16,
                                      height: 16,
                                      borderWidth: 2,
                                    }}
                                  ></span>
                                  Reporting...
                                </span>
                              ) : (
                                "🐞 Report Issue"
                              )}
                            </button>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Close Confirmation Modal */}
                    {showCloseConfirmation && (
                      <div
                        style={{
                          position: "fixed",
                          top: 0,
                          left: 0,
                          width: "100vw",
                          height: "100vh",
                          background: "rgba(0,0,0,0.5)",
                          zIndex: 10001,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          backdropFilter: "blur(4px)",
                        }}
                      >
                        <div
                          style={{
                            background: "white",
                            borderRadius: "16px",
                            padding: "32px 28px 24px 28px",
                            minWidth: "400px",
                            maxWidth: "95vw",
                            boxShadow: "0 8px 32px rgba(0,0,0,0.12)",
                            animation: "modalFadeIn 0.3s ease",
                            display: "flex",
                            flexDirection: "column",
                            gap: 18,
                          }}
                        >
                          <div
                            style={{
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                              marginBottom: 8,
                            }}
                          >
                            <h3
                              style={{
                                margin: 0,
                                fontSize: "20px",
                                fontWeight: "600",
                                color: "#dc3545",
                              }}
                            >
                              ⚠️ Confirm Close
                            </h3>
                          </div>

                          <div
                            style={{
                              fontSize: "16px",
                              color: "#2c3e50",
                              lineHeight: "1.5",
                            }}
                          >
                            {recording || paused ? (
                              <p>
                                <strong>
                                  Recording is currently{" "}
                                  {paused ? "paused" : "active"}.
                                </strong>
                                <br />
                                Closing will discard unsaved recordings. Are you
                                sure you want to continue?
                              </p>
                            ) : (
                              <p>
                                Are you sure you want to close the ALaaS modal?
                              </p>
                            )}
                          </div>

                          <div
                            style={{
                              display: "flex",
                              justifyContent: "flex-end",
                              gap: 12,
                              marginTop: 16,
                              borderTop: "1px solid #e2e8f0",
                              paddingTop: 16,
                            }}
                          >
                            <button
                              className="btn btn-light"
                              onClick={() => setShowCloseConfirmation(false)}
                            >
                              Cancel
                            </button>
                            <button
                              className="btn btn-danger"
                              onClick={handleConfirmClose}
                              style={{
                                backgroundColor: "#dc3545",
                                borderColor: "#dc3545",
                                color: "white",
                              }}
                            >
                              {recording || paused
                                ? "Stop Recording & Close"
                                : "Close"}
                            </button>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Upload Cancellation Confirmation Modal */}
                    {showUploadCancelConfirmation && (
                      <div
                        style={{
                          position: "fixed",
                          top: 0,
                          left: 0,
                          width: "100vw",
                          height: "100vh",
                          background: "rgba(0,0,0,0.5)",
                          zIndex: 10002,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          backdropFilter: "blur(4px)",
                        }}
                      >
                        <div
                          style={{
                            background: "white",
                            borderRadius: "16px",
                            padding: "32px 28px 24px 28px",
                            minWidth: "400px",
                            maxWidth: "95vw",
                            boxShadow: "0 8px 32px rgba(0,0,0,0.12)",
                            animation: "modalFadeIn 0.3s ease",
                            display: "flex",
                            flexDirection: "column",
                            gap: 18,
                          }}
                        >
                          <div
                            style={{
                              display: "flex",
                              justifyContent: "space-between",
                              alignItems: "center",
                              marginBottom: 8,
                            }}
                          >
                            <h3
                              style={{
                                margin: 0,
                                fontSize: "20px",
                                fontWeight: "600",
                                color: "#dc3545",
                              }}
                            >
                              ⚠️ Cancel Upload
                            </h3>
                          </div>

                          <div
                            style={{
                              fontSize: "16px",
                              color: "#2c3e50",
                              lineHeight: "1.5",
                            }}
                          >
                            <p>
                              <strong>
                                Upload and summarization is in progress.
                              </strong>
                              <br />
                              Are you sure you want to cancel the upload? This
                              will stop the current process and discard any
                              progress.
                            </p>
                          </div>

                          <div
                            style={{
                              display: "flex",
                              justifyContent: "flex-end",
                              gap: 12,
                              marginTop: 16,
                              borderTop: "1px solid #e2e8f0",
                              paddingTop: 16,
                            }}
                          >
                            <button
                              className="btn btn-light"
                              onClick={() =>
                                setShowUploadCancelConfirmation(false)
                              }
                            >
                              Continue Upload
                            </button>
                            <button
                              className="btn btn-danger"
                              onClick={handleConfirmUploadCancel}
                              style={{
                                backgroundColor: "#dc3545",
                                borderColor: "#dc3545",
                                color: "white",
                              }}
                            >
                              Cancel Upload
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                    <button
                      className="btn btn-warning"
                      onClick={handleOpenReportModal}
                      style={{ marginRight: "8px" }}
                    >
                      🐞 Report Issue
                    </button>

                  </div>
                </div>
                <VolumeMeter level={volumeLevel} visible={recording} />
                {/* {console.log('[VolumeMeter Debug] Rendering VolumeMeter with level:', volumeLevel, 'and visible:', recording)} */}
              </div>

              {/* Enhanced Audio Player - appears when not recording and audio exists */}
              {showEnhancedPlayer &&
                lastSavedAudio &&
                !recording &&

                <EnhancedAudioPlayer
                  audioFiles={lastSavedAudio}
                  isVisible={showEnhancedPlayer}
                  onClose={() => setShowEnhancedPlayer(false)}
                  sessionId={sessionId}
                  eventId={eventId}
                  API_BASE_URL={API_BASE_URL}
                  role={role}
                  selectedMics={selectedMics}
                />
              }

              {/* Live Multi-Mic Audio Player - appears during recording for admins */}
              <div
                style={{
                  width: "100%",
                  display: isAdmin(role) && recording ? "flex" : "none",
                  justifyContent: "center",
                  marginTop: 10,
                  marginBottom: 0,
                }}
              >
                <MultiMicAudioPlayer
                  selectedMics={selectedMics}
                  recording={recording}
                  paused={paused}
                  setAudioChunks={setAudioChunks}
                />
              </div>
              <div className="feedback-section-main">
                <div className={`main-panels-row ${!isAdmin(role) ? 'conversation-hidden' : ''}`}>
                  {/* Conversation Panel Group */}
                  <div
                    className="panel-group"
                    style={{ display: isAdmin(role) ? undefined : "none" }}
                  >
                    <div
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        margin: "20px 0 15px 0",
                      }}
                    >
                      <h2 className="panel-heading" style={{ margin: 0 }}>
                        Conversation
                      </h2>
                      <ConversationPanel.DownloadButtons
                        sessionId={sessionId}
                        recording={recording}
                        audioChunks={audioChunks}
                        onSaveAudio={handleSaveAudio}
                        onShowDownloadModal={async () => {
                          setShowDownloadModal(true);
                          await fetchRecordingFiles();
                        }}
                      />
                    </div>
                    <div className="conversation-panel">
                      <ConversationPanel
                        recording={recording}
                        transcriptionChunks={transcriptionChunks}
                        mergedTranscript={mergedTranscript}
                        sessionId={sessionId}
                      />
                    </div>
                  </div>

                  {/* AI Feedback Panel Group */}
                  <div className="panel-group">
                    <h2
                      className="panel-heading"
                      style={{ marginLeft: "25px" }}
                    >
                      AI Feedback
                    </h2>
                    <div className="ai-feedback-panel">
                      <AIFeedbackPanel
                        summary={summary}
                        setSummary={setSummary}
                        isLoading={uploadLoading}
                        enableRichEditor={true}
                        sessionId={sessionId}
                        patientId={currentPatientId}
                        eventId={eventId}
                        lastSavedAudio={lastSavedAudio}
                        uploadedFileInfo={uploadedFileInfo}
                        API_BASE_URL={API_BASE_URL}
                        disableDBUpdateOnCopy={disableDBUpdateOnCopy}
                        hideUntilContent={true}
                        showProlongedMessage={showProlongedMessage}
                      />
                      {/* <FeedbackInput /> */}
                    </div>
                  </div>
                </div>
              </div>

              <ExternalAudioService
                audioChunks={audioChunks}
                selectedMics={selectedMics}
                transcriptionChunks={transcriptionChunks}
                setTranscriptionChunks={setTranscriptionChunks}
                sessionId={sessionId}
                eventId={eventId}
                patientId={currentPatientId}
                language={language}
                isConnected={recording}
                setMergedTranscript={setMergedTranscript}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
});

export default ClinicalUIPopUp;
