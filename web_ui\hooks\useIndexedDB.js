import { useState, useEffect } from 'react';

/**
 * Custom hook for managing data in IndexedDB
 * @param {string} key - The key to store data under (currently not used in implementation)
 * @param {*} initialValue - The initial value to use if no data exists
 * @returns {[*, function]} - Returns [storedValue, setDBValue] similar to useState
 */
function useIndexedDB(key, initialValue) {
  const [storedValue, setStoredValue] = useState(initialValue);

  useEffect(() => {
    const dbReq = indexedDB.open('MicRoleDB', 1);
    
    dbReq.onupgradeneeded = () => {
      dbReq.result.createObjectStore('micRoles', { keyPath: 'deviceId' });
    };
    
    dbReq.onsuccess = () => {
      const db = dbReq.result;
      const tx = db.transaction('micRoles', 'readonly');
      const store = tx.objectStore('micRoles');
      const getAllReq = store.getAll();
      
      getAllReq.onsuccess = () => {
        setStoredValue(getAllReq.result.length ? getAllReq.result : initialValue);
      };
    };
  }, [key, initialValue]);

  const setDBValue = (value) => {
    setStoredValue(value);
    const dbReq = indexedDB.open('MicRoleDB', 1);
    
    dbReq.onsuccess = () => {
      const db = dbReq.result;
      const tx = db.transaction('micRoles', 'readwrite');
      const store = tx.objectStore('micRoles');
      
      if (Array.isArray(value)) {
        value.forEach(v => store.put(v));
      } else {
        console.warn('Expected an array but got:', value);
      }
    };
  };

  return [storedValue, setDBValue];
}

export default useIndexedDB;
