# Audio Merge Functionality Test Guide

## Overview
This document outlines how to test the new audio merging functionality in the ALaaS upload modal.

## Features Added

### 1. Upload Modal Enhancements
- **Title**: Changed from "Upload Audio Files" to "Upload & Merge Audio Files"
- **Button**: Changed from "Upload Audios" to "Upload & Merge Audios"
- **Role Assignment**: When exactly 2 files are selected, dropdown menus appear to assign roles (<PERSON><PERSON>/Doctor)
- **Merge Section**: Special UI section appears when 2 files are uploaded with merge controls

### 2. Role-Based File Assignment
- Each uploaded file (when exactly 2 files) gets a role dropdown
- Available roles: Patient, Doctor
- Both files must have roles assigned before merging is enabled

### 3. Merge Functionality
- **Merge Button**: Combines the two audio files into a single conversation
- **Download Button**: Appears after successful merge to download the merged audio
- **Loading States**: Shows spinner and "Merging..." text during processing

### 4. Backend API
- **Endpoint**: `/api/merge_audio`
- **Method**: POST with multipart form data
- **Input**: 2 audio files + role assignments
- **Output**: Merged WAV file with download URL

## Testing Steps

### Prerequisites
1. Ensure the backend server is running
2. Have 2 WAV or MP3 audio files ready for testing
3. Access the ALaaS application with admin role

### Test Procedure

1. **Open Upload Modal**
   - Click "Upload & Merge Audios" button
   - Verify modal opens with updated title

2. **Upload Two Files**
   - Click "Choose file(s)" and select exactly 2 audio files
   - Verify both files appear in the list
   - Verify role dropdowns appear for each file

3. **Assign Roles**
   - Set first file role to "Patient"
   - Set second file role to "Doctor"
   - Verify merge button becomes enabled

4. **Merge Audio**
   - Click "🔗 Merge Audio Files" button
   - Verify loading state shows "Merging..." with spinner
   - Wait for merge completion

5. **Download Merged Audio**
   - Verify "📥 Download Merged Audio" button appears
   - Click download button
   - Verify merged file downloads as "merged_conversation.wav"

6. **Test Edge Cases**
   - Try uploading only 1 file (merge section should not appear)
   - Try uploading 3+ files (merge section should not appear)
   - Try merging without assigning roles (button should be disabled)

## Expected Behavior

### UI States
- **0-1 files**: Normal upload interface, no merge section
- **2 files**: Role dropdowns appear, merge section visible
- **3+ files**: Normal upload interface, no merge section

### Merge Process
1. Files are uploaded to server
2. FFmpeg concatenates the files in order
3. Merged file is saved with timestamp
4. Download URL is returned to client
5. User can download the merged conversation

### Error Handling
- Invalid file formats show error toast
- Merge failures show error message
- Network errors are handled gracefully

## File Structure Changes

### Frontend Files Modified
- `web_ui/ClinicalUIPopUp.jsx`: Main UI component with merge functionality
- `web_ui/services/audioService.js`: Added `mergeAudioFiles` function
- `web_ui/App.css`: Added spinner animation

### Backend Files Modified
- `web_ui/server.js`: Added `/api/merge_audio` endpoint and `mergeAudioWithRoles` helper

## Dependencies
- **FFmpeg**: Required on server for audio merging
- **Multer**: For file upload handling
- **React Hot Toast**: For user notifications

## Notes
- Merged files are saved in the recordings directory
- File naming convention: `merged_conversation_[timestamp].wav`
- Temporary file lists are created and cleaned up during merge process
- Only WAV and MP3 formats are supported for merging
