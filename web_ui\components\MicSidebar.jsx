import React from 'react';
import DSPControls from './DSPControls.jsx';

function MicSidebar({ mics, micRoles, setMicRoles, dspConfigs, setDSPConfigs, onRefresh, onCollapse }) {
  const ROLES = ['Doctor', 'Patient', 'Nurse', 'Family', 'Other'];
  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <span className="sidebar-title">Microphones</span>
        <button className="refresh-btn" onClick={onRefresh} title="Refresh mic list">⟳</button>
        <button className="sidebar-toggle" onClick={onCollapse} title="Collapse menu">←</button>
      </div>
      <div className="sidebar-mic-list">
        {mics.length === 0 && <div className="sidebar-mic-none">No microphones found</div>}
        {mics.map(mic => {
          const selected = micRoles.find(m => m.deviceId === String(mic.id));
          const dsp = dspConfigs[mic.id] || {};
          return (
            <div className="sidebar-mic-row" key={mic.id}>
              <input
                type="checkbox"
                checked={!!selected}
                onChange={e => {
                  const updated = micRoles.filter(m => m.deviceId !== String(mic.id));
                  if (e.target.checked) {
                    updated.push({ deviceId: String(mic.id), role: selected ? selected.role : ROLES[0] });
                  }
                  setMicRoles(updated);
                }}
              />
              <span className="sidebar-mic-label">{mic.name || `Mic ${mic.id}`}</span>
              {selected && (
                <select
                  className="sidebar-role-select"
                  value={selected.role}
                  onChange={e => {
                    const updated = micRoles.map(m => m.deviceId === String(mic.id) ? { ...m, role: e.target.value } : m);
                    setMicRoles(updated);
                  }}
                >
                  {ROLES.map(role => <option key={role} value={role}>{role}</option>)}
                </select>
              )}
              {/* DSP Controls Accordion */}
              {selected && (
                <div style={{ width: '100%' }}>
                  <details>
                    <summary style={{ fontWeight: 600, marginTop: 4 }}>🎚 DSP Controls</summary>
                    <div style={{ padding: '4px 0' }}>
                      <DSPControls
                        dsp={dspConfigs[mic.id] || {
                          noiseGate: { threshold: 0.1, attack: 0.01, hold: 0.01, release: 0.01, ratio: 2 },
                          compressor: { threshold: 0.1, attack: 0.01, hold: 0.01, release: 0.05, ratio: 2 },
                          limiter: { threshold: 0.39, ratio: 10 },
                          eq: {
                            lowCutFreq: 400,        // updated from 1000 or 20
                            lowCutSlope: 18,        // updated from 6
                            highCutFreq: 10000,     // updated from 2000
                            highCutSlope: 18,       // updated from 6
                            mid1Freq: 500,
                            mid1Q: 1,
                            mid2Freq: 4000,
                            mid2Q: 1
                          }
                        }}
                        onChange={(section, param, value) => setDSPConfigs(prev => ({
                          ...prev,
                          [mic.id]: {
                            ...((prev[mic.id]) || {}),
                            [section]: {
                              ...(((prev[mic.id] || {})[section]) || {}),
                              [param]: value
                            }
                          }
                        }))}
                      />
                    </div>
                  </details>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}

export default MicSidebar;
