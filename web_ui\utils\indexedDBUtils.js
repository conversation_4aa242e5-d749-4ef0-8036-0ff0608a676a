/**
 * Utility functions for managing IndexedDB operations
 */

/**
 * Clear all micRoles and DSP configs from IndexedDB
 * This function clears the 'micRoles' object store in the 'MicRoleDB' database
 */
export function clearIndexedDB() {
  const dbReq = indexedDB.open('MicRoleDB', 1);
  
  dbReq.onsuccess = () => {
    const db = dbReq.result;
    const tx = db.transaction('micRoles', 'readwrite');
    const store = tx.objectStore('micRoles');
    store.clear();
  };
  
  // Add similar logic for DSP configs if stored in a separate store
  // This can be extended in the future if DSP configs are stored separately
}
