services:
  ui:
    build:
      context: .
      dockerfile: Dockerfile.ui
      args:
      - VITE_API_BASE_URL=${VITE_API_BASE_URL}
      - VITE_EMR_BEARER_TOKEN=${VITE_EMR_BEARER_TOKEN}
      - VITE_AUDIO_AGENT_BASE_URL=${VITE_AUDIO_AGENT_BASE_URL}
    ports:
      - "5173:5173"
    env_file:
      - .env
    environment:
      - SSL_CERT_KEY=/certs/localhost.key
      - SSL_CERT=/certs/localhost.cert
    volumes:
      - /usr/src/app/node_modules
      - ./recordings_web:/app/recordings_web
      - /etc/ssl/certs:/certs
    restart: unless-stopped
    depends_on: []
    networks:
      - webnet
  api:
    build:
      context: .
      dockerfile: Dockerfile.api
    ports:
      - "4000:4000"
    env_file:
      - .env
    environment:
      - SSL_CERT_KEY=/certs/localhost.key
      - SSL_CERT=/certs/localhost.cert
    volumes:
      - /usr/src/app/node_modules
      - ./recordings_web:/app/recordings_web
      - /etc/ssl/certs:/certs
    restart: unless-stopped
    networks:
      - webnet
  postgres:
    image: postgres:15
    container_name: arca_postgres
    restart: unless-stopped
    environment:
      - POSTGRES_USER=${DATABASE_USER}
      - POSTGRES_PASSWORD=${DATABASE_PASSWORD}
      - POSTGRES_DB=${DATABASE_NAME}
    ports:
      - "5433:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
    networks:
      - webnet
volumes:
  pgdata:
    driver: local
networks:
  webnet:
    driver: bridge
