import React, { useRef, useEffect, useState } from 'react';
import './AudioWaveform.css';
import { FaPlay, FaPause, FaVolumeUp, FaVolumeMute, FaCog, FaStepBackward, FaStepForward } from 'react-icons/fa';

const AudioWaveform = ({ micId, micName, role, audioData, isPlaying, isMuted, onPlay, onPause, onMute }) => {
  const canvasRef = useRef(null);
  const [volume, setVolume] = useState(0.8);
  const animationRef = useRef(null);
  const bufferRef = useRef([]);
  const [peakLevel, setPeakLevel] = useState(-60);
  const [currentPosition, setCurrentPosition] = useState(0);
  const [duration, setDuration] = useState(30); // Default 30 seconds
  const [showSettings, setShowSettings] = useState(false);

  useEffect(() => {
    if (audioData && audioData.length > 0) {
      bufferRef.current = [...bufferRef.current, ...audioData];

      if (bufferRef.current.length > 10000) {
        bufferRef.current = bufferRef.current.slice(bufferRef.current.length - 10000);
      }
      
      const peak = Math.max(...audioData.map(Math.abs));
      const dbLevel = peak > 0 ? 20 * Math.log10(peak) : -60;
      setPeakLevel(Math.max(dbLevel, -60));
    }
  }, [audioData]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const width = canvas.width;
    const height = canvas.height;
    
    const drawWaveform = () => {
      ctx.clearRect(0, 0, width, height);
      
      ctx.fillStyle = '#f5f5f5';
      ctx.fillRect(0, 0, width, height);
      
      ctx.strokeStyle = '#e0e0e0';
      ctx.lineWidth = 1;
      
      ctx.beginPath();
      ctx.moveTo(0, height / 2);
      ctx.lineTo(width, height / 2);
      ctx.stroke();
      
      for (let x = 0; x < width; x += 50) {
        ctx.beginPath();
        ctx.moveTo(x, 0);
        ctx.lineTo(x, height);
        ctx.stroke();
      }
      
      const buffer = bufferRef.current;
      if (buffer.length === 0) return;
      
      const step = Math.max(1, Math.floor(buffer.length / width));
      const amp = (height / 2) * 0.9;
      
      ctx.strokeStyle = isPlaying ? (isMuted ? '#999999' : '#1a9c5e') : '#666666';
      ctx.lineWidth = 2;
      ctx.beginPath();
      
      for (let i = 0; i < width; i++) {
        const idx = Math.min(buffer.length - 1, Math.floor(i * step));
        const y = (buffer[idx] * amp) + (height / 2);
        if (i === 0) {
          ctx.moveTo(i, y);
        } else {
          ctx.lineTo(i, y);
        }
      }
      
      ctx.stroke();
      
      animationRef.current = requestAnimationFrame(drawWaveform);
    };
    
    drawWaveform();
    
    return () => {
      cancelAnimationFrame(animationRef.current);
    };
  }, [isPlaying, isMuted]);

  const getVolumeColor = () => {
    if (peakLevel > -3) return '#ff0000';
    if (peakLevel > -9) return '#ff9900';
    if (peakLevel > -18) return '#1a9c5e';
    return '#666666';
  };

  const getVolumeHeight = () => {
    const percentage = ((peakLevel + 60) / 60) * 100;
    return `${Math.max(0, Math.min(100, percentage))}%`;
  };

  // Format time in MM:SS format
  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Handle seeking in the audio
  const handleSeek = (e) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const seekPosition = (x / rect.width) * duration;
    setCurrentPosition(seekPosition);
    
    // In a real implementation, this would seek to the position in the audio
    console.log(`Seeking to ${seekPosition} seconds`);
  };

  return (
    <div className="audio-waveform">
      <div className="waveform-header">
        <div className="waveform-info">
          <span className="waveform-name">{micName || `Mic ${micId}`}</span>
          <span className="waveform-role">{role}</span>
        </div>
        <div className="waveform-controls">
          <button 
            className="control-button"
            onClick={() => setCurrentPosition(Math.max(0, currentPosition - 5))}
            title="Back 5 seconds"
          >
            <FaStepBackward />
          </button>
          <button 
            className="control-button"
            onClick={isPlaying ? onPause : onPlay}
            title={isPlaying ? "Pause" : "Play"}
          >
            {isPlaying ? <FaPause /> : <FaPlay />}
          </button>
          <button 
            className="control-button"
            onClick={() => setCurrentPosition(Math.min(duration, currentPosition + 5))}
            title="Forward 5 seconds"
          >
            <FaStepForward />
          </button>
          <button 
            className="control-button"
            onClick={onMute}
            title={isMuted ? "Unmute" : "Mute"}
          >
            {isMuted ? <FaVolumeMute /> : <FaVolumeUp />}
          </button>
          <div className="volume-slider-container">
            <input
              type="range"
              min="0"
              max="1"
              step="0.01"
              value={volume}
              onChange={(e) => setVolume(parseFloat(e.target.value))}
              className="volume-slider"
            />
          </div>
          <div className="volume-meter-container">
            <div 
              className="volume-meter" 
              style={{ 
                height: getVolumeHeight(),
                backgroundColor: getVolumeColor()
              }}
            ></div>
          </div>
          <button 
            className="control-button settings-button"
            onClick={() => setShowSettings(!showSettings)}
            title="Audio Settings"
          >
            <FaCog />
          </button>
        </div>
      </div>
      <div className="waveform-canvas-container" onClick={handleSeek}>
        <canvas 
          ref={canvasRef} 
          width={800} 
          height={100}
          className="waveform-canvas"
        ></canvas>
        <div 
          className="playhead" 
          style={{ left: `${(currentPosition / duration) * 100}%` }}
        ></div>
      </div>
      <div className="waveform-timeline">
        <span className="time current-time">{formatTime(currentPosition)}</span>
        <div className="timeline-progress">
          <div 
            className="timeline-progress-bar" 
            style={{ width: `${(currentPosition / duration) * 100}%` }}
          ></div>
        </div>
        <span className="time total-time">{formatTime(duration)}</span>
      </div>
      {showSettings && (
        <div className="audio-settings-panel">
          <h4>Audio Settings</h4>
          <div className="setting-group">
            <label>Gain</label>
            <input type="range" min="0" max="2" step="0.1" defaultValue="1" />
          </div>
          <div className="setting-group">
            <label>Filter Low</label>
            <input type="range" min="20" max="500" step="10" defaultValue="80" />
          </div>
          <div className="setting-group">
            <label>Filter High</label>
            <input type="range" min="1000" max="20000" step="100" defaultValue="12000" />
          </div>
        </div>
      )}
    </div>
  );
};

export default AudioWaveform;
