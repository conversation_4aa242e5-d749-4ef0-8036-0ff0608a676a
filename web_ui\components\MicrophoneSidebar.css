.microphone-sidebar {
	position: fixed;
	top: 0;
	left: 0;
	height: 100vh;
	background-color: white;
	box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
	transition: transform 0.3s ease;
	display: flex;
	flex-direction: column;
	z-index: 1000;
	width: 320px;
	overflow-y: auto;
  }
  
  .microphone-sidebar.open {
	transform: translateX(0);
  }
  
  .microphone-sidebar.closed {
	transform: translateX(-100%);
  }
  
  .sidebar-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16px;
	border-bottom: 1px solid #eaeaea;
	background-color: #f8f8f8;
  }
  
  .sidebar-header h2 {
	margin: 0;
	font-size: 1.2rem;
	color: #333;
  }
  
  .sidebar-actions {
	display: flex;
	gap: 8px;
  }
  
  .refresh-button,
  .toggle-button {
	background: none;
	border: none;
	width: 32px;
	height: 32px;
	border-radius: 4px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	color: #555;
	transition: background-color 0.2s ease;
  }
  
  .refresh-button:hover,
  .toggle-button:hover {
	background-color: #e0e0e0;
  }
  
  .microphone-list {
	flex: 1;
	overflow-y: auto;
	padding: 16px;
	display: flex;
	flex-direction: column;
	gap: 12px;
  }
  
  .no-mics-message {
	color: #888;
	text-align: center;
	padding: 20px;
	font-style: italic;
  }
  
  .microphone-item {
	border: 1px solid #eaeaea;
	border-radius: 8px;
	overflow: hidden;
	background-color: #f9f9f9;
	margin-bottom: 12px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
	transition: all 0.2s ease;
  }
  
  .microphone-item:hover {
	border-color: #1a9c5e;
	box-shadow: 0 2px 8px rgba(26, 156, 94, 0.2);
  }
  
  .microphone-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 12px;
	background-color: white;
  }
  
  .microphone-selection {
	display: flex;
	align-items: center;
	gap: 8px;
	flex-wrap: wrap;
  }
  
  .mic-icon {
	color: #1a9c5e;
	margin-right: 4px;
  }
  
  .test-mic-button {
	background-color: #f0f0f0;
	border: 1px solid #ddd;
	border-radius: 4px;
	padding: 4px 8px;
	font-size: 0.8rem;
	cursor: pointer;
	transition: all 0.2s ease;
	margin-left: auto;
  }
  
  .test-mic-button:hover {
	background-color: #e0e0e0;
  }
  
  .test-mic-button.testing {
	background-color: #ff5252;
	color: white;
	border-color: #ff5252;
  }
  
  .volume-meter-container {
	height: 8px;
	background-color: #f0f0f0;
	border-radius: 4px;
	margin: 8px 0;
	overflow: hidden;
	width: 100%;
  }
  
  .volume-meter {
	height: 100%;
	background-color: #1a9c5e;
	border-radius: 4px;
	transition: width 0.1s ease;
  }
  
  .microphone-selection input[type="checkbox"] {
	width: 16px;
	height: 16px;
  }
  
  .microphone-selection label {
	font-size: 0.95rem;
	font-weight: 500;
	color: #333;
  }
  
  .microphone-controls {
	display: flex;
	align-items: center;
	gap: 8px;
  }
  
  .role-select {
	padding: 4px 8px;
	border: 1px solid #ddd;
	border-radius: 4px;
	font-size: 0.85rem;
	background-color: white;
  }
  
  .expand-button {
	background-color: #f0f0f0;
	border: none;
	border-radius: 4px;
	padding: 4px 8px;
	font-size: 0.8rem;
	cursor: pointer;
	transition: background-color 0.2s ease;
  }
  
  .expand-button:hover {
	background-color: #e0e0e0;
  }
  
  .dsp-settings-container {
	padding: 12px;
	border-top: 1px solid #eaeaea;
	background-color: #f5f5f5;
  }

  /* Responsive adjustments for extra small screens */
  @media (max-width: 480px) {
	.microphone-sidebar {
	  width: 280px; /* Slightly smaller on xs */
	}

	.sidebar-header {
	  padding: 12px;
	}

	.sidebar-header h2 {
	  font-size: 1.1rem;
	}

	.sidebar-actions {
	  gap: 6px;
	}

	.refresh-button,
	.toggle-button {
	  width: 36px;
	  height: 36px;
	  font-size: 1.1rem;
	}

	.refresh-button:hover,
	.toggle-button:hover {
	  background-color: #d0d0d0;
	}

	.microphone-list {
	  padding: 12px;
	}

	.microphone-item {
	  margin-bottom: 10px;
	}

	.microphone-header {
	  padding: 10px;
	}
  }
  