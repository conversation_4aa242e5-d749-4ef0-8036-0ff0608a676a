import React, { useMemo, useRef, useEffect, useState } from 'react';
import ReactMarkdown from 'react-markdown';
import RichTextEditor from './RichTextEditor.jsx';
import './AIFeedbackPanel.css';

// Helper function to format file size
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Extracts SOAP/medical fields from a summary string (simple regex/section split)
function parseMedicalSummary(summary) {
  if (!summary) return {};
  // Try to split by common SOAP/section headers
  const fields = [
    'presentingcomplaint',
    'historyofpresenting',
    'familyhistory',
    'treatmenthistory',
    'generalexamination',
    'examination',
    'assessment',
    'plan',
    'diagnosis',
    'subjective',
    'objective',
    'soap',
    'summary',
  ];
  const result = {};
  let current = null;
  summary.split(/\n|\r/).forEach(line => {
    const l = line.trim().toLowerCase();
    const found = fields.find(f => l.startsWith(f));
    if (found) {
      current = found;
      result[current] = line.trim().replace(/^[^:]*:?/, '').trim();
    } else if (current && line.trim()) {
      result[current] += '\n' + line.trim();
    }
  });
  return result;
}

function AIFeedbackPanel({ summary, setSummary, isLoading = false, enableRichEditor = false, sessionId, patientId, eventId, lastSavedAudio, uploadedFileInfo, API_BASE_URL, disableDBUpdateOnCopy, hideUntilContent = false, showProlongedMessage = false }) {
  // Track whether a summary was ever received to prevent showing placeholder after editing to empty
  const summaryEverReceived = useRef(false);

  // Memoize parsed fields for performance
  const parsed = useMemo(() => parseMedicalSummary(summary), [summary]);

  // Update the flag when we receive a non-empty summary
  useEffect(() => {
    if (summary && summary.trim() !== '') {
      summaryEverReceived.current = true;
    }
  }, [summary]);

  // Debug: log uploaded file info
  console.log('[AIFeedbackPanel] uploadedFileInfo:', uploadedFileInfo);
  // Debug: log the summary and parsed fields
  // console.log('[AIFeedbackPanel] summary prop:', summary);
  // console.log('[AIFeedbackPanel] parsed fields:', parsed);
  const fieldOrder = [
    'presentingcomplaint',
    'historyofpresenting',
    'familyhistory',
    'treatmenthistory',
    'generalexamination'
  ];
  console.log(showProlongedMessage,'showProlongedMessage');
  
  // console.log('RichTextEditor API_BASE_URL in ai:', API_BASE_URL);
  // Check if any field has a value
  const hasAnyValue = fieldOrder.some((field) => parsed[field] && parsed[field].trim());

  // State to track if the rich text editor is being edited
  const [isEditing, setIsEditing] = useState(false);

  // Handle editing state changes from RichTextEditor
  const handleEditingStateChange = (editingState) => {
    setIsEditing(editingState);
  };

  // Check if we should show a placeholder when hiding until content
  const hasContent = summary && summary.trim() !== '';
  // Only show placeholder if we've never received a summary and there's no content and not loading
  const shouldShowPlaceholder = hideUntilContent && !hasContent && !isLoading && !summaryEverReceived.current&& !isEditing;

  return (
    <div style={{ position: 'relative' }}>
      {isLoading && (
        <div className="ai-feedback-loading-overlay">
          <div className="ai-feedback-spinner" />
          <span style={{ marginTop: 12, color: '#555', fontWeight: 500 }}>Generating summary...</span>
          {showProlongedMessage && (
            <div style={{
              marginTop: 16,
              padding: '12px 16px',
              backgroundColor: '#fff3cd',
              border: '1px solid #ffeaa7',
              borderRadius: '6px',
              color: '#856404',
              fontSize: '14px',
              lineHeight: '1.4',
              maxWidth: '400px',
              textAlign: 'center'
            }}>
              This is taking longer than expected. You can continue with your next patient—your summary will be available under View AI Data when ready.
            </div>
          )}
        </div>
      )}

      {shouldShowPlaceholder ? (
        <div style={{
          padding: '40px 20px',
          textAlign: 'center',
          backgroundColor: '#f8f9fa',
          border: '2px dashed #dee2e6',
          borderRadius: '8px',
          color: '#6c757d'
        }}>
          <div style={{ fontSize: '48px', marginBottom: '16px', opacity: 0.5 }}>🤖</div>
          <h3 style={{ margin: '0 0 8px 0', fontSize: '18px', fontWeight: '600' }}>
            AI Summary Not Available
          </h3>
          <p style={{ margin: '0', fontSize: '14px', lineHeight: '1.5' }}>
            Summarization starts automatically after stopping the recording or uploading audio.
            <br />
            Click <strong>Get Summary</strong> to display the AI-generated medical summary.
          </p>

        </div>
      ) : (
        <div className="feedback-section" style={isLoading ? { opacity: 0.5, pointerEvents: 'none' } : {}}>
          {/* Display uploaded file info if available */}
          {uploadedFileInfo && (
            <div style={{
              backgroundColor: '#f8f9fa',
              border: '1px solid #dee2e6',
              borderRadius: '6px',
              padding: '12px',
              marginBottom: '16px',
              fontSize: '13px'
            }}>
              <div style={{ fontWeight: '600', marginBottom: '8px', color: '#495057' }}>
                📁 Audio File Information
              </div>

              {/* Display each file with its metadata */}
              {uploadedFileInfo.files ? (
                // New format with detailed file info
                uploadedFileInfo.files.map((file, index) => (
                  <div key={index} style={{
                    marginBottom: index < uploadedFileInfo.files.length - 1 ? '8px' : '4px',
                    paddingLeft: '8px',
                    borderLeft: '3px solid #007bff',
                    backgroundColor: '#ffffff',
                    padding: '8px',
                    borderRadius: '4px'
                  }}>
                    <div style={{ fontWeight: '500', marginBottom: '4px' }}>
                      {file.name}
                    </div>
                    <div style={{ fontSize: '12px', color: '#6c757d' }}>
                      <strong>Size:</strong> {formatFileSize(file.size)}
                    </div>
                  </div>
                ))
              ) : uploadedFileInfo.fileNames ? (
                // Backward compatibility for old format
                <div style={{ marginBottom: '4px' }}>
                  <strong>Files:</strong> {uploadedFileInfo.fileNames.join(', ')}
                </div>
              ) : null}

              <div style={{ marginTop: '8px', marginBottom: '4px' }}>
                <strong>Language:</strong> {uploadedFileInfo.language}
              </div>
              <div style={{ color: '#6c757d', fontSize: '12px' }}>
                <strong>Uploaded:</strong> {new Date(uploadedFileInfo.uploadTimestamp).toLocaleString()}
              </div>
            </div>
          )}

          {hasAnyValue ? (
            <div>
              {fieldOrder.map((field) => (
                <div key={field}>
                  <b>{field}</b><br />
                  <div style={{ marginBottom: 8 }}>
                    {parsed[field] ? (
                      <ReactMarkdown>{parsed[field]}</ReactMarkdown>
                    ) : (
                      <span style={{ color: '#888' }}>No data</span>
                    )}
                  </div>
                </div>
              ))}

              {/* Rich Text Editor for the full summary - only in main popup */}
              {enableRichEditor && (
                <div style={{ marginTop: 16 }}>
                  <RichTextEditor
                    content={summary}
                    onContentChange={setSummary}
                    isLoading={isLoading}
                    placeholder="No summary available..."
                    readOnly={false}
                    sessionId={sessionId}
                    patientId={patientId}
                    eventId={eventId}
                    lastSavedAudio={lastSavedAudio}
                    API_BASE_URL={API_BASE_URL}
                    disableDBUpdateOnCopy={disableDBUpdateOnCopy}
                    hideUntilContent={hideUntilContent}
                    onEditingStateChange={handleEditingStateChange}
                  />
                </div>
              )}
            </div>
          ) : (
            <div>
              {enableRichEditor ? (
                /* Use Rich Text Editor for raw summary - only in main popup */
                <RichTextEditor
                  content={summary}
                  onContentChange={setSummary}
                  isLoading={isLoading}
                  placeholder="No summary available..."
                  readOnly={false}
                  sessionId={sessionId}
                  patientId={patientId}
                  eventId={eventId}
                  lastSavedAudio={lastSavedAudio}
                  API_BASE_URL={API_BASE_URL}
                  disableDBUpdateOnCopy={disableDBUpdateOnCopy}
                  hideUntilContent={hideUntilContent}
                  onEditingStateChange={handleEditingStateChange}
                />
              ) : (
                /* Use simple display for AI Data modal */
                  <div style={{ background: '#f5f5f5', borderRadius: 6, padding: 8 }}>
                    <ReactMarkdown>{summary || 'No summary available.'}</ReactMarkdown>
                  </div>
                
              )}
            </div>
          )}
        </div>
      )}
    </div>
  );
}

export default AIFeedbackPanel;
