# Dockerfile for React UI (Production)
FROM node:20-slim AS build

WORKDIR /app
COPY package.json pnpm-lock.yaml ./
RUN npm install -g pnpm && pnpm install --frozen-lockfile
COPY . .
ARG VITE_API_BASE_URL
ARG VITE_EMR_BEARER_TOKEN
ARG VITE_AUDIO_AGENT_BASE_URL
ENV VITE_API_BASE_URL=$VITE_API_BASE_URL
ENV VITE_EMR_BEARER_TOKEN=$VITE_EMR_BEARER_TOKEN
ENV VITE_AUDIO_AGENT_BASE_URL=$VITE_AUDIO_AGENT_BASE_URL
RUN pnpm run build

# Serve with Vite preview (or use nginx if you prefer)
FROM node:20-slim AS prod
WORKDIR /app
COPY --from=build /app/dist ./dist
COPY --from=build /app/package.json ./
COPY --from=build /app/node_modules ./node_modules
COPY vite.config.js /app/vite.config.js
RUN npm install -g vite
EXPOSE 5173
CMD ["vite", "preview", "--port", "5173", "--host","0.0.0.0"]
