import WaveSurfer from 'wavesurfer.js';
import React, { useEffect, useRef,useState } from 'react';
import AudioWaveform from './Audiowaveform.jsx';
import authService from '../services/authService.js';



// import MicrophoneSidebar from './components/MicrophoneSidebar.jsx';
// import MicrophoneAccess from './components/MicrophoneAccess.jsx';
import { FaPlay, FaStop, FaFileAlt, FaTimes, FaBars, FaMicrophone, FaSpinner } from 'react-icons/fa';

export default function AudioPlayerSection({ selectedMics, recording, }) {
  const [audioStreams, setAudioStreams] = useState({});
  const wsRef = useRef(null);
  const audioCtxRef = useRef(null);
  
  // Initialize audio context and WebSocket connection when recording starts
  useEffect(() => {
    if (recording) {
      // Initialize AudioContext if not already created
      if (!audioCtxRef.current) {
        try {
          audioCtxRef.current = new (window.AudioContext || window.webkitAudioContext)({ sampleRate: 48000 });
        } catch (e) {
          console.error('Failed to create AudioContext:', e);
        }
      }
      
      // Initialize WebSocket connection for audio streaming
      const initWebSocket = async () => {
        // Ensure we have a valid token
        if (!authService.isAuthenticated()) {
          await authService.login();
        }
        const token = authService.getToken();
        const ws = new WebSocket(`ws://localhost:5001/audio_stream?token=${encodeURIComponent(token)}`);
        ws.binaryType = 'arraybuffer';
        return ws;
      };

      initWebSocket().then(ws => {
        wsRef.current = ws;

        ws.onopen = () => {
          console.log('Audio WebSocket connected');
        };

        ws.onmessage = (event) => {
          try {
            const data = new DataView(event.data);
            const micId = data.getUint8(0);
            const sampleRate = data.getUint32(1, false);
            const pcmData = new Float32Array(event.data.slice(5));

            setAudioStreams(prev => {
              const micKey = String(micId);
              const currentBuffer = prev[micKey]?.buffer || [];

              const newBuffer = [...currentBuffer, ...pcmData];
              if (newBuffer.length > 10000) {
                newBuffer.splice(0, newBuffer.length - 10000);
              }

              return {
                ...prev,
                [micKey]: {
                  ...prev[micKey],
                  buffer: newBuffer,
                  sampleRate,
                  lastUpdate: Date.now()
                }
              };
            });
          } catch (e) {
            console.error('Error processing audio data:', e);
          }
        };

        ws.onerror = (error) => {
          console.error('Audio WebSocket error:', error);
        };

        ws.onclose = () => {
          console.log('Audio WebSocket disconnected');
        };
      }).catch(error => {
        console.error('Failed to initialize WebSocket:', error);
      });

      return () => {
        if (wsRef.current) {
          wsRef.current.close();
        }
      };
    } else {
      if (wsRef.current) {
        wsRef.current.close();
        wsRef.current = null;
      }
    }
  }, [recording]);
  
  const handlePlay = (micId) => {
    setAudioStreams(prev => ({
      ...prev,
      [micId]: { ...prev[micId], isPlaying: true }
    }));
  };
  
  const handlePause = (micId) => {
    setAudioStreams(prev => ({
      ...prev,
      [micId]: { ...prev[micId], isPlaying: false }
    }));
  };
  
  const handleMute = (micId) => {
    setAudioStreams(prev => ({
      ...prev,
      [micId]: { ...prev[micId], isMuted: !prev[micId]?.isMuted }
    }));
  };
  
  const playTestTone = () => {
    let ctx = audioCtxRef.current;
    if (!ctx) {
      ctx = new (window.AudioContext || window.webkitAudioContext)();
      audioCtxRef.current = ctx;
    }
    
    const duration = 1;
    const sampleRate = 48000;
    const buffer = ctx.createBuffer(1, sampleRate * duration, sampleRate);
    const data = buffer.getChannelData(0);
    
    for (let i = 0; i < data.length; i++) {
      data[i] = Math.sin(2 * Math.PI * 440 * (i / sampleRate));
    }
    
    const src = ctx.createBufferSource();
    src.buffer = buffer;
    src.connect(ctx.destination);
    src.start();
    src.onended = () => src.disconnect();
  };

  return (
    <div className="audio-player-section">
      <div className="audio-player-header">
        <h2>Audio Streams</h2>
        <button className="test-tone-button" onClick={playTestTone}>Play Test Tone</button>
      </div>
      
      <div className="audio-waveforms">
        {selectedMics.length === 0 && (
          <div className="no-mics-selected">
            No microphones selected. Please select microphones from the sidebar.
          </div>
        )}
        
        {selectedMics.map(mic => {
          const micId = String(mic.device);
          const audioData = audioStreams[micId]?.buffer || [];
          const isPlaying = audioStreams[micId]?.isPlaying !== false;
          const isMuted = audioStreams[micId]?.isMuted || false;
          
          return (
            <AudioWaveform
              key={micId}
              micId={micId}
              micName={mic.device}
              role={mic.role}
              audioData={audioData}
              isPlaying={isPlaying}
              isMuted={isMuted}
              onPlay={() => handlePlay(micId)}
              onPause={() => handlePause(micId)}
              onMute={() => handleMute(micId)}
            />
          );
        })}
      </div>
    </div>
  );
}