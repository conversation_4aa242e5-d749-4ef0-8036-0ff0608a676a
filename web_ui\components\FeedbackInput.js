import React, { useState } from 'react';
import './FeedbackInput.css';

function FeedbackInput() {
  const [feedback, setFeedback] = useState('');
  const [rating, setRating] = useState(0);

  return (
    <div className="feedback-input-row">
      <textarea
        className="feedback-textarea"
        placeholder="Share your feedback"
        value={feedback}
        onChange={e => setFeedback(e.target.value)}
      />
      <div className="star-rating">
        {[1,2,3,4,5].map(star => (
          <span
            key={star}
            className={star <= rating ? 'star filled' : 'star'}
            onClick={() => setRating(star)}
          >★</span>
        ))}
      </div>
      <button className="btn-submit">Submit</button>
    </div>
  );
}

export default FeedbackInput;
