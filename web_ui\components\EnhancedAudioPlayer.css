.enhanced-audio-player {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 12px;
  margin: 8px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  max-width: 100%;
  animation: slideIn 0.3s ease-out;
  transition: box-shadow 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced shadow when expanded */
.enhanced-audio-player:has(.collapsible-content.expanded) {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.audio-player-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid #dee2e6;
}

.player-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.player-title h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 14px;
  font-weight: 600;
}

.file-counter {
  background: #007bff;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.expand-collapse-toggle {
  background: none;
  border: none;
  font-size: 16px;
  color: #6c757d;
  cursor: pointer;
  padding: 6px 8px;
  border-radius: 6px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  position: relative;
}

.expand-collapse-toggle:hover {
  background: #f8f9fa;
  color: #495057;
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.expand-collapse-toggle:active {
  transform: scale(0.95);
  transition: all 0.1s ease;
}

/* Icon rotation animation */
.expand-collapse-toggle svg {
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.expand-collapse-toggle:hover svg {
  transform: scale(1.1);
}

/* Subtle pulse animation when collapsed to hint interactivity */
.enhanced-audio-player:has(.collapsible-content.collapsed) .expand-collapse-toggle {
  animation: subtlePulse 3s ease-in-out infinite;
}

@keyframes subtlePulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(108, 117, 125, 0.2);
  }
  50% {
    box-shadow: 0 0 0 4px rgba(108, 117, 125, 0.1);
  }
}

/* Collapsible content animation - SLOW MOTION */
.collapsible-content {
  overflow: hidden;
  transition:
    max-height 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    opacity 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    padding 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    margin 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    transform 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.collapsible-content.expanded {
  max-height: 1200px; /* Increased for larger content */
  opacity: 1;
  padding-top: 8px;
}

.collapsible-content.collapsed {
  max-height: 0;
  opacity: 0;
  padding-top: 0;
  margin-top: 0;
}

/* Staggered animation for content elements */
.collapsible-content.expanded .file-selector {
  animation: fadeInUp 0.5s ease-out 0.1s both;
}

.collapsible-content.expanded .waveform-container {
  animation: fadeInUp 0.5s ease-out 0.2s both;
}

.collapsible-content.expanded .time-display {
  animation: fadeInUp 0.5s ease-out 0.3s both;
}

.collapsible-content.expanded .main-controls {
  animation: fadeInUp 0.5s ease-out 0.4s both;
}

.collapsible-content.expanded .secondary-controls {
  animation: fadeInUp 0.5s ease-out 0.5s both;
}

/* Fade in up animation keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.file-selector {
  display: flex;
  gap: 4px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

.file-tab {
  background: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.file-tab:hover {
  background: #dee2e6;
}

.file-tab.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.waveform-container {
  position: relative;
  height: 40px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin: 8px 0;
  cursor: pointer;
  overflow: hidden;
  display: flex;
  align-items: center;
  padding: 4px;
}

.waveform-bars {
  display: flex;
  align-items: center;
  height: 100%;
  width: 100%;
  gap: 1px;
}

.waveform-bar {
  flex: 1;
  min-height: 2px;
  border-radius: 1px;
  transition: background-color 0.1s ease;
}

.progress-indicator {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 2px;
  background: #dc3545;
  border-radius: 1px;
  pointer-events: none;
  transition: left 0.1s ease;
}

.time-display {
  display: flex;
  justify-content: center;
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 8px;
  font-family: 'Courier New', monospace;
}

.main-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.control-btn {
  background: #fff;
  border: 1px solid #dee2e6;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  color: #495057;
}

.control-btn:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.control-btn:active {
  transform: translateY(0);
}

.control-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.play-btn {
  width: 40px;
  height: 40px;
  background: #007bff;
  color: white;
  border-color: #007bff;
  font-size: 14px;
}

.play-btn:hover {
  background: #0056b3;
  border-color: #0056b3;
}

.skip-btn {
  font-size: 10px;
}

.stop-btn {
  background: #dc3545;
  color: white;
  border-color: #dc3545;
}

.stop-btn:hover {
  background: #c82333;
  border-color: #c82333;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #ffffff40;
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.secondary-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 4px;
  flex: 1;
  min-width: 80px;
}

.volume-btn {
  width: 24px;
  height: 24px;
  font-size: 10px;
}

.volume-slider {
  flex: 1;
  height: 3px;
  background: #e9ecef;
  border-radius: 2px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  background: #007bff;
  border-radius: 50%;
  cursor: pointer;
  transition: background 0.2s ease;
}

.volume-slider::-webkit-slider-thumb:hover {
  background: #0056b3;
}

.volume-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  background: #007bff;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  transition: background 0.2s ease;
}

.volume-slider::-moz-range-thumb:hover {
  background: #0056b3;
}

.playback-rate {
  display: flex;
  align-items: center;
  gap: 4px;
}

.playback-rate label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.rate-selector {
  padding: 2px 6px;
  border: 1px solid #ced4da;
  border-radius: 3px;
  background: white;
  font-size: 12px;
  cursor: pointer;
}

.download-btn {
  width: 24px;
  height: 24px;
  font-size: 10px;
  background: #28a745;
  color: white;
  border-color: #28a745;
}

.download-btn:hover {
  background: #218838;
  border-color: #218838;
}

.download-all-btn {
  width: auto;
  height: 24px;
  padding: 0 8px;
  font-size: 10px;
  background: #17a2b8;
  color: white;
  border-color: #17a2b8;
  border-radius: 12px;
  display: flex;
  align-items: center;
  gap: 2px;
}

.download-all-btn:hover {
  background: #138496;
  border-color: #138496;
}

/* Responsive design */
@media (max-width: 768px) {
  .enhanced-audio-player {
    padding: 8px;
  }

  .secondary-controls {
    flex-direction: column;
    gap: 6px;
  }

  .volume-control {
    width: 100%;
  }

  .file-selector {
    justify-content: center;
  }

  .file-tab {
    max-width: 100px;
  }
}

@media (max-width: 480px) {
  .main-controls {
    gap: 4px;
  }

  .control-btn {
    width: 28px;
    height: 28px;
    font-size: 10px;
  }

  .play-btn {
    width: 36px;
    height: 36px;
    font-size: 12px;
  }
}
