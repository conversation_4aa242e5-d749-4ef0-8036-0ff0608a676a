import React from 'react';
import './ConversationPanel.css';

const messages = [
  { text: 'എന്നാലും ഡോക്ടർ എന്റെ കുഴിയിൽ അത്ര വലിയ കഷ്ടം ഒന്നുമില്ല...', time: '04/25/2025, 15:42:15' },
  { text: 'ആ പക കാഞ്ഞങ്ങൾ...', time: '04/25/2025, 15:42:35' },
  { text: 'എന്നാലും നാളെ...', time: '04/25/2025, 15:42:47' },
  { text: 'ഓർക്കോ ശശി.', time: '04/25/2025, 15:42:47' },
];

function ConversationPanel() {
  return (
    <div className="conversation-panel">
      <div className="panel-title">Conversation</div>
      <div className="messages-scroll">
        {messages.map((msg, idx) => (
          <div className="message-block" key={idx}>
            <div className="message-text">{msg.text}</div>
            <div className="message-time">{msg.time}</div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default ConversationPanel;
