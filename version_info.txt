# version_info.txt
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(1,0,0,0),
    prodvers=(1,0,0,0),
    mask=0x3f,
    flags=0x0,
    OS=0x4,
    fileType=0x1,
    subtype=0x0,
    date=(0,0)
    ),
  kids=[
    StringFileInfo([
      StringTable(
        '040904B0',
        [StringStruct('CompanyName', 'ArcaAI'),
        StringStruct('FileDescription', 'Multi-mic Audio Agent'),
        StringStruct('FileVersion', '*******'),
        StringStruct('InternalName', 'MMR-Agent'),
        StringStruct('LegalCopyright', 'Copyright (c) 2025 ArcaAI'),
        StringStruct('OriginalFilename', 'MMR-Agent.exe'),
        StringStruct('ProductName', 'MMR-Agent'),
        StringStruct('ProductVersion', '*******')])
      ]),
    VarFileInfo([VarStruct('Translation', [1033, 1200])])
  ]
)
