# High-Level Design (HLD): Multi-mic Audio Agent

## Overview
The Audio Agent is a production-ready, secure, and robust FastAPI backend for multi-microphone audio capture, real-time DSP (via Pedalboard), and live streaming to a web UI. Each computer runs its own agent, while the web UI is hosted on a local server in the customer environment.

## Architecture
- **Input:** Multiple microphones (sounddevice)
- **Processing:** Real-time DSP (Pedalboard: EQ, noise gate, compressor, limiter)
- **Streaming:** WebSocket audio stream to web UI
- **Control:** REST API for device listing, recording control, and DSP config
- **Persistence:** Full session WAV file per mic
- **Security:** CORS restricted, port checks, robust shutdown, OWASP-aligned
- **Observability:** Prometheus metrics endpoint (`/metrics` and port 8001), OpenTelemetry tracing (toggle in config)
- **Service Management:** See README for Windows service install/start/stop/restart commands

## Key Components
- **FastAPI app**: REST and WebSocket endpoints
- **Threaded audio capture**: One thread per active mic
- **DSP pipeline**: Pedalboard plugins, live config
- **Graceful shutdown**: Signal handling, thread cleanup
- **Logging**: File and console, all major events

## Deployment
- Each agent runs on a local machine (one per computer)
- Web UI is accessed from a central server (CORS restricted)
- No external internet required; all traffic is local

## Security
- CORS restricted to web UI server
- Port-in-use check prevents accidental double start
- Graceful shutdown on SIGINT/SIGTERM
- No sensitive data stored or transmitted
- Logging for audit and troubleshooting

---
