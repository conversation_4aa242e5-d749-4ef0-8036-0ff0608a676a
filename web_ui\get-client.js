const { Pool } = require('pg');
require('dotenv').config();

// Create a connection pool instead of individual clients
const pool = new Pool({
  host: process.env.PG_HOST,
  port: process.env.PG_PORT,
  user: process.env.DATABASE_USER,
  password: process.env.DATABASE_PASSWORD,
  database: process.env.DATABASE_NAME,
  ssl: false,
  // Connection pool configuration
  max: 100, // Maximum number of connections in the pool
  idleTimeoutMillis: 30000, // Close idle connections after 30 seconds
  connectionTimeoutMillis: 2000, // Return error after 2 seconds if unable to get connection
});

// Handle pool errors
pool.on('error', (err, client) => {
  console.error('Unexpected error on idle client', err);
  process.exit(-1);
});

async function getClient() {
  // Return a client from the pool
  return await pool.connect();
}

// Export both the pool and getClient function
module.exports = { getClient, pool };

// const { Client } = require('pg');
// require('dotenv').config();

// (async () => {
//   const client = new Client({
// 	host: process.env.PG_HOST,
// 	port: process.env.PG_PORT,
// 	user: process.env.PG_USER,
// 	password: process.env.PG_PASSWORD,
// 	database: process.env.PG_DATABASE,
// 	ssl: true,
//   });
//   await client.connect();
//   const res = await client.query('SELECT $1::text as connected', ['Connection to postgres successful!']);
//   console.log(res.rows[0].connected);
//   await client.end();
// })();
