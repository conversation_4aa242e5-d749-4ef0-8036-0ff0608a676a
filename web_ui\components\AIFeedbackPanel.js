import React from 'react';
import './AIFeedbackPanel.css';

function AIFeedbackPanel({ summary, setSummary }) {
  return (
    <div className="ai-feedback-panel">
      <div className="panel-title">Ai Feedback</div>
      <div className="feedback-section">
        <b>presentingcomplaint</b><br/>
        <b>historyofpresenting</b><br/>
        <b>familyhistory</b><br/>
        <b>treatmenthistory</b>
        <ul>
          <li>The doctor mentioned that surgery will be necessary due to a small tear, which was identified in the scanning report.</li>
          <li>The patient indicated financial constraints and discussed the possibility of making a partial payment for the surgery.</li>
          <li>The doctor confirmed that no further testing is required as the report has already provided the necessary details.</li>
        </ul>
        <b>generalexamination</b>
      </div>
      <textarea className="summary-edit" value={summary} onChange={e => setSummary(e.target.value)} placeholder="Edit feedback here..." />
    </div>
  );
}

export default AIFeedbackPanel;
