import React, { useEffect, useState, useRef } from 'react';
import ClinicalUIPopUp from '../ClinicalUIPopUp';

function ClinicalPage() {
  const [sessionId, setSessionId] = useState('');
  const [eventId, setEventId] = useState('');
  const [patientId, setPatientId] = useState('');
  const [patientName, setPatientName] = useState('');
  const [role, setRole] = useState('');

  // Ref to access recording state from ClinicalUIPopUp
  const clinicalUIRef = useRef(null);

  useEffect(() => {
    const query = new URLSearchParams(window.location.search);

    setSessionId(query.get('sessionId') || '');
    setEventId(query.get('eventId') || '');
    setPatientId(query.get('patientId') || '');
    setPatientName(query.get('patientName') || '');
    setRole(query.get('role') || '');

    // Add beforeunload event listener to handle tab closing
    const handleBeforeUnload = (event) => {
      // Check if recording is ongoing through the ref
      if (clinicalUIRef.current && clinicalUIRef.current.isRecordingActive()) {
        const message = 'A recording is ongoing or paused. Closing will remove partial recordings. Do you want to proceed?';
        event.preventDefault();
        event.returnValue = message;
        return message;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  const handleClose = () => {
    try {
      // Try to close the tab (this will work if the tab was opened by script)
      window.close();
    } catch (error) {
      console.log('Cannot close tab programmatically, redirecting to entry point');
      // If we can't close the tab, redirect back to the trigger page
      window.location.href = '/EntryPoint.html';
    }
  };

  return (
    <div style={{ 
      width: '100vw', 
      height: '100vh', 
      overflow: 'hidden',
      background: '#f5f5f5'
    }}>
      <ClinicalUIPopUp
        ref={clinicalUIRef}
        onClose={handleClose}
        sessionId={sessionId}
        eventId={eventId}
        patientId={patientId}
        patientName={patientName}
        role={role}
      />
    </div>
  );
}

export default ClinicalPage;
