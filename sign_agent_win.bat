@echo off
REM Digitally sign the EXE using osslsigncode (or signtool if available)
REM Update CERT_PATH and CERT_PASS as needed

set CERT_PATH=mycert.p12
set CERT_PASS=YOURPASS
set EXE_PATH=dist\MMR-Agent.exe
set SIGNED_EXE_PATH=dist\MMR-Agent-signed.exe

REM If using osslsigncode (install via Chocolatey: choco install osslsigncode)
osslsigncode sign -pkcs12 %CERT_PATH% -pass %CERT_PASS% -n "MMR-Agent" -i https://www.arcaai.com -in %EXE_PATH% -out %SIGNED_EXE_PATH%

echo EXE signed: %SIGNED_EXE_PATH%

REM If using signtool (uncomment and update as needed)
REM signtool sign /f %CERT_PATH% /p %CERT_PASS% /tr http://timestamp.digicert.com /td sha256 /fd sha256 %EXE_PATH%
REM echo EXE signed: %EXE_PATH%
