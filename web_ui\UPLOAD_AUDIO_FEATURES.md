# Upload Audio API & Medical Transcription Features

This document tracks the implementation status of the full-audio upload and medical transcript generation feature for the web UI backend.

---

## Features Checklist

- [x] **/api/upload_audio endpoint**  
  Accepts a WAV file upload from the client for batch transcription and medical summary generation.

- [x] **Send audio to Azure Speech Service (batch transcription)**  
  Uses Azure's batch (or conversation) endpoint to transcribe the uploaded audio file. Handles both English and Malayalam.

- [x] **Send transcript to OpenAI for medical summary**  
  Sends the Azure transcript to OpenAI (or Azure OpenAI) to generate a structured medical transcript (e.g., SOAP note or summary).

- [x] **Return both raw and medical transcripts**  
  Responds to the client with both the raw Azure transcript and the OpenAI-generated medical summary.

- [x] **Error handling**  
  Handles and reports errors for both Azure and OpenAI API failures, and file upload issues.

- [x] **Configuration via environment variables**  
  All API keys, endpoints, and region are configurable via `.env` or environment variables.

- [x] **File cleanup**  
  Uploaded audio files are deleted after processing to save disk space.

- [ ] **(Optional) Progress/status endpoint**  
  Not implemented. For long Azure batch jobs, consider adding a status/polling endpoint.

---

## Usage

- **POST** `/api/upload_audio`  
  Form-data: `audio` (WAV file), `language` (e.g., `en-US`, `ml-IN`)
  
  **Response:**
  ```json
  {
    "success": true,
    "rawTranscript": "...",
    "medicalTranscript": "..."
  }
  ```

- **Environment Variables Required:**
  - `AZURE_SPEECH_KEY`
  - `AZURE_SPEECH_REGION`
  - `OPENAI_API_KEY` or `AZURE_OPENAI_API_KEY`
  - `OPENAI_API_BASE` or `AZURE_OPENAI_ENDPOINT_URL`
  - `AZURE_OPENAI_DEPLOYMENT_NAME` (for Azure OpenAI)

---

## Notes
- For production, use Azure Blob Storage for batch jobs and public file URLs.
- The current implementation uses the conversation endpoint for simplicity.
- Extend as needed for polling/progress, or to support true Azure batch jobs.
