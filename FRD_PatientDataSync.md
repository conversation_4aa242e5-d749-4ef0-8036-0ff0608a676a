
# Functional Requirements Document (FRD)

## Project Title:  
**Patient Data Sync and Storage Module**

**Prepared By:** Najmunneesa  
**Date:** July 17, 2025

---

## 1. Objective

Build a web module using **React (Frontend)** and **Node.js (Backend)** that:

- Calls an external API (`https://mi-preproduction.bcmch.org:4432`) or uses mock data based on configuration.
- Receives a structured JSON response containing **patient** and **consultant** data.
- Forwards the data to the backend.
- Stores or updates:
  - **Patient**
  - **Consultant (Doctor)**
  - **Department**
- Handles data integrity using identifiers like `regNo`, `doctorId`, and `departmentId`.

---

## 2. High-Level Architecture

```plaintext
React Frontend (ClinicalUIPopup.jsx)
     |
     |-- Config from .env:
     |     - REACT_APP_USE_MOCK_DATA=true/false
     |     - REACT_APP_EXTERNAL_API=https://mi-preproduction.bcmch.org:4432
     |
     |-- GET: Fetch mock or actual data
     |
     |-- POST: Send response to internal backend API → /api/storePatientData
     |
Node.js Backend
     |
     |-- Validate and parse JSON
     |-- Upsert data into DB (Patient, Doctor, Department)
     |
Database (e.g., PostgreSQL / MongoDB)
```

---

## 3. Functional Requirements

### 3.1 React Frontend

#### 3.1.1 Component: `ClinicalUIPopup.jsx`

- On component mount:
  - Based on `.env` configuration:
    - If `REACT_APP_USE_MOCK_DATA` is `true` → fetch mock data from local JSON
    - Else → fetch from `REACT_APP_EXTERNAL_API`
  - POST the final response to backend API `/api/storePatientData`
  - Handle loading, success, and error states in UI

#### 3.1.2 Environment Variables (`.env`)

```env
REACT_APP_USE_MOCK_DATA=true
REACT_APP_EXTERNAL_API=https://mi-preproduction.bcmch.org:4432
```

#### 3.1.3 API Endpoints

- `GET`: From `REACT_APP_EXTERNAL_API` or `/mock/patientResponse.json`
- `POST /api/storePatientData`: Payload: Complete JSON response

---

### 3.2 Node.js Backend

#### 3.2.1 Endpoint: `POST /api/storePatientData`

- **Input**: JSON object with patient, consultant, department, encounters
- **Process**:
  - Validate required fields
  - Upsert data into tables based on unique identifiers
- **Output**: `{ success: true/false, message: "..." }`

#### 3.2.2 Database Tables

- **Patients**: regNo, name, dob, gender, vitals, firstVisit
- **Doctors**: doctorId, name, departmentId
- **Departments**: departmentId, name
- **Encounters**: encounterId, patientRegNo, doctorId, departmentId, date, caseNotes

---

## 4. Non-Functional Requirements

- Use `.env` for toggling mock/live mode
- Retry mechanism for API fetch
- JSON validation in backend
- Graceful handling of duplicates (upsert)
- Logs for sync operations

---

## 5. Sample Workflow

1. `ClinicalUIPopup.jsx` loads
2. Checks `.env` config (`REACT_APP_USE_MOCK_DATA`)
3. Fetches data (mock or external)
4. Sends data to `/api/storePatientData`
5. Backend parses and updates DB
6. UI shows result

---

## 6. Future Enhancements

- Role-based data access
- Encounter view in UI
- Secure API with token-based access
- Admin re-sync panel

---

## 7. Sample Mock JSON

Saved as `public/mock/patientResponse.json`:

```json
{
  "patient": {
    "regNo": "REG123456",
    "name": "John Doe",
    "dob": "2000-09-12",
    "gender": "Male",
    "FirstVisitInfo ": {
      "hospital": true,
      "department": true,
      "doctor": true,
      "clinic": true
    },
    "vitals": {
      "bloodPressure": "120/80 mmHg",
      "GRBS": "130 mg/dL"
    },
    "encounter": [
      {
        "encounterId": 123456798,
        "date": "2025-06-31",
        "departmentId": "",
        "departmentName": "",
        "doctorId": "12346",
        "doctorName": "Test Doctor",
        "caseNotes": [
          {
            "head": "Presenting Complaints",
            "text": "<div><p>This is a test data</p><b>Covid Status : Yes </b></div>"
          },
          {
            "head": "Hostory",
            "text": "<div><p>This is a test data 2</p><b>Covid Status : Yes </b></div>"
          }
        ]
      }
    ]
  },
  "consultant": {
    "consultantId": "USR789",
    "Name": "Dr. Admin MBBS",
    "departmentId": "DEPT101",
    "departmentName": "Cardiology"
  }
}
```

---
