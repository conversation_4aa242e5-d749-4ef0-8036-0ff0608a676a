
OPENAI_API_BASE=https://alaas-openai.openai.azure.com/
OPENAI_API_KEY=F5Kvc2iVDdZVkGHsVSssaZs342f0qUURXUWIFn5VaJiodtqNV2McJQQJ99BAACYeBjFXJ3w3AAABACOGP15b
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4


ARCA_SERVICE_BUS_URL=https://alaas-ms.azurewebsites.net/api/sb/send-message
ARCA_SERVICE_BUS_USERNAME=userclient1
ARCA_SERVICE_BUS_PASSWORD=abc123

AZURE_SPEECH_KEY=EhVQLrNppOEFTy802mw5piyol8BESvZQpE5HeajO0qbba1FoY5X0JQQJ99BAACYeBjFXJ3w3AAAYACOGerJC
AZURE_SPEECH_REGION=eastus

AZURE_STORAGE_ACCOUNT=alaastoragdev
AZURE_STORAGE_KEY=****************************************************************************************,
AZURE_STORAGE_CONTAINER=audiouploads 

PORT=4000
UPLOAD_MAX_SIZE_MB=100

FRONTEND_ORIGIN=https://arcaai-u2204.bcmch.org
VITE_API_BASE_URL=https://arcaai-u2204.bcmch.org

USE_SECURE=0
SSL_CERT_KEY=/certs/localhost.key
SSL_CERT=/certs/localhost.cert