.ai-feedback-panel {
  background: #f7fafd;
  border-radius: 8px;
  box-shadow: 0 2px 8px #e3eaf2;
  flex: 1.5;
  padding: 16px 18px 8px 18px;
  display: flex;
  flex-direction: column;
  min-width: 340px;
  max-width: 600px;
  height: 420px;
}
.panel-title {
  font-weight: 600;
  color: #1a355e;
  margin-bottom: 8px;
}
.feedback-section {
  background: #fff;
  border-radius: 6px;
  padding: 12px 14px;
  margin-bottom: 10px;
  font-size: 1.02rem;
  color: #222;
  text-align: left;
}
.summary-edit {
  width: 100%;
  min-height: 60px;
  border-radius: 4px;
  border: 1px solid #b0c4de;
  padding: 6px;
  font-size: 1rem;
  margin-top: 6px;
}

/* Loading spinner animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Add this to AIFeedbackPanel.css for spinner/overlay styling */
.ai-feedback-loading-overlay {
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(255,255,255,0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}
.ai-feedback-spinner {
  width: 36px;
  height: 36px;
  border: 4px solid #e0e0e0;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: ai-spin 1s linear infinite;
}
@keyframes ai-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
