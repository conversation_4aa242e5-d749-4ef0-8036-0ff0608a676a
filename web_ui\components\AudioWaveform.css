.audio-waveform {
	width: 100%;
	margin-bottom: 15px;
	background-color: white;
	border-radius: 8px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
	overflow: hidden;
  }
  
  .waveform-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 8px 12px;
	background-color: #f8f8f8;
	border-bottom: 1px solid #eaeaea;
  }
  
  .waveform-info {
	display: flex;
	flex-direction: column;
  }
  
  .waveform-name {
	font-weight: 600;
	font-size: 0.9rem;
	color: #333;
  }
  
  .waveform-role {
	font-size: 0.8rem;
	color: #666;
  }
  
  .waveform-controls {
	display: flex;
	align-items: center;
	gap: 8px;
  }
  
  .control-button {
	background: none;
	border: none;
	cursor: pointer;
	width: 30px;
	height: 30px;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background-color: #f0f0f0;
	color: #333;
	transition: all 0.2s ease;
  }
  
  .control-button:hover {
	background-color: #e0e0e0;
  }
  
  .control-button:active {
	background-color: #d0d0d0;
  }
  
  .volume-slider-container {
	width: 80px;
	margin: 0 8px;
  }
  
  .volume-slider {
	width: 100%;
	height: 4px;
	-webkit-appearance: none;
	appearance: none;
	background: #ddd;
	outline: none;
	border-radius: 2px;
  }
  
  .volume-slider::-webkit-slider-thumb {
	-webkit-appearance: none;
	appearance: none;
	width: 12px;
	height: 12px;
	border-radius: 50%;
	background: #1a9c5e;
	cursor: pointer;
  }
  
  .volume-slider::-moz-range-thumb {
	width: 12px;
	height: 12px;
	border-radius: 50%;
	background: #1a9c5e;
	cursor: pointer;
	border: none;
  }
  
  .volume-meter-container {
	width: 5px;
	height: 30px;
	background-color: #eee;
	border-radius: 2px;
	overflow: hidden;
	position: relative;
  }
  
  .volume-meter {
	position: absolute;
	bottom: 0;
	width: 100%;
	background-color: #1a9c5e;
	transition: height 0.1s ease, background-color 0.1s ease;
  }
  
  .waveform-canvas-container {
	padding: 10px;
	background-color: #fff;
	position: relative;
	overflow: hidden;
	cursor: pointer;
  }
  
  .playhead {
	position: absolute;
	top: 0;
	width: 2px;
	height: 100%;
	background-color: #1a9c5e;
	z-index: 2;
	pointer-events: none;
  }
  
  .waveform-timeline {
	display: flex;
	align-items: center;
	padding: 0 12px 8px;
	font-size: 0.75rem;
	color: #666;
  }
  
  .timeline-progress {
	flex: 1;
	height: 4px;
	background-color: #eee;
	margin: 0 8px;
	border-radius: 2px;
	position: relative;
  }
  
  .timeline-progress-bar {
	position: absolute;
	top: 0;
	left: 0;
	height: 100%;
	background-color: #1a9c5e;
	border-radius: 2px;
  }
  
  .audio-settings-panel {
	padding: 12px;
	background-color: #f8f8f8;
	border-top: 1px solid #eaeaea;
  }
  
  .audio-settings-panel h4 {
	margin-bottom: 10px;
	font-size: 0.9rem;
	color: #333;
  }
  
  .setting-group {
	display: flex;
	align-items: center;
	margin-bottom: 8px;
  }
  
  .setting-group label {
	width: 80px;
	font-size: 0.8rem;
	color: #666;
  }
  
  .setting-group input {
	flex: 1;
	height: 4px;
	-webkit-appearance: none;
	appearance: none;
	background: #ddd;
	outline: none;
	border-radius: 2px;
  }
  
  .setting-group input::-webkit-slider-thumb {
	-webkit-appearance: none;
	appearance: none;
	width: 12px;
	height: 12px;
	border-radius: 50%;
	background: #1a9c5e;
	cursor: pointer;
  }
  
  .setting-group input::-moz-range-thumb {
	width: 12px;
	height: 12px;
	border-radius: 50%;
	background: #1a9c5e;
	cursor: pointer;
	border: none;
  }
  
  .waveform-canvas {
	width: 100%;
	height: 100px;
	display: block;
  }
  