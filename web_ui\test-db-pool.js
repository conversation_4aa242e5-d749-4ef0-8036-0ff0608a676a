#!/usr/bin/env node

/**
 * Test script to verify database connection pooling is working correctly
 * This script simulates multiple concurrent database requests to test pool behavior
 */

const { getClient, pool } = require('./get-client');

async function testSingleConnection() {
  console.log('Testing single connection...');
  let client;
  try {
    client = await getClient();
    const result = await client.query('SELECT NOW() as current_time, pg_backend_pid() as pid');
    console.log(`✓ Single connection test passed. Time: ${result.rows[0].current_time}, PID: ${result.rows[0].pid}`);
  } catch (err) {
    console.error('✗ Single connection test failed:', err.message);
  } finally {
    if (client) await client.release();
  }
}

async function testConcurrentConnections(numConnections = 10) {
  console.log(`\nTesting ${numConnections} concurrent connections...`);
  
  const promises = [];
  for (let i = 0; i < numConnections; i++) {
    promises.push(
      (async (index) => {
        let client;
        try {
          const startTime = Date.now();
          client = await getClient();
          const connectTime = Date.now() - startTime;
          
          const result = await client.query('SELECT NOW() as current_time, pg_backend_pid() as pid, $1 as request_id', [index]);
          const totalTime = Date.now() - startTime;
          
          console.log(`✓ Request ${index}: PID ${result.rows[0].pid}, Connect: ${connectTime}ms, Total: ${totalTime}ms`);
          return { success: true, index, pid: result.rows[0].pid, connectTime, totalTime };
        } catch (err) {
          console.error(`✗ Request ${index} failed:`, err.message);
          return { success: false, index, error: err.message };
        } finally {
          if (client) await client.release();
        }
      })(i)
    );
  }
  
  const results = await Promise.all(promises);
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`\nConcurrent test results:`);
  console.log(`✓ Successful: ${successful.length}/${numConnections}`);
  console.log(`✗ Failed: ${failed.length}/${numConnections}`);
  
  if (successful.length > 0) {
    const avgConnectTime = successful.reduce((sum, r) => sum + r.connectTime, 0) / successful.length;
    const avgTotalTime = successful.reduce((sum, r) => sum + r.totalTime, 0) / successful.length;
    console.log(`Average connect time: ${avgConnectTime.toFixed(2)}ms`);
    console.log(`Average total time: ${avgTotalTime.toFixed(2)}ms`);
    
    // Check if we're reusing connections (different requests might get same PID)
    const uniquePids = new Set(successful.map(r => r.pid));
    console.log(`Unique database processes used: ${uniquePids.size}`);
  }
  
  return { successful: successful.length, failed: failed.length };
}

async function testPoolStats() {
  console.log('\nPool statistics:');
  console.log(`Total connections: ${pool.totalCount}`);
  console.log(`Idle connections: ${pool.idleCount}`);
  console.log(`Waiting clients: ${pool.waitingCount}`);
}

async function runTests() {
  console.log('🔍 Database Connection Pool Test\n');
  console.log('================================');
  
  try {
    await testSingleConnection();
    await testPoolStats();
    
    // Test with moderate load
    await testConcurrentConnections(5);
    await testPoolStats();
    
    // Test with higher load
    await testConcurrentConnections(15);
    await testPoolStats();
    
    // Test with very high load (should test pool limits)
    console.log('\n⚠️  Testing pool limits with high load...');
    await testConcurrentConnections(25);
    await testPoolStats();
    
    console.log('\n✅ All tests completed!');
    
  } catch (err) {
    console.error('❌ Test suite failed:', err);
  } finally {
    console.log('\nClosing pool...');
    await pool.end();
    console.log('Pool closed. Exiting.');
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testSingleConnection, testConcurrentConnections, testPoolStats };
