[server]
allowed_origin = http://localhost:5173,https://arcaai-u2204.bcmch.org,https://arcaai-staging.bcmch.org
port = 5001
host = 0.0.0.0
enable_opentelemetry = False
driver_name = Windows WDM-KS

[environment]
# Environment mode: development or production
env = development
# Admin password for the default admin user (environment variable takes precedence)
admin_password = admin

[security]
# JWT secret key for token signing (environment variable takes precedence)
# Use a strong, unique secret key for production
jwt_secret = dev-jwt-secret-key-for-audio-agent-2024
jwt_algorithm = HS256
jwt_expire_minutes = 30
jwt_refresh_expire_minutes = 1440
idle_timeout_minutes = 30
rate_limit_token = 5/minute
max_login_attempts = 5
lockout_duration_minutes = 15
# Content Security Policy settings
csp_default_src = 'self'
csp_script_src = 'self' 'unsafe-inline'
csp_style_src = 'self' 'unsafe-inline'

[logging]
log_level = INFO
log_file = audio_agent.log
log_filemode = a
enable_audit_log = True
audit_log_file = audit.log

[api]
title = Multi-mic Audio Agent API
description = Production-ready backend for multi-mic audio streaming and DSP.
version = 1.0.0
