# Low-Level Design (LLD): Multi-mic Audio Agent

## 1. FastAPI Endpoints
- **GET /list_mics**: Returns available microphones
- **POST /start_recording**: Starts recording for selected mics (JSON body)
- **POST /stop_recording**: Stops all recordings, cleans up
- **POST /update_dsp**: Updates DSP config for all mics (JSON body)
- **WebSocket /audio_stream**: Streams live audio chunks to frontend
- **Swagger UI**: http://localhost:5001/docs

## 2. Audio Capture & Streaming
- Each selected mic spawns a thread:
  - Reads audio in 1s chunks
  - Applies DSP (Pedalboard)
  - Streams chunk to frontend (WebSocket)
  - Buffers for full-session WAV save
- Thread stops on signal or shutdown

## 3. DSP Pipeline
- Uses Pedalboard plugins:
  - Highpass/Lowpass (EQ)
  - NoiseGate
  - Compressor
  - Limiter
- Configurable per-mic, live update via API
- Output always 1D, float32, chunked

## 4. Shutdown & Cleanup
- SIGINT/SIGTERM and FastAPI shutdown event handled
- All threads signaled and joined
- All queues and configs cleared
- No resource leaks

## 5. Security Hardening
- CORS restricted to web UI server
- Port check before start
- No sensitive data in logs
- All exceptions logged, no stacktraces to client
- No external network calls

## 6. Logging
- All major events, errors, and state changes logged to file and console
- Prometheus metrics endpoint `/metrics` and port 8001 for monitoring
- OpenTelemetry tracing can be enabled via `enable_opentelemetry` in config (see README)

## 7. Service Management
- See README for Windows service install/start/stop/restart commands

---
