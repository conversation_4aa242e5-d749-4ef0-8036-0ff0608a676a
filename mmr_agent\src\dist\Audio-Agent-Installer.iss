; Audio-Agent Windows Installer Script
; Requires Inno Setup (https://jrsoftware.org/isinfo.php)

[Setup]
AppName=Audio-Agent
AppVersion=1.0.0
AppPublisher=ArcaAI
AppPublisherURL=https://www.arcaai.com
AppSupportURL=https://www.arcaai.com/support
AppUpdatesURL=https://www.arcaai.com/updates
DefaultDirName=C:\Users\<USER>\ARCA_AI_MMR
UsePreviousAppDir=no
AllowNoIcons=yes
DefaultGroupName=Audio-Agent
UninstallDisplayIcon={app}\Audio-Agent.exe
OutputBaseFilename=Audio-Agent-Installer
SetupIconFile=arca-logo.ico
Compression=lzma
SolidCompression=yes
LicenseFile=license.txt

[Files]
Source: "Audio-Agent.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "audio_agent_config.ini"; DestDir: "{app}"; Flags: ignoreversion
Source: "ARCA Privacy Policy.pdf"; DestDir: "{app}"; Flags: ignoreversion
Source: "nssm.exe"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
Name: "{group}\Audio-Agent"; Filename: "{app}\nssm.exe"; Parameters: "start Audio-Agent"; IconFilename: "{app}\Audio-Agent.exe"
Name: "{group}\Stop Audio-Agent"; Filename: "{app}\nssm.exe"; Parameters: "stop Audio-Agent"
Name: "{group}\Restart Audio-Agent"; Filename: "{app}\nssm.exe"; Parameters: "restart Audio-Agent"
Name: "{group}\Uninstall Audio-Agent"; Filename: "{uninstallexe}"
Name: "{commondesktop}\Audio-Agent"; Filename: "{app}\Audio-Agent.exe"; Tasks: desktopicon

[Run]
; Stop any existing service first
Filename: "{app}\nssm.exe"; Parameters: "stop Audio-Agent"; Flags: runhidden; StatusMsg: "Stopping existing service..."; Check: ServiceExists
Filename: "{app}\nssm.exe"; Parameters: "remove Audio-Agent confirm"; Flags: runhidden; Check: ServiceExists
; Install as Windows service using NSSM
Filename: "{app}\nssm.exe"; Parameters: "install Audio-Agent ""{app}\Audio-Agent.exe"""; StatusMsg: "Installing Audio-Agent service..."; Flags: runhidden waituntilterminated
Filename: "{app}\nssm.exe"; Parameters: "set Audio-Agent Description ""Multi-mic Audio Agent Service"""; Flags: runhidden waituntilterminated
Filename: "{app}\nssm.exe"; Parameters: "set Audio-Agent Start SERVICE_AUTO_START"; Flags: runhidden waituntilterminated
Filename: "{app}\nssm.exe"; Parameters: "set Audio-Agent AppStdout ""{commonappdata}\Audio-Agent\service.log"""; Flags: runhidden waituntilterminated
Filename: "{app}\nssm.exe"; Parameters: "set Audio-Agent AppStderr ""{commonappdata}\Audio-Agent\service_error.log"""; Flags: runhidden waituntilterminated
Filename: "{app}\nssm.exe"; Parameters: "start Audio-Agent"; Description: "Start Audio-Agent service now"; Flags: nowait postinstall skipifsilent

[UninstallRun]
; Stop and remove service on uninstall
Filename: "{app}\nssm.exe"; Parameters: "stop Audio-Agent"; Flags: runhidden waituntilterminated
Filename: "{app}\nssm.exe"; Parameters: "remove Audio-Agent confirm"; Flags: runhidden waituntilterminated

[Tasks]
Name: "desktopicon"; Description: "Create a &desktop icon"; GroupDescription: "Additional icons:"; Flags: unchecked
Name: "startservice"; Description: "Start Audio-Agent service after installation"; GroupDescription: "Service options:"; Flags: checkedonce

[Registry]
; Registry entries removed - service handles startup automatically

[CustomMessages]
WelcomeLabel1=Welcome to the Audio-Agent Setup Wizard
WelcomeLabel2=This will install Audio-Agent on your computer.

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Code]
function ServiceExists: Boolean;
var
  ResultCode: Integer;
begin
  Result := Exec('sc', 'query Audio-Agent', '', SW_HIDE, ewWaitUntilTerminated, ResultCode) and (ResultCode = 0);
end;
