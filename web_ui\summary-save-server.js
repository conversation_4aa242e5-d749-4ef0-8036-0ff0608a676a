const http = require('http');
const { getClient, pool } = require('./get-client');

(async () => {
  // Use a temporary client for setup, then release it
  const setupClient = await getClient();
  await setupTable(setupClient);
  await setupClient.release();

  const server = http.createServer();
  const address = { port: 8081, host: '0.0.0.0' };

  server.on('request', async (req, res) => {
    // Utility for JSON response
    const jsonResponse = (obj, code = 200) => {
      res.writeHead(code, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify(obj));
      console.log(new Date(), '-- Handled request:', req.method, req.url, code);
    };

    // Only handle POST /save_summary
    if (req.method === 'POST' && req.url === '/save_summary') {
      let body = '';
      req.on('data', chunk => { body += chunk; });
      req.on('end', async () => {
        let client;
        try {
          const data = JSON.parse(body);
          const { filepath, sessionId, patientId, eventId, summary, timestamp } = data;
          if (!filepath || !sessionId || !patientId || !eventId) {
            return jsonResponse({ success: false, message: 'Missing required fields' }, 400);
          }
          // Get a client from the pool for this request
          client = await getClient();
          // Upsert logic
          const result = await client.query(
            `INSERT INTO recording_summaries (filepath, session_id, patient_id, event_id, created_at, generated_summary)
             VALUES ($1, $2, $3, $4, $5, $6)
             ON CONFLICT (filepath, session_id, patient_id, event_id)
             DO UPDATE SET generated_summary = EXCLUDED.generated_summary, created_at = EXCLUDED.created_at
             RETURNING *`,
            [filepath, sessionId, patientId, eventId, timestamp || new Date(), summary]
          );
          jsonResponse({ success: true, data: result.rows[0] });
        } catch (e) {
          jsonResponse({ success: false, message: 'DB error', error: e.message }, 500);
        } finally {
          if (client) await client.release();
        }
      });
    } else {
      jsonResponse({ success: false, message: 'Not found' }, 404);
    }
  });

  server.listen(address.port, address.host);
  console.log(`Summary save server listening on http://${address.host}:${address.port}`);
})();

async function setupTable(client) {
  let createTableQuery = `
    CREATE TABLE IF NOT EXISTS recording_summaries(
      id BIGSERIAL PRIMARY KEY NOT NULL,
      filepath varchar NOT NULL,
      session_id varchar NOT NULL,
      patient_id varchar NOT NULL,
      event_id varchar NOT NULL,
      created_at TIMESTAMP NOT NULL DEFAULT current_timestamp,
      generated_summary text,
      UNIQUE(filepath, session_id, patient_id, event_id)
    );
  `;
  return await client.query(createTableQuery);
}
