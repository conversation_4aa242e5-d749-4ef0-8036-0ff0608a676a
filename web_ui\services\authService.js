/**
 * Authentication Service for Audio Agent API
 * Handles JWT token management and authentication
 */

// Get configuration from environment variables
const AUDIO_AGENT_BASE_URL = import.meta.env.VITE_AUDIO_AGENT_BASE_URL || 'http://localhost:5001' ;
const AUDIO_AGENT_USERNAME = import.meta.env.VITE_AUDIO_AGENT_USERNAME || 'admin' ;
const AUDIO_AGENT_PASSWORD = import.meta.env.VITE_AUDIO_AGENT_PASSWORD || 'admin' ;
const TOKEN_STORAGE_KEY = 'audio_agent_token';

class AuthService {
  constructor() {
    this.token = localStorage.getItem(TOKEN_STORAGE_KEY);
  }

  /**
   * Authenticate with the audio agent
   * @param {string} username 
   * @param {string} password 
   * @returns {Promise<boolean>} Success status
   */
  async login(username = AUDIO_AGENT_USERNAME, password = AUDIO_AGENT_PASSWORD) {
    try {
      const formData = new FormData();
      formData.append('username', username);
      formData.append('password', password);

      const response = await fetch(`${AUDIO_AGENT_BASE_URL}/token`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Authentication failed');
      }

      const data = await response.json();
      this.token = data.access_token;
      localStorage.setItem(TOKEN_STORAGE_KEY, this.token);
      
      console.log('Audio agent authentication successful');
      return true;
    } catch (error) {
      console.error('Authentication error:', error);
      this.token = null;
      localStorage.removeItem(TOKEN_STORAGE_KEY);
      return false;
    }
  }

  /**
   * Get the current authentication token
   * @returns {string|null} JWT token
   */
  getToken() {
    return this.token;
  }

  /**
   * Check if user is authenticated
   * @returns {boolean} Authentication status
   */
  isAuthenticated() {
    return !!this.token;
  }

  /**
   * Logout and clear token
   */
  logout() {
    this.token = null;
    localStorage.removeItem(TOKEN_STORAGE_KEY);
  }

  /**
   * Get authorization headers for API requests
   * @returns {Object} Headers object
   */
  getAuthHeaders() {
    if (!this.token) {
      return {};
    }
    return {
      'Authorization': `Bearer ${this.token}`,
    };
  }

  /**
   * Make an authenticated request to the audio agent
   * @param {string} url 
   * @param {Object} options 
   * @returns {Promise<Response>}
   */
  async authenticatedFetch(url, options = {}) {
    // Ensure we're authenticated
    if (!this.isAuthenticated()) {
      const loginSuccess = await this.login();
      if (!loginSuccess) {
        throw new Error('Failed to authenticate with audio agent');
      }
    }

    // Add auth headers
    const authHeaders = this.getAuthHeaders();
    const headers = {
      ...authHeaders,
      ...options.headers,
    };

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      // If we get 401, try to re-authenticate once
      if (response.status === 401) {
        console.log('Token expired, attempting re-authentication...');
        this.logout(); // Clear invalid token
        const loginSuccess = await this.login();
        if (loginSuccess) {
          // Retry the request with new token
          const newHeaders = {
            ...this.getAuthHeaders(),
            ...options.headers,
          };
          return await fetch(url, {
            ...options,
            headers: newHeaders,
          });
        } else {
          throw new Error('Re-authentication failed');
        }
      }

      return response;
    } catch (error) {
      console.error('Authenticated fetch error:', error);
      throw error;
    }
  }
}

// Create singleton instance
const authService = new AuthService();

export default authService;
