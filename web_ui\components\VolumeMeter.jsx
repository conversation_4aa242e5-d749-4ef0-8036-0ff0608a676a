import React from "react";

/**
 * VolumeMeter
 * Props:
 *   - level: number (0 to 1)
 *   - visible: boolean
 */
const VolumeMeter = ({ level = 0, visible = false }) => {
  // console.log('[VolumeMeter Debug] Props received - level:', level, 'visible:', visible);

  const widthPercentage = Math.min(100, Math.round(level * 100));
  // console.log('[VolumeMeter Debug] Calculated width %:', widthPercentage);
  if (!visible) return null;
  return (
    <div style={{
      margin: '10px 0',
      height: '14px',
      background: '#eee',
      borderRadius: '7px',
      overflow: 'hidden',
      width: '100%',
      boxShadow: '0 1px 4px rgba(0,0,0,0.07)'
    }}>
      <div
        style={{
          height: '100%',
          width: `${widthPercentage}%`,
          background: level > 0.2 ? '#28a745' : '#ffc107',
          transition: 'width 0.1s',
        }}
      />
    </div>
  );
};

export default VolumeMeter;
