@echo off
REM Digitally sign the installer <PERSON>X<PERSON> using osslsigncode (or signtool)
REM Update CERT_PATH and CERT_PASS as needed

set CERT_PATH=mycert.p12
set CERT_PASS=YOURPASS
set INSTALLER_PATH=Output\MMR-Agent-Installer.exe
set SIGNED_INSTALLER_PATH=Output\MMR-Agent-Installer-signed.exe

osslsigncode sign -pkcs12 %CERT_PATH% -pass %CERT_PASS% -n "MMR-Agent Installer" -i https://www.arcaai.com -in %INSTALLER_PATH% -out %SIGNED_INSTALLER_PATH%

echo Installer signed: %SIGNED_INSTALLER_PATH%

REM If using signtool (uncomment and update as needed)
REM signtool sign /f %CERT_PATH% /p %CERT_PASS% /tr http://timestamp.digicert.com /td sha256 /fd sha256 %INSTALLER_PATH%
REM echo Installer signed: %INSTALLER_PATH%
