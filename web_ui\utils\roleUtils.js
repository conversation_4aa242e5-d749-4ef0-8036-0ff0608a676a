// utils/roleUtils.js
// Utility to get role from URL query param, defaulting to 'doctor'

export function getRoleFromUrl() {
  if (typeof window === 'undefined') return 'doctor';
  const query = new URLSearchParams(window.location.search);
  const role = query.get('role');
  if (!role) return 'doctor';
  return role.toLowerCase();
}

export function isAdmin(role) {
  return role === 'admin';
}

export function isDoctor(role) {
  return role === 'doctor';
}
