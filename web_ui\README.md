# Clinical Conversation UI

## Setup

1. Install dependencies:
   ```sh
   npm install
   ```
2. Start the development server:
   ```sh
   npm start
   ```
3. Open [http://localhost:5173](http://localhost:5173) in your browser if it does not open automatically.

## Project Structure
- `App.js` - Main app layout
- `components/` - UI components
- `App.css` - Main styles
- `index.js` - Entry point
- `index.html` - HTML template
- `vite.config.js` - Vite config
