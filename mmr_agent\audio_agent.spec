# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['dist_obj/src/audio_agent.py'],
    pathex=['dist_obj/src'],
    binaries=[],
    datas=[('audio_agent_config.ini', '.'), ('dist_obj/pyarmor_runtime_000000', 'pyarmor_runtime_000000')],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='audio_agent',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
