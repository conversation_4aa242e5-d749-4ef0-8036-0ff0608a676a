import React, { useState, useEffect } from 'react';
import './MicrophoneSidebar.css';
import DSPControls from './DSPControls';
import { FaChevronLeft, FaChevronRight, FaSyncAlt, FaMicrophone, FaTimes, FaCog } from 'react-icons/fa';

const ROLES = ['Doctor', 'Patient', 'Nurse', 'Family', 'Other'];

const MicrophoneSidebar = ({ 
  isOpen, 
  onToggle, 
  mics, 
  micRoles, 
  setMicRoles, 
  onRefresh,
  dspSettings,
  onDSPSettingsChange
}) => {
  const [expandedMics, setExpandedMics] = useState({});

  const toggleMicExpand = (micId) => {
    setExpandedMics(prev => ({
      ...prev,
      [micId]: !prev[micId]
    }));
  };

  const handleRoleChange = (micId, role) => {
    const updated = micRoles.map(m => 
      m.deviceId === String(micId) ? { ...m, role } : m
    );
    setMicRoles(updated);
  };

  const handleMicToggle = (micId, checked) => {
    const updated = [...micRoles];
    if (checked) {
      if (!updated.find(m => m.deviceId === String(micId))) {
        updated.push({ deviceId: String(micId), role: ROLES[0] });
      }
    } else {
      const index = updated.findIndex(m => m.deviceId === String(micId));
      if (index !== -1) {
        updated.splice(index, 1);
      }
    }
    setMicRoles(updated);
  };

  const [testingMic, setTestingMic] = useState(null);
  const [audioLevel, setAudioLevel] = useState(0);
  const [audioStream, setAudioStream] = useState(null);
  const [audioContext, setAudioContext] = useState(null);

  const testMicrophone = async (deviceId) => {
    try {
      if (audioStream) {
        audioStream.getTracks().forEach(track => track.stop());
      }
      
      if (testingMic === deviceId) {
        setTestingMic(null);
        setAudioLevel(0);
        return;
      }
      
      setTestingMic(deviceId);
      
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: { deviceId: { exact: deviceId } }
      });
      
      setAudioStream(stream);
      
      const context = new (window.AudioContext || window.webkitAudioContext)();
      const analyser = context.createAnalyser();
      const microphone = context.createMediaStreamSource(stream);
      microphone.connect(analyser);
      analyser.fftSize = 256;
      const bufferLength = analyser.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);
      
      setAudioContext(context);
      
      const updateVolume = () => {
        if (testingMic === deviceId) {
          analyser.getByteFrequencyData(dataArray);
          let sum = 0;
          for (let i = 0; i < bufferLength; i++) {
            sum += dataArray[i];
          }
          const average = sum / bufferLength;
          setAudioLevel(average / 256);
          
          requestAnimationFrame(updateVolume);
        }
      };
      
      updateVolume();
    } catch (error) {
      console.error('Error testing microphone:', error);
      setTestingMic(null);
      setAudioLevel(0);
    }
  };
  
  useEffect(() => {
    return () => {
      if (audioStream) {
        audioStream.getTracks().forEach(track => track.stop());
      }
      if (audioContext) {
        audioContext.close();
      }
    };
  }, [audioStream, audioContext]);

  return (
    <div className={`microphone-sidebar ${isOpen ? 'open' : 'closed'}`}>
      <div className="sidebar-header">
        <h2><FaMicrophone /> Microphone Management</h2>
        <div className="sidebar-actions">
          <button 
            className="refresh-button" 
            onClick={onRefresh} 
            title="Refresh microphone list"
          >
            <FaSyncAlt />
          </button>
          <button 
            className="toggle-button" 
            onClick={onToggle} 
            title={isOpen ? "Close sidebar" : "Open sidebar"}
          >
            {isOpen ? <FaChevronLeft /> : <FaChevronRight />}
          </button>
        </div>
      </div>
      
      <div className="microphone-list">
        {mics.length === 0 && (
          <div className="no-mics-message">
            No microphones found. Click refresh to scan for devices.
          </div>
        )}
        
        {mics.map(mic => {
          const isSelected = micRoles.some(m => m.deviceId === String(mic.id));
          const selectedRole = micRoles.find(m => m.deviceId === String(mic.id))?.role || ROLES[0];
          const isExpanded = expandedMics[mic.id] || false;
          
          return (
            <div key={mic.id} className="microphone-item">
              <div className="microphone-header">
                <div className="microphone-selection">
                  <input 
                    type="checkbox" 
                    id={`mic-${mic.id}`}
                    checked={isSelected}
                    onChange={(e) => handleMicToggle(mic.id, e.target.checked)}
                  />
                  <label htmlFor={`mic-${mic.id}`}>
                    <FaMicrophone className="mic-icon" /> {mic.name || `Microphone ${mic.id}`}
                  </label>
                  
                  <button 
                    className={`test-mic-button ${testingMic === mic.id ? 'testing' : ''}`}
                    onClick={() => testMicrophone(mic.id)}
                    title={testingMic === mic.id ? 'Stop testing' : 'Test microphone'}
                  >
                    {testingMic === mic.id ? 'Stop Test' : 'Test'}
                  </button>
                </div>
                
                {testingMic === mic.id && (
                  <div className="volume-meter-container">
                    <div 
                      className="volume-meter" 
                      style={{ width: `${audioLevel * 100}%` }}
                    />
                  </div>
                )}
                
                {isSelected && (
                  <div className="microphone-controls">
                    <select 
                      value={selectedRole} 
                      onChange={(e) => handleRoleChange(mic.id, e.target.value)}
                      className="role-select"
                    >
                      {ROLES.map(role => (
                        <option key={role} value={role}>{role}</option>
                      ))}
                    </select>
                    
                    <button 
                      className="expand-button"
                      onClick={() => toggleMicExpand(mic.id)}
                      title={isExpanded ? "Hide DSP settings" : "Show DSP settings"}
                    >
                      {isExpanded ? "Hide DSP" : "Show DSP"}
                    </button>
                  </div>
                )}
              </div>
              
              {isSelected && isExpanded && (
                <div className="dsp-settings-container">
                  <DSPControls 
                    micId={mic.id} 
                    onSettingsChange={onDSPSettingsChange}
                  />
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default MicrophoneSidebar;
