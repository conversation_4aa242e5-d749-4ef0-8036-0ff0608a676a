import { API_BASE_URL } from '../src/constants';

/**
 * Service for handling AI data related API calls
 */
export class AIDataService {
  /**
   * Fetch all medical summaries for a specific patient
   * @param {string} patientId - The patient ID to fetch summaries for
   * @returns {Promise<{success: boolean, summaries?: Array, error?: string}>}
   */
  static async fetchPatientSummaries(patientId) {
    try {
      if (!patientId) {
        throw new Error('Patient ID is required');
      }

      const response = await fetch(
        `${API_BASE_URL}/api/patient_summaries/${encodeURIComponent(patientId)}`
      );
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        return {
          success: true,
          summaries: data.summaries || []
        };
      } else {
        return {
          success: false,
          error: data.message || 'Failed to fetch summaries'
        };
      }
    } catch (error) {
      console.error('Error fetching patient summaries:', error);
      return {
        success: false,
        error: error.message || 'Network error occurred'
      };
    }
  }

  /**
   * Fetch a specific summary by ID
   * @param {string} summaryId - The summary ID to fetch
   * @returns {Promise<{success: boolean, summary?: Object, error?: string}>}
   */
  static async fetchSummaryById(summaryId) {
    try {
      if (!summaryId) {
        throw new Error('Summary ID is required');
      }

      const response = await fetch(
        `${API_BASE_URL}/api/summary/${encodeURIComponent(summaryId)}`
      );
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.success) {
        return {
          success: true,
          summary: data.summary
        };
      } else {
        return {
          success: false,
          error: data.message || 'Failed to fetch summary'
        };
      }
    } catch (error) {
      console.error('Error fetching summary:', error);
      return {
        success: false,
        error: error.message || 'Network error occurred'
      };
    }
  }

  /**
   * Format date/time for display
   * @param {string} dateString - ISO date string
   * @returns {string} Formatted date string
   */
  static formatDateTime(dateString) {
    try {
      const date = new Date(dateString);
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid date';
    }
  }

  /**
   * Validate patient ID format
   * @param {string} patientId - Patient ID to validate
   * @returns {boolean} True if valid
   */
  static isValidPatientId(patientId) {
    return patientId && typeof patientId === 'string' && patientId.trim().length > 0;
  }
}

export default AIDataService;
