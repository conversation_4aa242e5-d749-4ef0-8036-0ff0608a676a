import React from 'react';
import AIFeedbackPanel from './AIFeedbackPanel.jsx';
import useAIData from '../hooks/useAIData.js';

function AIDataModal({ isOpen, onClose, patientId, patientName }) {

  const {
    summaries,
    loading,
    error,
    selectedSummary,
    showSummaryDetail,
    handleSummaryClick,
    handleBackToList,
    formatDateTime
  } = useAIData(patientId, isOpen);

  // State for pre-summary modal/pane
  const [showPreSummary, setShowPreSummary] = React.useState(false);

  if (!isOpen) return null;

  return (
    <div style={{
      width: '100vw',
      height: '100vh',
      display: 'flex',
      flexDirection: 'column',
      background: '#f5f5f5'
    }}>
      <div style={{
        background: 'white',
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden'
      }}>
        {/* Main Content - Header removed */}
        <div style={{
          flex: 1,
          overflow: 'auto',
          padding: '24px'
        }}>
          {showSummaryDetail ? (
            // Summary Detail View
            <div>
              <div style={{ marginBottom: '20px', display: 'flex', gap: 12, alignItems: 'center' }}>
                <button
                  onClick={handleBackToList}
                  style={{
                    background: '#f1f5f9',
                    border: '1px solid #e2e8f0',
                    borderRadius: '6px',
                    padding: '8px 16px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    color: '#475569',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}
                >
                  ← Back to List
                </button>
                {/* View Pre-Summary Button */}
                {selectedSummary?.previousCaseNotesSummary && selectedSummary.previousCaseNotesSummary.trim() !== '' ? (
                  <button
                    onClick={() => setShowPreSummary(true)}
                    style={{
                      background: '#f1f5f9',
                      border: '1px solid #e2e8f0',
                      borderRadius: '6px',
                      padding: '8px 16px',
                      cursor: 'pointer',
                      fontSize: '14px',
                      color: '#22396a',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      fontWeight: 600
                    }}
                  >
                    📝 View Pre-Summary
                  </button>
                ) : null}
              </div>

              {/* Pre-Summary Modal/Pane */}
              
              {showPreSummary && (
                <div style={{
                  position: 'fixed',
                  top: 0,
                  left: 0,
                  width: '100vw',
                  height: '100vh',
                  background: 'rgba(0,0,0,0.18)',
                  zIndex: 1000,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <div style={{
                    background: 'white',
                    borderRadius: 10,
                    boxShadow: '0 4px 24px rgba(0,0,0,0.13)',
                    maxWidth: 600,
                    width: '90vw',
                    maxHeight: '80vh',
                    padding: 32,
                    display: 'flex',
                    flexDirection: 'column',
                    position: 'relative',
                    overflow: 'hidden'
                  }}>
                    <div style={{ fontWeight: 700, fontSize: 18, color: '#22396a', marginBottom: 12 }}>Pre-Summary</div>
                    <div
                      style={{
                        flex: 1,
                        overflowY: 'auto',
                        marginBottom: 24,
                        paddingRight: 8,
                        scrollbarWidth: 'thin',
                        scrollbarColor: '#cbd5e1 #f1f5f9',
                        maxHeight: '50vh',
                      }}
                      className="custom-scrollbar"
                    >
                      {selectedSummary?.previousCaseNotesSummary && (
                        <AIFeedbackPanel summary={selectedSummary?.previousCaseNotesSummary} />
                      )}
                    </div>
                    <button
                      onClick={() => setShowPreSummary(false)}
                      style={{
                        alignSelf: 'flex-end',
                        background: '#f1f5f9',
                        border: '1px solid #e2e8f0',
                        borderRadius: '6px',
                        padding: '8px 24px',
                        cursor: 'pointer',
                        fontSize: '14px',
                        color: '#475569',
                        fontWeight: 600
                      }}
                    >
                      Close
                    </button>
                  </div>
                  <style>{`
                    .custom-scrollbar::-webkit-scrollbar {
                      width: 8px;
                    }
                    .custom-scrollbar::-webkit-scrollbar-thumb {
                      background: #cbd5e1;
                      border-radius: 4px;
                    }
                    .custom-scrollbar::-webkit-scrollbar-track {
                      background: #f1f5f9;
                      border-radius: 4px;
                    }
                  `}</style>
                </div>
              )}

              <div style={{
                backgroundColor: '#f8f9fa',
                padding: '16px',
                borderRadius: '8px',
                marginBottom: '20px'
              }}>
                <div style={{ fontSize: '14px', color: '#6b7280', marginBottom: '8px' }}>
                  <strong>Session:</strong> {selectedSummary?.sessionId} | 
                  <strong> Event:</strong> {selectedSummary?.eventId} | 
                  <strong> Generated:</strong> {formatDateTime(selectedSummary?.createdAt)}
                </div>
              </div>

              {/* Show both summaries if available */}
              <div style={{ marginBottom: '20px' }}>
                <div style={{ fontWeight: 600, color: '#22396a', marginBottom: 4 }}>AI Generated Summary:</div>
                <AIFeedbackPanel summary={selectedSummary?.summary} />
              </div>
              {selectedSummary?.updatedSummary && (
                <div style={{ marginBottom: '20px' }}>
                  <div style={{ fontWeight: 600, color: '#0a7d3a', marginBottom: 4 }}>User Updated Summary:</div>
                  <AIFeedbackPanel summary={selectedSummary?.updatedSummary} />
                </div>
              )}
            </div>
          ) : (
            // Summary List View
            <div>
              {loading ? (
                <div style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '200px',
                  fontSize: '16px',
                  color: '#6b7280'
                }}>
                  Loading summaries...
                </div>
              ) : error ? (
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '200px',
                  fontSize: '16px',
                  color: '#dc2626'
                }}>
                  <div style={{ marginBottom: '8px' }}>⚠️ Error loading summaries</div>
                  <div style={{ fontSize: '14px', color: '#6b7280' }}>{error}</div>
                </div>
              ) : summaries.length === 0 ? (
                <div style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  height: '200px',
                  fontSize: '16px',
                  color: '#6b7280'
                }}>
                  No AI summaries found for this patient.
                </div>
              ) : (
                <div>
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                    {summaries.map((summary) => (
                      <div
                        key={summary.id}
                        onClick={() => handleSummaryClick(summary)}
                        style={{
                          border: '1px solid #e2e8f0',
                          borderRadius: '8px',
                          padding: '16px',
                          cursor: 'pointer',
                          backgroundColor: '#ffffff',
                          transition: 'all 0.2s ease',
                          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                        }}
                        onMouseOver={(e) => {
                          e.target.style.backgroundColor = '#f8f9fa';
                          e.target.style.borderColor = '#3b82f6';
                          e.target.style.transform = 'translateY(-1px)';
                        }}
                        onMouseOut={(e) => {
                          e.target.style.backgroundColor = '#ffffff';
                          e.target.style.borderColor = '#e2e8f0';
                          e.target.style.transform = 'translateY(0)';
                        }}
                      >
                        <div style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'flex-start',
                          marginBottom: '8px'
                        }}>
                          <div style={{
                            fontSize: '16px',
                            fontWeight: '600',
                            color: '#1f2937'
                          }}>
                            {formatDateTime(summary.createdAt)}
                          </div>
                          <div style={{
                            fontSize: '12px',
                            color: '#6b7280',
                            backgroundColor: '#f1f5f9',
                            padding: '4px 8px',
                            borderRadius: '4px'
                          }}>
                            Session {summary.sessionId}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default AIDataModal;
