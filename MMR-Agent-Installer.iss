; MMR-Agent Windows Installer Script
; Requires Inno Setup (https://jrsoftware.org/isinfo.php)

[Setup]
AppName=MMR-Agent
AppVersion=1.0.0
AppPublisher=ArcaAI
AppPublisherURL=https://www.arcaai.com
AppSupportURL=https://www.arcaai.com/support
AppUpdatesURL=https://www.arcaai.com/updates
DefaultDirName={pf}\MMR-Agent
DefaultGroupName=MMR-Agent
UninstallDisplayIcon={app}\MMR-Agent.exe
OutputBaseFilename=MMR-Agent-Installer
SetupIconFile=arca-logo.ico
Compression=lzma
SolidCompression=yes
LicenseFile=license.txt

[Files]
Source: "dist\MMR-Agent.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "dist\audio_agent_config.ini"; DestDir: "{app}"; Flags: ignoreversion
Source: "ARCA Privacy Policy.pdf"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
Name: "{group}\MMR-Agent"; Filename: "{app}\MMR-Agent.exe"; IconFilename: "{app}\MMR-Agent.exe"
Name: "{group}\Uninstall MMR-Agent"; Filename: "{uninstallexe}"

[Run]
Filename: "{app}\MMR-Agent.exe"; Description: "Start MMR-Agent now"; Flags: nowait postinstall skipifsilent

[Tasks]
Name: "desktopicon"; Description: "Create a &desktop icon"; GroupDescription: "Additional icons:"; Flags: unchecked
Name: "autostart"; Description: "Start MMR-Agent automatically with Windows"; GroupDescription: "Startup options:"; Flags: unchecked

[Registry]
; Optional: Add registry entries for service or startup

[CustomMessages]
WelcomeLabel1=Welcome to the MMR-Agent Setup Wizard
WelcomeLabel2=This will install MMR-Agent on your computer.

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"
