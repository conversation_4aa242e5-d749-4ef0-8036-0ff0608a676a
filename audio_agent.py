"""
Production-ready Audio Agent for Multi-mic Recording and Streaming

Author: <PERSON><PERSON><PERSON>ation: Chief Technology Officer
Organization: Kivotos AI Technology Private Ltd.

- Robust real-time DSP (Pedalboard)
- Graceful shutdown and thread cleanup
- Secure FastAPI configuration (OWASP-aligned)
- Logging, error handling, and resource management

Copyright (c) 2025 Kivotos AI Technology Private Ltd.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import sounddevice as sd
import soundfile as sf
import threading
import queue
import time
import os
import sys
import signal
import socket
from fastapi import FastAPI, Request, WebSocket, WebSocketDisconnect, Response
from pydantic import BaseModel
import uvicorn
from fastapi.middleware.cors import CORSMiddleware
import logging
import json
import asyncio
import numpy as np
import io
from pedalboard._pedalboard import Pedalboard
from pedalboard import Compressor, Limiter, NoiseGate, HighpassFilter, LowpassFilter
import configparser
from prometheus_client import start_http_server, Counter, Gauge, Histogram, generate_latest, CONTENT_TYPE_LATEST
from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.logging import LoggingInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.sdk.resources import SERVICE_NAME, Resource
from opentelemetry.sdk.trace import TracerProvider
from opentelemetry.sdk.trace.export import BatchSpanProcessor, ConsoleSpanExporter
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry import trace

# --- Logging Setup ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s',
    filename='audio_agent.log',
    filemode='a'
)
logger = logging.getLogger("audio_agent")
logger.addHandler(logging.StreamHandler(sys.stdout))
logger.info('Audio agent starting up...')

# --- Configurable Settings ---
config = configparser.ConfigParser()
config.read(os.path.join(os.path.dirname(__file__), 'audio_agent_config.ini'))
ALLOWED_ORIGIN = config.get('server', 'allowed_origin', fallback='http://localhost:5173')
ALLOWED_ORIGINS = [origin.strip() for origin in ALLOWED_ORIGIN.split(',')]
PORT = config.getint('server', 'port', fallback=5001)
HOST = config.get('server', 'host', fallback='0.0.0.0')
ENABLE_OPENTELEMETRY = config.getboolean('server', 'enable_opentelemetry', fallback=False)

# --- Dependency Check for OpenTelemetry ---
def check_opentelemetry_dependencies():
    try:
        import opentelemetry
        import opentelemetry.instrumentation.fastapi
        import opentelemetry.instrumentation.logging
        import opentelemetry.instrumentation.requests
        import opentelemetry.sdk.resources
        import opentelemetry.sdk.trace
        import opentelemetry.sdk.trace.export
        import opentelemetry.exporter.otlp.proto.http.trace_exporter
    except ImportError as e:
        logger.error(f"OpenTelemetry is enabled but required dependency is missing: {e}")
        sys.exit(1)

if ENABLE_OPENTELEMETRY:
    check_opentelemetry_dependencies()

# --- FastAPI App Setup ---
app = FastAPI(
    title="Multi-mic Audio Agent API",
    description="Production-ready backend for multi-mic audio streaming and DSP.",
    version="1.0.0"
)
# CORS: Restrict origins in production for security
app.add_middleware(
    CORSMiddleware,
    allow_origins=ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- Prometheus Metrics ---
REQUEST_COUNT = Counter('audio_agent_requests_total', 'Total HTTP requests', ['method', 'endpoint'])
RECORDING_ACTIVE = Gauge('audio_agent_recording_active', 'Number of active recording threads')
RECORDING_ERRORS = Counter('audio_agent_recording_errors_total', 'Total recording errors')
RECORDING_DURATION = Histogram('audio_agent_recording_duration_seconds', 'Duration of recording sessions (seconds)')

@app.middleware("http")
async def prometheus_metrics_middleware(request, call_next):
    method = request.method
    endpoint = request.url.path
    REQUEST_COUNT.labels(method=method, endpoint=endpoint).inc()
    response = await call_next(request)
    return response

@app.get("/metrics")
def metrics():
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)

# --- OpenTelemetry Tracing ---
def setup_opentelemetry():
    resource = Resource(attributes={SERVICE_NAME: "audio-agent"})
    provider = TracerProvider(resource=resource)
    trace.set_tracer_provider(provider)
    # Export to console (for demo); replace with OTLPSpanExporter for production
    processor = BatchSpanProcessor(ConsoleSpanExporter())
    provider.add_span_processor(processor)
    # To export to an observability backend, uncomment below and configure endpoint
    # otlp_exporter = OTLPSpanExporter(endpoint="http://localhost:4318/v1/traces", insecure=True)
    # provider.add_span_processor(BatchSpanProcessor(otlp_exporter))
    FastAPIInstrumentor.instrument_app(app)
    LoggingInstrumentor().instrument()
    RequestsInstrumentor().instrument()

if ENABLE_OPENTELEMETRY:
    setup_opentelemetry()

# Start Prometheus metrics server in background (default port 8001)
def start_prometheus_server():
    t = threading.Thread(target=start_http_server, args=(8001,), daemon=True)
    t.start()

start_prometheus_server()

# --- Global State ---
recording_threads = {}  # device -> Thread
recording_queues = {}   # device -> Queue (stop signal)
stream_queues = {}      # device -> Queue (audio chunks)
active_dsp_configs = {} # device -> DSP config
pause_events = {}       # device -> Event (pause signal)
CHUNK_SECONDS = 1       # Audio chunk duration (seconds)
shutdown_event = threading.Event()

def clean_device_name(device_name):
    """Extract clean device name from system path, especially for Bluetooth devices."""
    if not device_name:
        return device_name

    # Handle Bluetooth device names with system paths
    # Example: "Headset (@System32\drivers\bthhfenum.sys,#2;%1 Hands-Free%0 ;(JBL WAVE BEAM))"
    # Extract the part in the last parentheses
    if "(" in device_name:
        # Find the last occurrence of parentheses
        last_paren_start = device_name.rfind("(")
        if last_paren_start >= 0:
            # Extract content from last parentheses
            remaining = device_name[last_paren_start + 1:]
            # Remove all closing parentheses from the end
            clean_name = remaining.rstrip(")")
            if clean_name and clean_name != device_name:  # Only use if different and not empty
                return clean_name

    # Handle other system path patterns
    # Remove common system path prefixes
    if "@System32" in device_name or "drivers\\" in device_name:
        # Try to extract device name after semicolon patterns
        parts = device_name.split(";")
        for part in reversed(parts):  # Check from end to beginning
            part = part.strip()
            if part and not part.startswith("@") and not part.startswith("%"):
                # Remove parentheses if they wrap the entire string
                if part.startswith("(") and part.endswith(")"):
                    part = part[1:-1]
                if part:
                    return part

    return device_name

class MicRole(BaseModel):
    device: int
    role: str

# --- Utility: Port Check ---
def is_port_in_use(port):
    """Check if a TCP port is already in use."""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(("127.0.0.1", port)) == 0

# --- DSP Settings Persistence Utilities ---
DSP_SETTINGS_FILE = os.path.join(os.path.dirname(__file__), 'dspsettings.json')

def load_dsp_settings():
    """Load DSP settings from dspsettings.json, create if missing."""
    if not os.path.exists(DSP_SETTINGS_FILE):
        with open(DSP_SETTINGS_FILE, 'w') as f:
            json.dump({}, f)
    with open(DSP_SETTINGS_FILE, 'r') as f:
        try:
            return json.load(f)
        except Exception:
            return {}

def save_dsp_settings(settings):
    """Persist DSP settings to dspsettings.json."""
    with open(DSP_SETTINGS_FILE, 'w') as f:
        json.dump(settings, f, indent=2)

# --- On Startup: Load and apply persisted DSP/mic settings ---
PERSISTED_DSP_SETTINGS = load_dsp_settings()
logger.info(f"Loaded persisted DSP/mic settings: {PERSISTED_DSP_SETTINGS}")

# --- API Endpoints ---
@app.get("/list_mics")
def list_mics():
    """List all available unique microphone devices, with optional driver filtering on Windows. Fix: Do not deduplicate by name; number identical names."""
    logger.info("/list_mics called (refreshing device list)")
    sd._terminate()
    sd._initialize()
    devices = sd.query_devices()
    mics = []
    import platform
    driver_filter = None
    if platform.system() == "Windows":
        driver_filter = config.get('server', 'driver_name', fallback=None)
        if driver_filter:
            driver_filter = [d.strip().strip("'").strip('"') for d in driver_filter.split(',') if d.strip()]
        hostapis = list(sd.query_hostapis())
    else:
        hostapis = []
    # Track counts of identical names for numbering
    name_counts = {}
    for i, d in enumerate(devices):
        try:
            if isinstance(d, dict):
                max_input = d.get("max_input_channels", 0)
                name = d.get("name", str(i))
                hostapi = d.get("hostapi", None)
            else:
                max_input = getattr(d, "max_input_channels", 0)
                name = getattr(d, "name", str(i))
                hostapi = getattr(d, "hostapi", None)
            driver_name = None
            if platform.system() == "Windows" and hostapi is not None and hostapi < len(hostapis):
                try:
                    hostapi_entry = hostapis[hostapi]
                    if isinstance(hostapi_entry, dict):
                        driver_name = hostapi_entry.get('name', None)
                    elif isinstance(hostapi_entry, tuple) and len(hostapi_entry) > 0 and isinstance(hostapi_entry[0], dict):
                        driver_name = hostapi_entry[0].get('name', None)
                except Exception:
                    driver_name = None
            logger.info(f"Device: {name}, HostAPI: {hostapi}, Driver: {driver_name}, Filter: {driver_filter}")
            if driver_filter and driver_name and driver_name not in driver_filter:
                continue
            if int(max_input) > 0:
                # Clean the device name to remove system paths
                clean_name = clean_device_name(name)
                # Number identical names
                base_name = clean_name
                count = name_counts.get(base_name, 0) + 1
                name_counts[base_name] = count
                if count > 1:
                    display_name = f"{base_name} {count}"
                else:
                    display_name = base_name
                mics.append({"id": i, "name": display_name, "driver": driver_name})
        except Exception:
            continue
    logger.info(f"Microphones found: {mics}")
    return {"mics": mics}

@app.post("/start_recording")
async def start_recording(request: Request):
    """Start recording from selected microphones."""
    try:
        mics = json.loads((await request.body()).decode("utf-8"))
        logger.info(f"Parsed mics: {mics}")
    except Exception as e:
        logger.error(f"Could not parse request body: {e}")
        return {"error": str(e)}
    for mic in mics:
        device = mic.get('device')
        role = mic.get('role')
        dsp = mic.get('dsp', {})
        micname = mic.get('micname')
        if device is None:
            logger.warning(f"Skipping mic with device=None: {mic}")
            continue
        if device in recording_threads:
            logger.info(f"Mic {device} already recording.")
            continue
        stop_q = queue.Queue()
        stream_q = queue.Queue()
        pause_event = threading.Event()
        recording_queues[device] = stop_q
        stream_queues[device] = stream_q
        pause_events[device] = pause_event
        active_dsp_configs[device] = dsp
        t = threading.Thread(target=record_mic, args=(device, role, stop_q, stream_q, pause_event, dsp, micname), daemon=True)
        recording_threads[device] = t
        t.start()
        RECORDING_ACTIVE.inc()  # Increment active recording gauge
    logger.info("Recording started for all requested mics.")
    return {"status": "started"}

@app.post("/pause_recording")
def pause_recording():
    """Pause all active recordings without stopping them."""
    logger.info("/pause_recording called")
    for device, pause_event in pause_events.items():
        pause_event.set()
        logger.info(f"Paused recording for device {device}")
    logger.info("All recordings paused.")
    return {"status": "paused"}

@app.post("/resume_recording")
def resume_recording():
    """Resume all paused recordings."""
    logger.info("/resume_recording called")
    for device, pause_event in pause_events.items():
        pause_event.clear()
        logger.info(f"Resumed recording for device {device}")
    logger.info("All recordings resumed.")
    return {"status": "resumed"}

@app.post("/stop_recording")
def stop_recording():
    """Stop all active recordings and clean up resources."""
    logger.info("/stop_recording called")
    for q in recording_queues.values():
        q.put(None)
    for t in list(recording_threads.values()):
        if t.is_alive():
            t.join(timeout=2)
    recording_threads.clear()
    recording_queues.clear()
    stream_queues.clear()
    active_dsp_configs.clear()
    pause_events.clear()
    RECORDING_ACTIVE.set(0)
    logger.info("All recordings stopped.")
    return {"status": "stopped"}

@app.post("/update_dsp")
async def update_dsp(request: Request):
    """Update mic + DSP settings and persist them to dspsettings.json."""
    try:
        payload = json.loads((await request.body()).decode("utf-8"))
        logger.info(f"/update_dsp called. Payload: {payload}")
        # Validate and normalize
        new_settings = {}
        for mic_id, mic_cfg in payload.items():
            micname = mic_cfg.get("micname")
            selectedRole = mic_cfg.get("selectedRole")
            settingsParams = mic_cfg.get("settingsParams")
            deviceId = mic_cfg.get("deviceId", mic_id)
            if not micname or not selectedRole or not settingsParams:
                logger.warning(f"Missing required fields for mic {mic_id}: {mic_cfg}")
                continue
            new_settings[micname] = {
                "micname": micname,
                "deviceId": str(deviceId),
                "selectedRole": selectedRole,
                "settingsParams": settingsParams
            }
        # Load current settings
        current_settings = load_dsp_settings()
        # Remove obsolete entries
        for micname in list(current_settings):
            if micname not in new_settings:
                del current_settings[micname]
        # Update/add new entries
        current_settings.update(new_settings)
        save_dsp_settings(current_settings)
        logger.info(f"Persisted DSP/mic settings: {current_settings}")
        return {"status": "DSP/mic settings updated and persisted"}
    except Exception as e:
        logger.error(f"Error in /update_dsp: {e}")
        return {"error": str(e)}

@app.get("/dspsettings.json")
def get_dsp_settings():
    """Return the entire persisted DSP settings file."""
    settings = load_dsp_settings()
    return settings

@app.websocket("/audio_stream")
async def audio_stream(websocket: WebSocket):
    """WebSocket endpoint for live audio streaming to the frontend. Cleans up orphaned threads on disconnect and avoids double-close."""
    await websocket.accept()
    logger.info(f"[WS] Client {id(websocket)} connected for audio stream.")
    client_active = True
    ws_closed = False
    try:
        active_devices = list(stream_queues.keys())
        if not active_devices:
            logger.error("No active mics for streaming!")
            await websocket.close()
            ws_closed = True
            return
        while not shutdown_event.is_set() and client_active:
            sent_any = False
            for mic_idx in active_devices:
                stream_q = stream_queues.get(mic_idx)
                if stream_q is None:
                    continue
                try:
                    chunk = stream_q.get_nowait()
                except queue.Empty:
                    continue
                if chunk is None:
                    continue
                samplerate, wav_bytes = chunk
                header = mic_idx.to_bytes(1, 'big') + int(samplerate).to_bytes(4, 'big') + len(wav_bytes).to_bytes(4, 'big')
                packet = header + wav_bytes
                try:
                    await websocket.send_bytes(packet)
                    sent_any = True
                except Exception as e:
                    logger.error(f"[WS] Error sending audio to client: {e}")
                    client_active = False
                    break
            if not sent_any:
                await asyncio.sleep(0.01)
    except WebSocketDisconnect:
        logger.info(f"[WS] Client {id(websocket)} disconnected from audio stream.")
    except Exception as e:
        logger.error(f"[WS] Error in audio_stream: {e}")
    finally:
        # Only close if not already closed
        if not ws_closed and websocket.client_state and websocket.client_state.name != 'DISCONNECTED':
            try:
                await websocket.close()
            except Exception as e:
                logger.warning(f"[WS] Exception during websocket.close(): {e}")
        # --- Cleanup orphaned threads/queues if no clients are connected ---
        logger.info("[WS] Checking for orphaned threads after disconnect...")
        if not websocket.client_state or websocket.client_state.name == 'DISCONNECTED':
            stop_recording()
            logger.info("[WS] Orphaned threads/queues cleaned up after disconnect.")

# --- DSP Processing ---
def apply_dsp(frames, dsp, samplerate, chunk_frames):
    """Apply DSP (EQ, noise gate, compressor, limiter) to audio frames using Pedalboard."""
    try:
        if not dsp:
            return frames
        board = Pedalboard()
        eq = dsp.get('eq', {})
        low_cut = eq.get('lowCutFreq', 0)
        if low_cut > 0:
            board.append(HighpassFilter(cutoff_frequency_hz=low_cut))
        high_cut = eq.get('highCutFreq', 0)
        if high_cut > 0:
            board.append(LowpassFilter(cutoff_frequency_hz=high_cut))
        ng = dsp.get('noiseGate', {})
        ng_thresh = ng.get('threshold', 0)
        if ng_thresh > 0:
            board.append(NoiseGate(threshold_db=20 * np.log10(max(ng_thresh, 1e-8))))
        comp = dsp.get('compressor', {})
        comp_thresh = comp.get('threshold', 1)
        comp_ratio = comp.get('ratio', 1)
        if comp_thresh < 1 and comp_ratio > 1:
            board.append(Compressor(threshold_db=20 * np.log10(max(comp_thresh, 1e-8)), ratio=comp_ratio))
        lim = dsp.get('limiter', {})
        lim_thresh = lim.get('threshold', 1)
        if lim_thresh < 1:
            board.append(Limiter(threshold_db=20 * np.log10(max(lim_thresh, 1e-8))))
        frames = np.asarray(frames, dtype=np.float32).reshape(-1)
        frames = frames[np.newaxis, :]
        processed = board(frames, samplerate)
        frames = processed.flatten().astype(np.float32)
        if len(frames) != chunk_frames:
            logger.error("Final DSP output frame size mismatch. Resizing.")
            frames = np.resize(frames, chunk_frames)
    except Exception as e:
        logger.error(f"DSP Error: {e}")
        frames = np.resize(frames, chunk_frames)
    return frames

# --- Recording Thread ---
def record_mic(device, role, stop_q, stream_q, pause_event, dsp=None, micname=None):
    """Thread target: Record audio from a single mic, apply DSP, and stream/save chunks. Uses callback API for WDM-KS support."""
    import time
    start_time = time.time()
    RECORDING_ACTIVE.inc()
    logger.info(f"Starting record_mic for device {device}, role {role}, DSP: {dsp}, micname: {micname}")
    dev_info = sd.query_devices(device)
    samplerate = int(dev_info["default_samplerate"] if isinstance(dev_info, dict) else getattr(dev_info, "default_samplerate", 16000))
    chunk_frames = int(samplerate * CHUNK_SECONDS)
    os.makedirs("recordings", exist_ok=True)
    all_frames = []
    audio_buffer = []
    buffer_lock = threading.Lock()
    stop_flag = threading.Event()

    def callback(indata, frames, time_info, status):
        if status:
            logger.warning(f"InputStream status: {status}")
        # Only copy data to buffer if not paused
        if not pause_event.is_set():
            with buffer_lock:
                audio_buffer.append(indata.copy())
        # Check for stop signal
        if not stop_q.empty():
            signal_val = stop_q.get()
            if signal_val is None:
                stop_flag.set()

    try:
        with sd.InputStream(device=device, channels=1, samplerate=samplerate, dtype='float32', callback=callback):
            logger.info(f"InputStream (callback) opened for device {device}")
            while not shutdown_event.is_set() and not stop_flag.is_set():
                # If paused, just sleep and continue
                if pause_event.is_set():
                    time.sleep(0.1)
                    continue

                # Wait for enough frames to form a chunk
                with buffer_lock:
                    total_frames = sum(arr.shape[0] for arr in audio_buffer)
                if total_frames < chunk_frames:
                    time.sleep(0.01)
                    continue
                # Gather enough frames for a chunk
                with buffer_lock:
                    frames_list = []
                    frames_needed = chunk_frames
                    while audio_buffer and frames_needed > 0:
                        arr = audio_buffer[0]
                        if arr.shape[0] <= frames_needed:
                            frames_list.append(arr)
                            frames_needed -= arr.shape[0]
                            audio_buffer.pop(0)
                        else:
                            frames_list.append(arr[:frames_needed])
                            audio_buffer[0] = arr[frames_needed:]
                            frames_needed = 0
                    if not frames_list:
                        continue
                    frames = np.concatenate(frames_list, axis=0)
                # DSP processing
                # Use persisted DSP/mic settings if available
                dsp_cfg = None
                if micname and micname in PERSISTED_DSP_SETTINGS:
                    dsp_cfg = PERSISTED_DSP_SETTINGS[micname].get("settingsParams", dsp)
                elif dsp is not None:
                    dsp_cfg = dsp
                if dsp_cfg:
                    frames = apply_dsp(frames, dsp_cfg, samplerate, chunk_frames)
                all_frames.append(frames)
                buf = io.BytesIO()
                sf.write(buf, frames, samplerate, format='WAV', subtype='PCM_16')
                wav_bytes = buf.getvalue()
                try:
                    stream_q.put((samplerate, wav_bytes))
                except Exception as e:
                    logger.error(f"Error putting chunk in stream_q: {e}")
                time.sleep(0.01)
        if all_frames:
            all_frames = [f.flatten() if f.ndim > 1 else f for f in all_frames]
            full_audio = np.concatenate(all_frames, axis=0)
            ts = int(time.time())
            full_path = f"recordings/mic{device}_{role}_{ts}_full.wav"
            sf.write(full_path, full_audio, samplerate)
            logger.info(f"Saved full session to {full_path}")
    except Exception as e:
        RECORDING_ERRORS.inc()
        logger.error(f"Error in record_mic for device {device}, role {role}: {e}")
    finally:
        RECORDING_ACTIVE.dec()
        duration = time.time() - start_time
        RECORDING_DURATION.observe(duration)

# --- Graceful Shutdown ---
def handle_shutdown(signum, frame):
    """Signal handler for graceful shutdown (SIGINT/SIGTERM)."""
    logger.info(f"Received shutdown signal ({signum}), stopping agent gracefully...")
    shutdown_event.set()
    stop_recording()
    logger.info("Shutdown complete. Exiting.")
    sys.exit(0)

signal.signal(signal.SIGINT, handle_shutdown)
signal.signal(signal.SIGTERM, handle_shutdown)

@app.on_event("shutdown")
def on_shutdown():
    """FastAPI shutdown event: clean up all resources."""
    logger.info("FastAPI shutdown event: stopping all recordings and cleaning up.")
    shutdown_event.set()
    stop_recording()
    logger.info("Shutdown complete.")

# --- Main Entrypoint ---
if __name__ == "__main__":
    if is_port_in_use(PORT):
        logger.error(f"Port {PORT} is already in use. Please stop the other process or choose a different port.")
        sys.exit(1)
    try:
        # Swagger UI: http://<host>:<port>/docs
        uvicorn.run(app, host=HOST, port=PORT, log_level="info")
    except Exception as e:
        logger.error(f"Uvicorn server error: {e}")
    finally:
        shutdown_event.set()
        stop_recording()
        logger.info("Audio agent stopped.")
