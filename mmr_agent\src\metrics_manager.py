from prometheus_client import Counter, Gauge, Histogram

class MetricsManager:
    def __init__(self):
        self.REQUEST_COUNT = Counter('audio_agent_requests_total', 'Total HTTP requests', ['method', 'endpoint'])
        self.RECORDING_ACTIVE = Gauge('audio_agent_recording_active', 'Number of active recording threads')
        self.RECORDING_ERRORS = Counter('audio_agent_recording_errors_total', 'Total recording errors')
        self.RECORDING_DURATION = Histogram('audio_agent_recording_duration_seconds', 'Duration of recording sessions (seconds)')

    def inc_request_count(self, method, endpoint):
        self.REQUEST_COUNT.labels(method=method, endpoint=endpoint).inc()

    def set_recording_active(self, value):
        self.RECORDING_ACTIVE.set(value)

    def inc_recording_errors(self):
        self.RECORDING_ERRORS.inc()

    def observe_recording_duration(self, duration):
        self.RECORDING_DURATION.observe(duration)