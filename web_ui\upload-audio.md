# Feature Request: Upload API for Full Audio Transcription and Medical Transcript Generation

## Summary

Create a new API endpoint in the `web_ui` server to allow uploading a recorded audio file. The server should:

- Accept a full audio file upload (e.g., WAV) from the client.
- Send the uploaded audio file to Azure Speech Service for full transcription (not streaming).
- Take the resulting transcript and send it to OpenAI (e.g., GPT-4) to generate a structured medical transcript.
- Return the OpenAI-generated medical transcript to the client.

## Requirements

- API should accept large audio files (multi-minute recordings).
- Use Azure Speech Service **batch transcription** endpoint for best accuracy.
- Handle both **English** and **Malayalam** audio.
- After receiving the transcript from Azure, send it to OpenAI with a prompt to generate a medical transcript (e.g., **SOAP note** or **summary**).
- Return both the **raw transcript** and the **OpenAI medical transcript** in the API response.
- Add error handling for both **Azure** and **OpenAI** API failures.
- All API keys and endpoints should be **configurable** via environment variables or configuration files.

## Acceptance Criteria

- [ ] Client can upload a full audio file and receive a medical transcript in response.
- [ ] Works for both English and Malayalam audio.
- [ ] Handles large files robustly (multi-minute audio).
- [ ] Returns both raw Azure transcript and the generated medical summary.
- [ ] Configuration supports easy setup of Azure and OpenAI API keys and endpoints.
- [ ] Provides meaningful error messages for Azure/OpenAI failures.

## Context

This feature will enable users to upload complete audio recordings for post-session medical documentation, complementing the current streaming/real-time transcription features.

