/**
 * Test script to verify the enhanced getSummary functionality
 * This script tests that previous case notes are included in live recording summaries
 */

const axios = require('axios');
const { Pool } = require('pg');

// Database configuration
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'clinical_db',
  password: process.env.DB_PASSWORD || 'password',
  port: process.env.DB_PORT || 5432,
});

async function testSummaryEnhancement() {
  console.log('🧪 Testing Summary Enhancement with Previous Case Notes...\n');
  
  let client;
  try {
    client = await pool.connect();
    
    // Test data
    const testPatientId = 'TEST-PATIENT-001';
    const testSessionId = 'TEST-SESSION-001';
    const testEventId = 'TEST-EVENT-001';
    const previousCaseNotes = `
## Previous Medical History Summary

### COVID-19 Treatment (June 2025)
- **Chief Co<PERSON>laint**: Persistent cough and fever for 5 days
- **Diagnosis**: COVID-19 pneumonia
- **Treatment**: Oxygen therapy, azithromycin, dexamethasone
- **Outcome**: Complete recovery from respiratory symptoms

### Post-COVID Follow-up (July 2025)
- **New Symptoms**: Chest discomfort and palpitations during activity
- **Assessment**: Post-viral myocarditis suspected
- **Plan**: ECG, echocardiogram, beta-blocker therapy
- **Status**: Gradual return to physical activity advised
    `.trim();

    // Step 1: Insert test patient with previous case notes
    console.log('1️⃣ Setting up test patient with previous case notes...');
    await client.query(`
      INSERT INTO patients (reg_no, name, dob, gender, previous_case_notes_summary)
      VALUES ($1, $2, $3, $4, $5)
      ON CONFLICT (reg_no) DO UPDATE SET
        previous_case_notes_summary = EXCLUDED.previous_case_notes_summary
    `, [testPatientId, 'Test Patient', '1990-01-01', 'Male', previousCaseNotes]);
    
    // Step 2: Insert test recording summary
    console.log('2️⃣ Setting up test recording session...');
    await client.query(`
      INSERT INTO recording_summaries (session_id, patient_id, event_id, created_at)
      VALUES ($1, $2, $3, $4)
      ON CONFLICT (session_id, patient_id, event_id) DO NOTHING
    `, [testSessionId, testPatientId, testEventId, new Date()]);
    
    // Step 3: Insert test recording file
    console.log('3️⃣ Setting up test recording file...');
    await client.query(`
      INSERT INTO recording_files (filepath, session_id, event_id, created_at)
      VALUES ($1, $2, $3, $4)
      ON CONFLICT DO NOTHING
    `, ['test_audio.wav', testSessionId, testEventId, new Date()]);
    
    console.log('✅ Test data setup complete!\n');
    
    // Step 4: Test the enhanced summary generation
    console.log('4️⃣ Testing enhanced summary generation...');
    console.log('📝 This would normally call the OpenAI API with enhanced prompt');
    console.log('🔍 Checking if patient data is correctly retrieved...\n');
    
    // Verify patient data retrieval
    const patientResult = await client.query(
      'SELECT previous_case_notes_summary FROM patients WHERE reg_no = $1',
      [testPatientId]
    );
    
    if (patientResult.rows.length > 0 && patientResult.rows[0].previous_case_notes_summary) {
      console.log('✅ Previous case notes found for patient:');
      console.log('📋 Case Notes Preview:');
      console.log(patientResult.rows[0].previous_case_notes_summary.substring(0, 200) + '...\n');
    } else {
      console.log('❌ No previous case notes found for patient\n');
    }
    
    // Verify recording session data
    const sessionResult = await client.query(
      'SELECT patient_id FROM recording_summaries WHERE session_id = $1 AND event_id = $2',
      [testSessionId, testEventId]
    );
    
    if (sessionResult.rows.length > 0) {
      console.log('✅ Recording session found with patient_id:', sessionResult.rows[0].patient_id);
    } else {
      console.log('❌ Recording session not found');
    }
    
    console.log('\n🎉 Test completed successfully!');
    console.log('📝 The enhanced getSummary function should now:');
    console.log('   • Retrieve patient_id from recording_summaries');
    console.log('   • Fetch previous case notes from patients table');
    console.log('   • Include case notes in the AI prompt for better context');
    console.log('   • Generate more comprehensive summaries for live recordings\n');
    
    // Cleanup
    console.log('🧹 Cleaning up test data...');
    await client.query('DELETE FROM recording_files WHERE session_id = $1', [testSessionId]);
    await client.query('DELETE FROM recording_summaries WHERE session_id = $1', [testSessionId]);
    await client.query('DELETE FROM patients WHERE reg_no = $1', [testPatientId]);
    console.log('✅ Cleanup complete!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    if (client) client.release();
    await pool.end();
  }
}

// Run the test
if (require.main === module) {
  testSummaryEnhancement();
}

module.exports = { testSummaryEnhancement };
