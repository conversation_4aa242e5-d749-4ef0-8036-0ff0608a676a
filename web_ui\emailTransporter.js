// Nodemailer config for sending report issue emails
const nodemailer = require('nodemailer');

// Configure your SMTP transport (use environment variables for real deployment)
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: process.env.SMTP_PORT ? parseInt(process.env.SMTP_PORT) : 587, // default to 587 for STARTTLS
  secure: process.env.SMTP_SECURE === 'true' || false, // false for port 587 (STARTTLS), true for 465 (SSL)
  auth: {
    user: process.env.SMTP_USERNAME || '<EMAIL>',
    pass: process.env.SMTP_PASSWORD || 'your_gmail_app_password',
  },
});

module.exports = transporter;
