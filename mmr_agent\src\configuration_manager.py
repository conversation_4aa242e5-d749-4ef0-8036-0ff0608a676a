import configparser
import os
import sys
import logging
from io import StringIO

class ConfigurationManager:
    # Embedded configuration - this will be used when no external config file is found
    EMBEDDED_CONFIG = """[server]
allowed_origin = http://localhost:5173,https://arcaai-u2204.bcmch.org
port = 5001
host = 0.0.0.0
enable_opentelemetry = False
driver_name = Windows WDM-KS

[environment]
# Environment mode: development or production
env = development
# Admin password for the default admin user (environment variable takes precedence)
admin_password = admin

[security]
# JWT secret key for token signing (environment variable takes precedence)
# Use a strong, unique secret key for production
jwt_secret = dev-jwt-secret-key-for-audio-agent-2024
jwt_algorithm = HS256
jwt_expire_minutes = 30
jwt_refresh_expire_minutes = 1440
idle_timeout_minutes = 30
rate_limit_token = 5/minute
max_login_attempts = 5
lockout_duration_minutes = 15
# Content Security Policy settings
csp_default_src = 'self'
csp_script_src = 'self' 'unsafe-inline'
csp_style_src = 'self' 'unsafe-inline'

[logging]
log_level = INFO
log_file = audio_agent.log
log_filemode = a
enable_audit_log = True
audit_log_file = audit.log

[api]
title = Multi-mic Audio Agent API
description = Production-ready backend for multi-mic audio streaming and DSP.
version = 1.0.0
"""

    def __init__(self, config_file='audio_agent_config.ini'):
        self.config = configparser.ConfigParser()
        self.logger = logging.getLogger("config_manager")
        config_loaded = False

        # Determine the correct path for config file based on execution context
        if getattr(sys, 'frozen', False):
            # Running as PyInstaller executable - try to find external config first
            app_dir = os.path.dirname(sys.executable)
        else:
            # Running as Python script - look in parent directory (project root)
            app_dir = os.path.dirname(os.path.dirname(__file__))

        self.config_path = os.path.join(app_dir, config_file)

        # Try to load external config file first
        if os.path.exists(self.config_path):
            try:
                self.config.read(self.config_path)
                # Only log to file, not console when running as executable
                if not getattr(sys, 'frozen', False):
                    print(f"INFO: External config file loaded from {self.config_path}")
                config_loaded = True
            except Exception as e:
                if not getattr(sys, 'frozen', False):
                    print(f"WARNING: Failed to load external config file: {e}")

        # If external config not found or failed to load, try alternative locations
        if not config_loaded:
            alt_paths = [
                os.path.join(os.getcwd(), config_file),  # Current working directory
                os.path.join(os.path.dirname(__file__), config_file),  # Same directory as this file
            ]
            for alt_path in alt_paths:
                if os.path.exists(alt_path):
                    try:
                        self.config_path = alt_path
                        self.config.read(self.config_path)
                        if not getattr(sys, 'frozen', False):
                            print(f"INFO: Config file found at alternative location: {self.config_path}")
                        config_loaded = True
                        break
                    except Exception as e:
                        if not getattr(sys, 'frozen', False):
                            print(f"WARNING: Failed to load config from {alt_path}: {e}")

        # If no external config found, use embedded config
        if not config_loaded:
            try:
                self.config.read_string(self.EMBEDDED_CONFIG)
                if not getattr(sys, 'frozen', False):
                    print("INFO: Using embedded configuration (no external config file found)")
                self.config_path = "embedded"
                config_loaded = True
            except Exception as e:
                if not getattr(sys, 'frozen', False):
                    print(f"ERROR: Failed to load embedded configuration: {e}")
                raise RuntimeError("Failed to load any configuration")

        if not config_loaded:
            raise RuntimeError(f"Failed to load configuration from any source")

    def get(self, section, key, fallback=None):
        try:
            return self.config.get(section, key, fallback=fallback)
        except (configparser.NoSectionError, configparser.NoOptionError):
            return fallback

    def getint(self, section, key, fallback=None):
        try:
            return self.config.getint(section, key, fallback=fallback)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return fallback

    def getboolean(self, section, key, fallback=None):
        try:
            return self.config.getboolean(section, key, fallback=fallback)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return fallback