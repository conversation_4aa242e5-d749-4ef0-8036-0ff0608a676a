import React from 'react';
import DSPControls from './DSPControls.jsx';

const ROLES = ['Doctor', 'Patient', 'Nurse', 'Other'];

function Sidebar({
  mics,
  micRoles,
  setMicRoles,
  onRefresh,
  onCollapse,
  dspConfigs,
  setDSPConfigs,
  recording,
  onApplyDSP,
  isAdminRole,
  isCollapsed = false
}) {
  return (
    <div className={`sidebar ${isCollapsed ? 'sidebar-collapsed' : ''}`}>
      <div className="sidebar-header">
        <button
          className="sidebar-toggle"
          onClick={onCollapse}
          title="Collapse menu"
          disabled={recording && window.innerWidth > 1440}
          style={{
            display: isCollapsed ? 'none' : 'block'
          }}
        >
          ←
        </button>
        <span className="sidebar-title" style={{ display: isCollapsed ? 'none' : 'block' }}>
          Microphones
        </span>
        <button
          className="refresh-btn"
          onClick={onRefresh}
          title="Refresh mic list"
          disabled={recording}
          style={{ display: isCollapsed ? 'none' : 'block' }}
        >
          ⟳
        </button>
      </div>

      {/* Apply DSP button for admins */}
       <button
        className="btn btn-light"
        style={{
          margin: '16px 0 16px 0',
          width: '100%',
          fontSize:'16px',
          display: isCollapsed ? 'none' : 'block'
        }}
        onClick={onApplyDSP}
        disabled={recording}
      >
        Save & Apply
      </button>

      <div className="sidebar-mic-list" style={{ display: isCollapsed ? 'none' : 'block' }}>
        {mics.length === 0 && <div className="sidebar-mic-none">No microphones found</div>}
        {mics.map(mic => {
          const selected = micRoles.find(m => m.deviceId === String(mic.id));
          return (
            <div className="sidebar-mic-row" key={mic.id}>
              <div style={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                <input
                  type="checkbox"
                  checked={!!selected}
                  disabled={recording}
                  onChange={e => {
                    const updated = micRoles.filter(m => m.deviceId !== String(mic.id));
                    if (e.target.checked) {
                      updated.push({ deviceId: String(mic.id), role: selected ? selected.role : ROLES[0],micname: mic.name });
                    }
                    setMicRoles(updated);
                  }}
                  style={{ marginRight: 8 }}
                />
                <span className="sidebar-mic-label">{mic.name || `Mic ${mic.id}`}</span>
                {selected && (
                  <select
                    className="sidebar-role-select"
                    value={selected.role}
                    disabled={recording}
                    onChange={e => {
                      const updated = micRoles.map(m => m.deviceId === String(mic.id) ? { ...m, role: e.target.value,micname: selected.micname  } : m);
                      setMicRoles(updated);
                    }}
                  >
                    {ROLES.map(role => <option key={role} value={role}>{role}</option>)}
                  </select>
                )}
              </div>
              {selected && (
                <div style={{ width: '100%' }}>
                  <DSPControls
                    dsp={dspConfigs[mic.id] || {
                      noiseGate: { threshold: 0.1, attack: 0.01, hold: 0.01, release: 0.01, ratio: 2 },
                      compressor: { threshold: 0.1, attack: 0.01, hold: 0.01, release: 0.05, ratio: 2 },
                      limiter: { threshold: 0.39, ratio: 10 },
                      eq: {
                        lowCutFreq: 400,        // updated from 1000 or 20
                        lowCutSlope: 18,        // updated from 6
                        highCutFreq: 10000,     // updated from 2000
                        highCutSlope: 18,       // updated from 6
                        mid1Freq: 500,
                        mid1Q: 1,
                        mid2Freq: 4000,
                        mid2Q: 1
                      }
                    }}
                    onChange={(section, param, value) => setDSPConfigs(prev => ({
                      ...prev,
                      [mic.id]: {
                        ...((prev[mic.id]) || {}),
                        [section]: {
                          ...(((prev[mic.id] || {})[section]) || {}),
                          [param]: value
                        }
                      }
                    }))}
                  />
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}

export default Sidebar;
