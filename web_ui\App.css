html, body, #root, .app-container {
  box-sizing: border-box;
}
body,
html,
#root,
.main-bg {
  height: 100%;
  margin: 0;
  font-family: 'Segoe UI', Arial, sans-serif;
  background: #f7fafd;
}

html,
body,
#root {
  font-size: 15px;
}

.main-bg {
  display: flex;
  min-height: 100vh;
  background: #f7fafd;
  position: relative;
  font-size: 1rem;
}

.app-container {
  display: flex;
  height: 100vh;
  background: #f7fafd;
  /* width: 100%; */
  overflow-x: hidden;
  position: relative;
  padding: 12px 12px 12px 12px;
  box-sizing: border-box;
}

/* Responsive app container */
@media (max-width: 1200px) {
  .app-container {
    flex-direction: column;
    overflow-y: auto;
    overflow-x: hidden;
  }

  /* Sidebar styles are now handled by the 1440px breakpoint for overlay behavior */
}

@media (max-width: 768px) {
  .app-container {
    padding: 8px;
  }
}

@media (max-width: 480px) {
  .app-container {
    padding: 6px;
  }
}

/* Header full width */
.header {
  width: 100%;
  box-sizing: border-box;
  background: #D6E5F5;
  color: #333333;
  /* padding: 18px 32px 10px 32px; */
  border-radius: 0 0 12px 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* margin-bottom: 18px; */
  padding: 20px 10px 10px 10px;
  border-radius: 12px;
  /* margin: 12px  12px 0 0; */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 100; /* Ensure header stays above other content */
}

.header-title {
  font-size: 1.5rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: 10px;
}

.header-session {
  display: flex;
  align-items: center;
  gap: 18px;
  font-size: 1rem;
  flex-wrap: wrap;
  justify-content: center;
}

/* .feedback-section {
  background-color: #D6E5F5;
} */

.feedback-section-main {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-top: 10px;
  margin-bottom: 10px;
  height: calc(100vh - 320px);
  box-sizing: border-box;
}

/* Responsive feedback section */
@media (max-width: 1200px) {
  .feedback-section-main {
    height: auto;
    min-height: 400px;
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .feedback-section-main {
    margin-top: 8px;
    margin-bottom: 8px;
    min-height: 300px;
  }
}

@media (max-width: 480px) {
  .feedback-section-main {
    margin-top: 6px;
    margin-bottom: 6px;
    min-height: 250px;
  }
}

.main-panels-row {
  display: flex;
  flex-direction: row;
  gap: 0;
  justify-content: center;
  align-items: stretch;
  width: 100%;
  margin-top: 0;
  height: 100%;
  max-width: 1800px;
  margin-left: auto;
  margin-right: auto;
}

.conversation-panel {
  width: 50%;
  flex-shrink: 0;
  border-radius: 10px;
  margin-bottom: 0;
  padding: 10px;
  overflow-y: auto;
  min-width: 400px;
  max-width: 900px;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2px 8px #e3eaf2;
  border: 1px solid #DDDDDD;
  font-size: 13px;
}

.ai-feedback-panel {
  width: 50%;
  flex-shrink: 0;
  background: #f3f6fa;
  border-radius: 10px;
  padding: 18px;
  margin: 0 0 0 24px;
  overflow-y: auto;
  box-shadow: 0 2px 8px #e3eaf2;
  min-width: 650px;
  max-width: 780px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  border: 1px solid #DDDDDD
}

/* When conversation panel is hidden (non-admin users), make AI feedback panel larger */
.main-panels-row.conversation-hidden .panel-group .ai-feedback-panel {
  width: 80% !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

/* Additional fallback - target when first panel-group is hidden */
.main-panels-row .panel-group[style*="display: none"] ~ .panel-group .ai-feedback-panel,
.main-panels-row .panel-group[style*="display:none"] ~ .panel-group .ai-feedback-panel {
  width: 80% !important;
  max-width: 1200px !important;
  margin: 0 auto !important;
}

.share-feedback-row {
  margin-top: 18px;
  margin-bottom: 0;
  display: flex;
  align-items: flex-end;
  gap: 12px;
}

/* Large screens - optimize layout */
@media (max-width: 1600px) {
  .header {
    padding: 18px 16px 8px 16px;
  }

  .content-row {
    padding: 16px 24px 0 24px;
  }
}

/* Medium screens - adjust panel widths and header */
@media (max-width: 1400px) {
  .conversation-panel {
    width: 50%;
  }

  .ai-feedback-panel {
    width: 50%;
    margin-left: 16px;
  }

  /* Single panel on medium screens */
  .main-panels-row.conversation-hidden .panel-group .ai-feedback-panel {
    width: 85% !important;
    max-width: 1000px !important;
    margin: 0 auto !important;
  }

  /* Header adjustments for medium screens */
  .header {
    padding: 16px 12px 8px 12px;
  }

  .header-session {
    gap: 14px;
    font-size: 0.95rem;
  }

  .controls-container {
    gap: 8px;
  }

  .btn {
    padding: 7px 14px;
    font-size: 13px;
    min-width: 80px;
  }
}

/* Tablet screens - adjust layout */
@media (max-width: 1200px) {
  .main-panels-row {
    flex-direction: column;
    gap: 18px;
    align-items: stretch;
    height: auto;
  }

  .ai-feedback-panel,
  .conversation-panel {
    width: 100%;
    max-width: 98vw;
    min-width: 0;
    height: auto;
  }

  .ai-feedback-panel {
    margin-left: 0;
    margin-top: 0;
  }

  .conversation-panel {
    width: 100%;
    max-width: 98vw;
    min-width: 0;
    height: auto;
  }

  /* Single panel on small screens */
  .main-panels-row.conversation-hidden .panel-group .ai-feedback-panel {
    width: 100% !important;
    max-width: 98vw !important;
    margin: 0 auto !important;
  }

  /* Header adjustments for tablet */
  .header {
    padding: 14px 10px 8px 10px;
  }

  .header-session {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: 12px;
    font-size: 0.9rem;
  }

  .controls-container {
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
  }

  .sidebar {
    width: 100%;
    max-width: none;
    margin-right: 0;
    margin-bottom: 12px;
  }

  .content-row {
    flex-direction: column;
    padding: 14px 16px 0 16px;
    gap: 14px;
  }
}

/* Small tablet screens */
@media (max-width: 1024px) {
  .header-session {
    flex-direction: column;
    gap: 10px;
  }

  .controls-container {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: 6px;
  }

  .btn {
    min-width: 70px;
    padding: 6px 12px;
    font-size: 12px;
  }

  .lang-select select {
    min-width: 100px;
    padding: 6px 24px 6px 10px;
    font-size: 12px;
  }
}

.content-row {
  display: flex;
  flex-direction: row;
  gap: 18px;
  padding: 18px 32px 0 32px;
}

.sidebar {
  width: 320px;
  background: #D6E5F5;
  color: #fff;
  /* padding: 20px 10px 10px 10px; */
  border-radius: 12px;
  /* margin: 12px 0 12px 12px; */
  overflow-y: auto;
  min-width: 260px;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  transition: margin-left 0.2s, width 0.2s;
  margin-right: 12px;
  box-sizing: border-box;
}

.sidebar-collapsed {
  width: 48px;
  min-width: 0;
  padding: 0;
  margin-left: 0;
  overflow: hidden;
}

.sidebar-header {
  display: flex;
  /* flex-direction: column; */
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  position: relative;
  margin: 12px;
  padding: 12px 0;
}

.sidebar-title {
  font-size: 1.15rem;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333333;
  margin-top: 10px;
  margin-left: 10px;
  /* increased from 36px for more space */
}

.sidebar-header-row {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 6px;
  margin-left: 44px;
  /* space for collapse button */
}

.refresh-btn {
  left: 12px;
  top: 12px;
  z-index: 10;
  background: #22396a;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 1.2rem;
  cursor: pointer;
  box-shadow: 2px 0 8px #e3eaf2;
  transition: background 0.2s;
}

.refresh-btn:hover {
  background: #2e4a7a;
}

.sidebar-mic-list {
  flex: 1;
  overflow-y: auto;
}

.sidebar-mic-row {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 10px;
  gap: 4px;
  background: #22396a;
  border-radius: 6px;
  padding: 6px 8px 8px 8px;
}

.sidebar-mic-label {
  font-size: 1rem;
  color: #fff;
  margin-bottom: 2px;
}

.sidebar-role-select {
  border-radius: 4px;
  border: 1px solid #b0c4de;
  padding: 2px 8px;
  font-size: 0.98rem;
  background: #fff;
  color: #1a355e;
  margin-bottom: 4px;
  margin-left: 15px
}

.sidebar-mic-none {
  color: #ffe066;
  font-size: 1rem;
  margin-top: 20px;
}

.dsp-controls {
  width: 96%;
  margin: 8px auto 12px auto;
  background: #D6E5F5;
  color: #333333;
  border-radius: 8px;
  padding: 10px 18px 8px 18px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  box-sizing: border-box;
  align-items: flex-start; /* Ensure left alignment of all children */
}

.dsp-controls details,
.dsp-controls label,
.dsp-controls input,
.dsp-controls summary,
.dsp-controls span {
  text-align: left !important;
  align-items: flex-start !important;
  justify-content: flex-start !important;
}

.dsp-controls label {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 0.98em;
  margin-bottom: 2px;
  gap: 8px;
  width: 100%;
}

.dsp-controls input[type="range"] {
  flex: 1;
  margin-left: 8px;
}

.sidebar-toggle {
  /* position: absolute; */
  left: 12px;
  top: 12px;
  z-index: 10;
  background: #22396a;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 1.2rem;
  cursor: pointer;
  box-shadow: 2px 0 8px #e3eaf2;
  transition: background 0.2s;
}

.sidebar-toggle:hover {
  background: #2e4a7a;
}

.sidebar-toggle-collapsed {
  position: absolute;
  left: 16px;
  top: 16px;
  background: #1a355e;
  color: #ffe066;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  cursor: pointer;
  box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
  transition: all 0.2s ease;
}

.sidebar-toggle-collapsed:hover {
  background: #2e4a7a;
  transform: scale(1.05);
  box-shadow: 2px 2px 12px rgba(0, 0, 0, 0.2);
}

/* Responsive adjustments for medium and smaller screens */
@media (max-width: 1440px) {
  .sidebar-toggle-collapsed {
    left: 16px;
    top: 16px;
    width: 40px;
    height: 40px;
    font-size: 1.1rem;
    z-index: 1000; /* Ensure it's above other content */
  }

  /* On medium screens and below, sidebar should be collapsible and overlay */
  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 999;
    transform: translateX(0);
    transition: transform 0.3s ease;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  }

  .sidebar.sidebar-collapsed {
    transform: translateX(-100%);
    width: 320px; /* Keep original width when hidden */
  }

  /* Ensure hamburger menu is always visible when sidebar is collapsed */
  .sidebar-toggle-collapsed {
    display: flex !important;
  }

  /* Add backdrop overlay when sidebar is open */
  .sidebar-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 998;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
  }

  .sidebar-backdrop.active {
    opacity: 1;
    visibility: visible;
  }
}

@media (max-width: 768px) {
  .sidebar-toggle-collapsed {
    left: 12px;
    top: 12px;
    width: 36px;
    height: 36px;
    font-size: 1rem;
    z-index: 1000; /* Ensure it's above other content */
  }

  /* Header responsive adjustments */
  .header {
    padding: 15px 8px 8px 8px;
    margin: 8px;
    border-radius: 8px;
  }

  .header-session {
    flex-direction: column;
    gap: 12px;
    text-align: center;
    font-size: 0.9rem;
  }

  .controls-container {
    flex-direction: column;
    gap: 8px;
    width: 100%;
    align-items: stretch;
  }

  .lang-select {
    width: 100%;
  }

  .lang-select select {
    width: 100%;
    min-width: auto;
  }

  .btn {
    width: 100%;
    min-width: auto;
    padding: 10px 16px;
    font-size: 16px;
  }

  /* Sidebar responsive adjustments - remove duplicate since it's now in 1200px breakpoint */
  .content-row {
    flex-direction: column;
    padding: 12px 8px 0 8px;
    gap: 12px;
  }

  /* Panel adjustments for mobile */
  .main-panels-row {
    padding: 0 8px;
  }

  .conversation-panel,
  .ai-feedback-panel {
    padding: 12px;
    margin-bottom: 8px;
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .sidebar-toggle-collapsed {
    left: 8px;
    top: 8px;
    width: 32px;
    height: 32px;
    font-size: 0.9rem;
    z-index: 1001; /* Higher than sidebar */
  }

  /* Extra small screens - sidebar should be fully collapsible */
  .sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    width: 280px; /* Slightly smaller on xs */
    transform: translateX(0);
    transition: transform 0.3s ease;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.2);
  }

  .sidebar.sidebar-collapsed {
    transform: translateX(-100%);
  }

  /* Ensure main content adjusts when sidebar is open on xs */
  .app-main-content {
    transition: margin-left 0.3s ease;
  }

  /* Responsive styles for sidebar buttons on xs screens */
  .sidebar-toggle {
    padding: 6px 10px;
    font-size: 1.1rem;
    min-width: 36px;
    height: 36px;
    display: flex !important;
    align-items: center;
    justify-content: center;
    z-index: 1002;
    position: relative;
  }

  .refresh-btn {
    padding: 6px 10px;
    font-size: 1.1rem;
    min-width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .sidebar-header {
    padding: 10px 12px;
    flex-wrap: wrap;
    gap: 8px;
  }

  .sidebar-title {
    font-size: 1.05rem;
    margin-left: 0;
    margin-top: 0;
    margin-bottom: 8px;
  }

  /* Extra small screens */
  .header {
    padding: 12px 6px 6px 6px;
    margin: 6px;
  }

  .header-session {
    font-size: 0.8rem;
    gap: 8px;
  }

  .controls-container {
    gap: 6px;
  }

  .btn {
    padding: 8px 12px;
    font-size: 14px;
  }

  .content-row {
    padding: 8px 6px 0 6px;
  }

  .conversation-panel,
  .ai-feedback-panel {
    padding: 8px;
    font-size: 13px;
  }
}

.dsp-section {
  background: #2c3e50;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 18px;
}

.dsp-section h4 {
  margin: 0 0 8px 0;
  font-size: 1.1em;
}

.dsp-slider {
  margin-bottom: 10px;
}

.app-main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  margin-left: 0;
  transition: all 0.3s ease;
  width: 100%;
  position: relative;
  overflow-y: auto;
  box-sizing: border-box;
}

/* Responsive main content */
@media (max-width: 1200px) {
  .app-main-content {
    margin-left: 0;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .app-main-content {
    overflow-y: visible;
    height: auto;
  }
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin: 12px 0 12px 0;
  padding: 0 24px;
}

.app-main-content-sidebar {
  margin-left: 20px;
}

.footer-note {
  background: #ffe066;
  color: #444;
  font-size: 1rem;
  padding: 8px 32px;
  margin-top: 18px;
  border-top: 1px solid #f5d442;
}

.multi-mic-audio-player {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
  /* Add spacing between players */
}

.audio-track {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 8px;
  background-color: #f9f9f9;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-message {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 10px 20px;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  border-radius: 4px;
  z-index: 1000;
  animation: fadeOut 3s forwards;
}

.patient-info {
  font-weight: 600;
  color: #333333;
}

.controls-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.lang-select {
  position: relative;
  display: inline-block;
}

.lang-select select {
  appearance: none;
  background-color: white;
  border: 1px solid #DDDDDD;
  border-radius: 4px;
  padding: 8px 30px 8px 12px;
  font-size: 14px;
  color: #333333;
  cursor: pointer;
  min-width: 140px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.lang-select::after {
  content: '';
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 5px solid #555;
  pointer-events: none;
}

/* Button Styling */
.btn {
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 90px;
  transition: background-color 0.2s, transform 0.1s;
  position: relative; /* Add this to ensure tooltip positioning works */
}

.btn:active {
  transform: translateY(1px);
}

/* Add or update button disabled styles */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background-color: #cccccc;
  color: #666666;
  box-shadow: none;
}

.btn-primary:disabled {
  background-color: #8bafd3;
  color: #e0e0e0;
}

.btn-primary:disabled .icon-mic {
  background-color: #e0e0e0;
}

/* Tooltip ONLY for disabled buttons */
.btn:disabled[data-tooltip]:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #333;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
  margin-bottom: 5px;
  pointer-events: none; /* Prevents the tooltip from interfering with hover */
}

/* Remove any previous tooltip styles that might conflict */
.btn:disabled:hover::after {
  content: attr(data-tooltip);
}

.btn-primary:disabled:hover {
  background-color: #8bafd3;
}

/* Audio Player Section */
.audio-section {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 20px 0;
  overflow: hidden;
}

/* .multi-mic-audio-player {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
} */

/* Audio Track */
.audio-track {
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  padding: 16px;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
}

.audio-track:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  border-color: #dee2e6;
}

.audio-track-info {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.audio-track-label {
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  margin-right: 16px;
}

.audio-track-status {
  display: flex;
  align-items: center;
  gap: 16px;
}

.audio-track-status span {
  color: #6c757d;
  font-size: 0.9rem;
}

/* Mute Button */
.mute-button {
  background: transparent;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #6c757d;
}

.mute-button:hover {
  background-color: #f0f2f5;
  color: #2c3e50;
}

.mute-button.muted {
  background-color: #fee2e2;
  color: #dc2626;
}

.mute-button.muted:hover {
  background-color: #fecaca;
}

.mute-button svg {
  width: 20px;
  height: 20px;
}

/* Live Indicator */
.live-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #10b981;
  font-weight: 500;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: rgba(16, 185, 129, 0.1);
}

.live-indicator .live-dot {
  width: 8px;
  height: 8px;
  background-color: #10b981;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

/* Paused state styles */
.live-indicator.paused {
  color: #f59e0b;
  background-color: rgba(245, 158, 11, 0.1);
}

.live-indicator.paused .live-dot {
  background-color: #f59e0b;
  animation: none; /* Stop pulsing when paused */
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.live-indicator::before {
  content: '';
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #10b981;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(16, 185, 129, 0);
  }
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0);
  }
}

.audio-track-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.mute-button {
  background: none;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 16px;
}

.mute-button:hover {
  background: #f5f5f5;
}

.mute-button.muted {
  background: #f0f0f0;
  color: #666;
}
/* 
.audio-track-label {
  font-weight: 500;
  color: #333;
} */

.btn-primary {
  background-color: #0D4C9E;
  color: white;
}

.btn-primary:hover {
  background-color: #0A3F83;
}

.btn-light {
  background-color: #EEEEEE;
  color: #444444;
}

.btn-light:hover {
  background-color: #E0E0E0;
}

.btn-success {
  background-color: #4CAF50;
  color: white;
}

.btn-success:hover {
  background-color: #3D9140;
}

.btn-secondary {
  background-color: #44546A;
  color: white;
}

.btn-secondary:hover {
  background-color: #3A4859;
}

/* Icon in buttons */
.btn i {
  margin-right: 6px;
  font-size: 16px;
}

/* Using span for icons since we don't have actual icon fonts */
.icon {
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 6px;
  vertical-align: text-bottom;
  background-position: center;
  background-repeat: no-repeat;
  background-size: contain;
}

.icon-mic {
  background-color: #0D4C9E;
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.91-3c-.49 0-.9.36-.98.85C16.52 14.2 14.47 16 12 16s-4.52-1.8-4.93-4.15c-.08-.49-.49-.85-.98-.85-.61 0-1.09.54-1 1.14.49 3 2.89 5.35 5.91 5.78V20c0 .55.45 1 1 1s1-.45 1-1v-2.08c3.02-.43 5.42-2.78 5.91-5.78.1-.6-.39-1.14-1-1.14z'/%3E%3C/svg%3E");
  -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.91-3c-.49 0-.9.36-.98.85C16.52 14.2 14.47 16 12 16s-4.52-1.8-4.93-4.15c-.08-.49-.49-.85-.98-.85-.61 0-1.09.54-1 1.14.49 3 2.89 5.35 5.91 5.78V20c0 .55.45 1 1 1s1-.45 1-1v-2.08c3.02-.43 5.42-2.78 5.91-5.78.1-.6-.39-1.14-1-1.14z'/%3E%3C/svg%3E");
}

.icon-stop {
  background-color: #444444;
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M6 6h12v12H6z'/%3E%3C/svg%3E");
  -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M6 6h12v12H6z'/%3E%3C/svg%3E");
}

.icon-document {
  background-color: white;
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'/%3E%3C/svg%3E");
  -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'/%3E%3C/svg%3E");
}

.icon-pause {
  background-color: #f59e0b;
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M6 19h4V5H6v14zm8-14v14h4V5h-4z'/%3E%3C/svg%3E");
  -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M6 19h4V5H6v14zm8-14v14h4V5h-4z'/%3E%3C/svg%3E");
}

.icon-play {
  background-color: #10b981;
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M8 5v14l11-7z'/%3E%3C/svg%3E");
  -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M8 5v14l11-7z'/%3E%3C/svg%3E");
}

.conversation-panel,
.ai-feedback-panel {
  box-sizing: border-box;
  margin-bottom: 12px;
  overflow-y: auto;
  max-height: calc(100vh - 200px);
}

/* Responsive content handling */
@media (max-width: 1200px) {
  .conversation-panel,
  .ai-feedback-panel {
    max-height: none;
    height: auto;
    min-height: 300px;
  }
}

@media (max-width: 768px) {
  .conversation-panel,
  .ai-feedback-panel {
    min-height: 250px;
    max-height: 400px;
  }
}

@media (max-width: 480px) {
  .conversation-panel,
  .ai-feedback-panel {
    min-height: 200px;
    max-height: 350px;
  }
}

@keyframes fadeOut {
  0% {
    opacity: 1;
  }

  70% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes blink {
  0% { opacity: 1; }
  50% { opacity: 0.3; }
  100% { opacity: 1; }
}

@keyframes modalFadeIn {
  0% { opacity: 0; transform: scale(0.9); }
  100% { opacity: 1; transform: scale(1); }
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  padding: 2rem;
  border-radius: 8px;
  min-width: 300px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  text-align: center;
}

.close-modal-btn {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.close-modal-btn:hover {
  background-color: #c82333;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  border-bottom: 1px solid #e2e8f0;
  background-color: #f8f9fa;
}

.modal-header h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
}

.modal-header .btn-light {
  font-size: 18px;
  padding: 4px 12px;
  border-radius: 8px;
  border: none;
  background: transparent;
  color: #64748b;
  cursor: pointer;
}

.upload-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4px);
}

.upload-modal-container {
  background: white;
  border-radius: 12px;
  padding: 24px;
  width: 480px;
  max-width: 90vw;
  max-height: 80vh;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.upload-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.upload-modal-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.upload-modal-close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.upload-modal-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
  text-align: left;
}

.upload-modal-file-drop {
  display: inline-block;
  padding: 10px 16px;
  border-radius: 8px;
  border: 2px dashed #d1d5db;
  background: #f9fafb;
  color: #374151;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  width: 100%;
  box-sizing: border-box;
  transition: all 0.2s ease;
  text-align: left;
}

.upload-modal-file-list {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  max-height: 150px;
  overflow-y: auto;
  background: #f9fafb;
}

.upload-modal-file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  font-size: 14px;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
}

.upload-modal-file-item:last-child {
  border-bottom: none;
}

.upload-modal-file-name {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
}

.upload-modal-remove-btn {
  margin-left: 8px;
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  padding: 4px;
  border-radius: 4px;
}

.upload-modal-select {
  width: 100%;
  padding: 10px 12px;
  border-radius: 8px;
  border: 1px solid #d1d5db;
  font-size: 14px;
  background: white;
  color: #374151;
}

.upload-modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
  margin-top: 4px;
}

.upload-modal-footer-info {
  font-size: 14px;
  color: #6b7280;
}

.upload-modal-footer-actions {
  display: flex;
  gap: 12px;
}

.upload-modal-btn {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.upload-modal-cancel-btn {
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
}

.upload-modal-upload-btn {
  border: none;
  background: #3b82f6;
  color: white;
  display: flex;
  align-items: center;
  gap: 8px;
}

.upload-modal-upload-btn.disabled {
  background: #9ca3af;
  cursor: not-allowed;
}

.upload-modal-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: upload-modal-spin 1s linear infinite;
}

@keyframes upload-modal-spin {
  to {
    transform: rotate(360deg);
  }
}
