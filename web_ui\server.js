// / Simple Node.js/Express backend for saving WAV files from React frontend

const express = require('express');
const multer = require('multer');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const { execFile } = require('child_process');
const { getClient, pool } = require('./node_backend/get-client');
const crypto = require('crypto');
// Import medical prompts from node_backend constants
const { MEDICAL_SUMMARY_PROMPT, MEDICAL_TRANSLATION_PROMPT } = require('./node_backend/constants/medicalPrompts');

const PORT = process.env.PORT || 4000;
const USE_SECURE = process.env.USE_SECURE === '1';
// Correct path for recordings on the deployed server, assuming 'web_ui' and 'recordings_web' are siblings.
const RECORDINGS_DIR = path.resolve(__dirname, '../recordings_web');
console.log(`[SERVER STARTUP] Recordings directory resolved to: ${RECORDINGS_DIR}`);
require('dotenv').config();
// Ensure recordings directory exists
if (!fs.existsSync(RECORDINGS_DIR)) {
  fs.mkdirSync(RECORDINGS_DIR, { recursive: true });
}

let app;
if (USE_SECURE) {
  // HTTPS server
  const https = require('https');
  const sslKey = process.env.SSL_CERT_KEY || '/certs/localhost.key';
  const sslCert = process.env.SSL_CERT || '/certs/localhost.cert';
  const credentials = {
    key: fs.readFileSync(sslKey),
    cert: fs.readFileSync(sslCert)
  };
  app = express();
  app._createServer = () => https.createServer(credentials, app);
  // await setupTable(client);
} else {
  // HTTP server
  app = express();
  app._createServer = () => https.createServer(credentials, app);
  // await setupTable(client);
}

// async function setupTable(client) {
//   let createTableQuery = `
//     CREATE TABLE IF NOT EXISTS recording_summaries(
//       id BIGSERIAL PRIMARY KEY NOT NULL,
//       filepath varchar NOT NULL,
//       session_id varchar NOT NULL,
//       patient_id varchar NOT NULL,
//       event_id varchar NOT NULL,
//       created_at TIMESTAMP NOT NULL DEFAULT current_timestamp,
//       generated_summary text,
//       UNIQUE(filepath, session_id, patient_id, event_id)
//     );
//   `;
//   return await client.query(createTableQuery);
// }

// Allow CORS from React dev server (default Vite port 5173)
// Allow CORS from frontend, configurable via env (default: localhost:5173)
// Enable CORS for all origins
app.use(cors({
  origin: true,
  credentials: true,
}));

// Add this before any routes that use req.body (e.g., before /api/save_recording_summary)
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// --- Request Logger Middleware ---
// This will log every incoming request to the console.
app.use((req, res, next) => {
  console.log(`[Request Logger] Method: ${req.method}, URL: ${req.originalUrl}`);
  next();
});

// --- Static File Serving --- Correct Order is Critical

// 1. Serve audio recordings from the corrected path with the correct Content-Type
app.use('/recordings_web', express.static(RECORDINGS_DIR, {
  setHeaders: (res, filePath) => {
    if (path.extname(filePath) === '.wav') {
      res.setHeader('Content-Type', 'audio/wav');
    }
  }
}));

// Also serve recordings under /api/recordings_web for consistency
app.use('/api/recordings_web', express.static(RECORDINGS_DIR, {
  setHeaders: (res, filePath) => {
    if (path.extname(filePath) === '.wav') {
      res.setHeader('Content-Type', 'audio/wav');
    }
  }
}));

// 2. Serve the main React app static files from the 'dist' directory
app.use(express.static(path.join(__dirname, 'dist')));

// Multer setup for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // Always save in the main recordings directory
    cb(null, RECORDINGS_DIR);
  },
  filename: (req, file, cb) => {
    // Use original filename or fallback, and add mic index if provided
    const safeName = file.originalname.replace(/[^a-zA-Z0-9_.-]/g, '_');
    let mic = null;
    if (req.body && req.body.mic) mic = req.body.mic;
    if (req.query && req.query.mic) mic = req.query.mic;
    let prefix = '';
    if (mic !== null && mic !== undefined && mic !== '') prefix = `mic${mic}_`;
    cb(null, prefix + (safeName || `audio_${Date.now()}.wav`));
  },
});
const upload = multer({ storage });



// --- In-memory job store for async batch jobs ---
const jobStore = {};
const { spawn } = require('child_process');

// Helper: get audio duration using ffprobe
async function getAudioDuration(filePath) {
  return new Promise((resolve, reject) => {
    const ffprobe = spawn('ffprobe', [
      '-v', 'error',
      '-show_entries', 'format=duration',
      '-of', 'default=noprint_wrappers=1:nokey=1',
      filePath
    ]);
    let output = '';
    ffprobe.stdout.on('data', data => { output += data; });
    ffprobe.stderr.on('data', data => { });
    ffprobe.on('close', code => {
      if (code === 0) {
        const duration = parseFloat(output.trim());
        resolve(duration);
      } else {
        reject(new Error('ffprobe failed'));
      }
    });
  });
}

// --- Upload Audio for Full Transcription and Medical Summary ---
app.post('/api/upload_audio', upload.array('audio', 10), async (req, res) => {
  console.log('[UPLOAD] /api/upload_audio called');
  if (!req.files || req.files.length === 0) {
    console.error('[UPLOAD] No files uploaded');
    return res.status(400).json({ success: false, message: 'No files uploaded' });
  }
  const language = req.body.language || 'en-US';
  console.log('[UPLOAD] Language:', language);
  let summaryFormat = req.body.summaryFormat;
  if (summaryFormat && typeof summaryFormat === 'string') {
    try {
      summaryFormat = JSON.parse(summaryFormat);
      console.log('[UPLOAD] Parsed summaryFormat:', summaryFormat);
    } catch (e) {
      console.warn('[UPLOAD] Could not parse summaryFormat:', summaryFormat);
    }
  }
  if (!summaryFormat || typeof summaryFormat !== 'object') {
    summaryFormat = null;
    console.log('[UPLOAD] No valid summaryFormat provided.');
  }
  const detailLevel = 'very detailed';
  console.log('[UPLOAD] detailLevel:', detailLevel);
  console.log('[UPLOAD] Received files:', req.files.map(f => f.originalname));

  // Check duration of all files
  let durations = [];
  try {
    console.log('[UPLOAD] Checking audio durations...');
    durations = await Promise.all(req.files.map(f => getAudioDuration(f.path)));
    console.log('[UPLOAD] Durations:', durations);
  } catch (e) {
    console.error('[UPLOAD] Failed to get audio duration:', e);
    return res.status(500).json({ success: false, message: 'Failed to get audio duration', error: e.message });
  }
  const maxDuration = Math.max(...durations);
  console.log('[UPLOAD] Max duration:', maxDuration);

  if (maxDuration < 300) {
    try {
      console.log('[UPLOAD] Using get-summary logic for short audio');
      const axios = require('axios');
      const fs = require('fs');
      const results = [];
      let allTranscripts = '';
      for (const file of req.files) {
        console.log(`[UPLOAD] Transcribing file: ${file.filename}`);
        const AZURE_SPEECH_KEY = process.env.AZURE_SPEECH_KEY;
        const AZURE_SPEECH_REGION = process.env.AZURE_SPEECH_REGION;
        if (!AZURE_SPEECH_KEY || !AZURE_SPEECH_REGION) {
          console.error('[UPLOAD] Azure Speech credentials not set');
          throw new Error('Azure Speech credentials not set');
        }
        const audioData = fs.readFileSync(file.path);
        const url = `https://${AZURE_SPEECH_REGION}.stt.speech.microsoft.com/speech/recognition/conversation/cognitiveservices/v1?language=${language}`;
        const resp = await axios.post(url, audioData, {
          headers: {
            'Ocp-Apim-Subscription-Key': AZURE_SPEECH_KEY,
            'Content-Type': 'audio/wav',
          },
          timeout: 120000
        });
        console.log(`[UPLOAD] Azure STT response for ${file.filename}:`, resp.status, resp.data);
        const transcript = resp.data.DisplayText || '';
        results.push({ filename: file.filename, rawTranscript: transcript, error: null });
        allTranscripts += transcript + '\n';
        try { fs.unlinkSync(file.path); } catch (e) { }
      }
      let combinedSummary = '';
      if (allTranscripts.trim().length > 0) {
        try {
          const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.AZURE_OPENAI_API_KEY;
          const OPENAI_API_BASE = process.env.OPENAI_API_BASE || process.env.AZURE_OPENAI_ENDPOINT_URL || 'https://api.openai.com/v1';
          const OPENAI_DEPLOYMENT = process.env.AZURE_OPENAI_DEPLOYMENT_NAME;
          if (!OPENAI_API_KEY) throw new Error('OpenAI API key not set');
          let translatedTranscript = allTranscripts;
          if (language && !language.toLowerCase().startsWith('en')) {
            const translationPrompt = MEDICAL_TRANSLATION_PROMPT(allTranscripts);
            let translationUrl, translationHeaders, translationBody;
            if (OPENAI_API_BASE.includes('azure.com')) {
              translationUrl = `${OPENAI_API_BASE}/openai/deployments/${OPENAI_DEPLOYMENT}/chat/completions?api-version=2024-02-15-preview`;
              translationHeaders = {
                'api-key': OPENAI_API_KEY,
                'Content-Type': 'application/json',
              };
              translationBody = {
                messages: [
                  { role: 'system', content: 'You are a helpful medical translator.' },
                  { role: 'user', content: translationPrompt }
                ],
                max_tokens: 4096,
                temperature: 0.0
              };
            } else {
              translationUrl = `${OPENAI_API_BASE}/chat/completions`;
              translationHeaders = {
                'Authorization': `Bearer ${OPENAI_API_KEY}`,
                'Content-Type': 'application/json',
              };
              translationBody = {
                model: 'gpt-4',
                messages: [
                  { role: 'system', content: 'You are a helpful medical translator.' },
                  { role: 'user', content: translationPrompt }
                ],
                max_tokens: 4096,
                temperature: 0.0
              };
            }
            console.log('[UPLOAD] Sending transcript for translation...');
            const translationResp = await axios.post(translationUrl, translationBody, { headers: translationHeaders, timeout: 120000 });
            if (translationResp.data.choices && translationResp.data.choices.length > 0) {
              translatedTranscript = translationResp.data.choices[0].message?.content || translationResp.data.choices[0].text || allTranscripts;
            }
            console.log('[UPLOAD] Translation complete.');
          }
          const medicalPrompt = MEDICAL_SUMMARY_PROMPT;
          let openaiUrl, headers, body;
          if (OPENAI_API_BASE.includes('azure.com')) {
            openaiUrl = `${OPENAI_API_BASE}/openai/deployments/${OPENAI_DEPLOYMENT}/chat/completions?api-version=2024-02-15-preview`;
            headers = {
              'api-key': OPENAI_API_KEY,
              'Content-Type': 'application/json',
            };
            body = {
              messages: [
                { role: 'system', content: medicalPrompt },
                { role: 'user', content: translatedTranscript }
              ],
              max_tokens: 1024,
              temperature: 0.2
            };
          } else {
            openaiUrl = `${OPENAI_API_BASE}/chat/completions`;
            headers = {
              'Authorization': `Bearer ${OPENAI_API_KEY}`,
              'Content-Type': 'application/json',
            };
            body = {
              model: 'gpt-4',
              messages: [
                { role: 'system', content: medicalPrompt },
                { role: 'user', content: translatedTranscript }
              ],
              max_tokens: 1024,
              temperature: 0.2
            };
          }
          console.log('[UPLOAD] Sending transcript for summary...');
          const openaiResp = await axios.post(openaiUrl, body, { headers, timeout: 120000 });
          if (openaiResp.data.choices && openaiResp.data.choices.length > 0) {
            combinedSummary = openaiResp.data.choices[0].message?.content || openaiResp.data.choices[0].text || '';
          }
          console.log('[UPLOAD] Summary complete.');
        } catch (err) {
          combinedSummary = `Error generating combined summary: ${err.message}`;
          console.error('[UPLOAD] Error during summary:', err);
        }
      }
      console.log('[UPLOAD] Returning summary response.');
      return res.status(200).json({ success: true, results, combinedSummary });
    } catch (err) {
      console.error('[UPLOAD] Error in short audio summary:', err);
      return res.status(500).json({ success: false, message: err.message });
    }
  }

  // Otherwise, use async batch job logic
  console.log('[UPLOAD] Using async batch job logic for long audio.');
  const jobId = crypto.randomUUID();
  jobStore[jobId] = { status: 'processing', results: null, combinedSummary: null, error: null };
  res.json({ success: true, jobId, results: null, combinedSummary: null });

  (async () => {
    try {
      console.log('[UPLOAD] Starting batch transcription job...');
      // ...existing batch transcription logic (from previous code, per-file)...
      const axios = require('axios');
      const path = require('path');
      const fs = require('fs');
      const { BlobServiceClient, StorageSharedKeyCredential, generateBlobSASQueryParameters, BlobSASPermissions } = require('@azure/storage-blob');
      const AZURE_STORAGE_ACCOUNT = process.env.AZURE_STORAGE_ACCOUNT;
      const AZURE_STORAGE_CONTAINER = process.env.AZURE_STORAGE_CONTAINER;
      const AZURE_STORAGE_KEY = process.env.AZURE_STORAGE_KEY;
      const AZURE_SPEECH_KEY = process.env.AZURE_SPEECH_KEY;
      const AZURE_SPEECH_REGION = process.env.AZURE_SPEECH_REGION;
      if (!AZURE_STORAGE_ACCOUNT || !AZURE_STORAGE_CONTAINER || !AZURE_STORAGE_KEY) throw new Error('Azure Blob Storage credentials not set');
      if (!AZURE_SPEECH_KEY || !AZURE_SPEECH_REGION) throw new Error('Azure Speech credentials not set');
      const sharedKeyCredential = new StorageSharedKeyCredential(AZURE_STORAGE_ACCOUNT, AZURE_STORAGE_KEY);
      const blobServiceClient = new BlobServiceClient(
        `https://${AZURE_STORAGE_ACCOUNT}.blob.core.windows.net`,
        sharedKeyCredential
      );
      const containerClient = blobServiceClient.getContainerClient(AZURE_STORAGE_CONTAINER);
      await containerClient.createIfNotExists();
      // Upload all files to blob
      const blobInfos = [];
      for (const file of req.files) {
        const blobName = `${crypto.randomUUID()}_${path.basename(file.path)}`;
        const blobClient = containerClient.getBlockBlobClient(blobName);
        await blobClient.uploadFile(file.path);
        blobInfos.push({ blobName, blobClient, file });
        console.log(`[UPLOAD] Uploaded file to blob: ${blobName}`);
      }
      // Generate SAS URLs
      const expiresOn = new Date(Date.now() + 2 * 60 * 60 * 1000);
      const sas = generateBlobSASQueryParameters({
        containerName: AZURE_STORAGE_CONTAINER,
        permissions: BlobSASPermissions.parse('r'),
        startsOn: new Date(Date.now() - 5 * 60 * 1000),
        expiresOn,
      }, sharedKeyCredential).toString();
      const blobUrls = blobInfos.map(b => `${b.blobClient.url}?${sas}`);
      console.log('[UPLOAD] Blob URLs for batch job:', blobUrls);
      // Submit batch transcription job
      const batchEndpoint = `https://${AZURE_SPEECH_REGION}.api.cognitive.microsoft.com/speechtotext/v3.1/transcriptions`;
      const transcriptionName = `Transcription_${Date.now()}`;
      const transcriptionPayload = {
        displayName: transcriptionName,
        description: 'Batch transcription from web UI',
        locale: language,
        contentUrls: blobUrls,
        properties: {
          diarizationEnabled: false,
          wordLevelTimestampsEnabled: false
        }
      };
      const batchResp = await axios.post(batchEndpoint, transcriptionPayload, {
        headers: {
          'Ocp-Apim-Subscription-Key': AZURE_SPEECH_KEY,
          'Content-Type': 'application/json',
        },
        timeout: 60000
      });
      const transcriptionUrl = batchResp.headers['location'];
      if (!transcriptionUrl) throw new Error('No transcription job location returned from Azure');
      console.log('[UPLOAD] Batch job submitted. Polling for completion...');
      // Poll for job completion
      let status = '', rawTranscripts = [];
      let pollCount = 0, maxPolls = 60;
      while (pollCount < maxPolls) {
        await new Promise(r => setTimeout(r, 10000));
        const statusResp = await axios.get(transcriptionUrl, {
          headers: { 'Ocp-Apim-Subscription-Key': AZURE_SPEECH_KEY },
          timeout: 30000
        });
        status = statusResp.data.status;
        console.log(`[UPLOAD] Batch job status: ${status}`);
        if (status === 'Succeeded') {
          const filesUrl = statusResp.data.links && statusResp.data.links.files;
          if (!filesUrl) throw new Error('No files link in Azure response');
          const filesListResp = await axios.get(filesUrl, {
            headers: { 'Ocp-Apim-Subscription-Key': AZURE_SPEECH_KEY },
            timeout: 30000
          });
          const filesArr = filesListResp.data.values || [];
          // For each file, get transcript
          for (const file of filesArr) {
            if (file.kind === 'Transcription' && file.links && file.links.contentUrl) {
              const transcriptResp = await axios.get(file.links.contentUrl, { timeout: 60000 });
              const phrases = transcriptResp.data.combinedRecognizedPhrases || transcriptResp.data.recognizedPhrases || [];
              rawTranscripts.push(phrases.map(p => p.display || p.lexical || p.text || '').join(' '));
            }
          }
          break;
        } else if (status === 'Failed') {
          throw new Error('Azure batch transcription failed');
        }
        pollCount++;
      }
      if (!rawTranscripts.length) throw new Error('No transcript returned from Azure Batch Speech');
      // Clean up blobs and files
      for (const b of blobInfos) {
        try { fs.unlinkSync(b.file.path); } catch (e) { }
        try { await b.blobClient.delete(); } catch (e) { }
      }
      // Summarize as before
      const allTranscripts = rawTranscripts.join('\n');
      let combinedSummary = '';
      if (allTranscripts.trim().length > 0) {
        try {
          console.log('[UPLOAD] Summarizing batch transcripts...');
          const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.AZURE_OPENAI_API_KEY;
          const OPENAI_API_BASE = process.env.OPENAI_API_BASE || process.env.AZURE_OPENAI_ENDPOINT_URL || 'https://api.openai.com/v1';
          const OPENAI_DEPLOYMENT = process.env.AZURE_OPENAI_DEPLOYMENT_NAME;
          if (!OPENAI_API_KEY) throw new Error('OpenAI API key not set');
          let translatedTranscript = allTranscripts;
          if (language && !language.toLowerCase().startsWith('en')) {
            console.log('[UPLOAD] Translating batch transcript to English...');
            const translationPrompt = MEDICAL_TRANSLATION_PROMPT(allTranscripts);
            let translationUrl, translationHeaders, translationBody;
            if (OPENAI_API_BASE.includes('azure.com')) {
              translationUrl = `${OPENAI_API_BASE}/openai/deployments/${OPENAI_DEPLOYMENT}/chat/completions?api-version=2024-02-15-preview`;
              translationHeaders = {
                'api-key': OPENAI_API_KEY,
                'Content-Type': 'application/json',
              };
              translationBody = {
                messages: [
                  { role: 'system', content: 'You are a helpful medical translator.' },
                  { role: 'user', content: translationPrompt }
                ],
                max_tokens: 4096,
                temperature: 0.0
              };
            } else {
              translationUrl = `${OPENAI_API_BASE}/chat/completions`;
              translationHeaders = {
                'Authorization': `Bearer ${OPENAI_API_KEY}`,
                'Content-Type': 'application/json',
              };
              translationBody = {
                model: 'gpt-4',
                messages: [
                  { role: 'system', content: 'You are a helpful medical translator.' },
                  { role: 'user', content: translationPrompt }
                ],
                max_tokens: 4096,
                temperature: 0.0
              };
            }
            const translationResp = await axios.post(translationUrl, translationBody, { headers: translationHeaders, timeout: 120000 });
            if (translationResp.data.choices && translationResp.data.choices.length > 0) {
              translatedTranscript = translationResp.data.choices[0].message?.content || translationResp.data.choices[0].text || allTranscripts;
            }
            console.log('[UPLOAD] Batch translation complete.');
          }
          medicalPrompt = MEDICAL_SUMMARY_PROMPT;
          let openaiUrl, headers, body;
          if (OPENAI_API_BASE.includes('azure.com')) {
            openaiUrl = `${OPENAI_API_BASE}/openai/deployments/${OPENAI_DEPLOYMENT}/chat/completions?api-version=2024-02-15-preview`;
            headers = {
              'api-key': OPENAI_API_KEY,
              'Content-Type': 'application/json',
            };
            body = {
              messages: [
                { role: 'system', content: medicalPrompt },
                { role: 'user', content: translatedTranscript }
              ],
              max_tokens: 1024,
              temperature: 0.2
            };
          } else {
            openaiUrl = `${OPENAI_API_BASE}/chat/completions`;
            headers = {
              'Authorization': `Bearer ${OPENAI_API_KEY}`,
              'Content-Type': 'application/json',
            };
            body = {
              model: 'gpt-4',
              messages: [
                { role: 'system', content: medicalPrompt },
                { role: 'user', content: translatedTranscript }
              ],
              max_tokens: 1024,
              temperature: 0.2
            };
          }
          const openaiResp = await axios.post(openaiUrl, body, { headers, timeout: 120000 });
          if (openaiResp.data.choices && openaiResp.data.choices.length > 0) {
            combinedSummary = openaiResp.data.choices[0].message?.content || openaiResp.data.choices[0].text || '';
          }
          console.log('[UPLOAD] Batch summary complete.', combinedSummary);
        } catch (err) {
          combinedSummary = `Error generating combined summary: ${err.message}`;
          console.error('[UPLOAD] Error during batch summary:', err);
        }
      }
      console.log(combinedSummary, 'combinedSummary');

      // Save results in jobStore
      jobStore[jobId] = {
        status: 'done',
        results: rawTranscripts.map((t, i) => ({ filename: req.files[i]?.filename, rawTranscript: t, error: null })),
        combinedSummary,
        error: null
      };
      console.log('[UPLOAD] Batch job completed.');
    } catch (err) {
      jobStore[jobId] = { status: 'error', results: null, combinedSummary: null, error: err.message };
      console.error('[UPLOAD] Error in batch job:', err);
    }
  })();
});

// Helper: Generate summary for a session (real logic)
async function generateSummaryForSession(sessionId, eventId, language, patientId = null) {
  let client;
  try {
    console.log(`[SUMMARY] Starting summary generation for session: ${sessionId}, language: ${language}`);
    client = await getClient();

    // Get previous case notes for the patient if patientId is provided
    let previousCaseNotes = null;

    // --- Collect extra context (recent vitals, etc.) ---
    let extraContext = "";
    let patientAge = null;
    let patientGender = null;
    if (patientId) {
      try {
        console.log(`[SUMMARY] Fetching previous case notes for patient: ${patientId}`);

        // Fetch previous case notes summary for this patient
        const caseNotesResult = await client.query(
          `SELECT previous_case_notes_summary, dob, gender FROM patients WHERE reg_no = $1`,
          [patientId]
        );

        if (caseNotesResult.rows.length > 0) {
          if (caseNotesResult.rows[0].previous_case_notes_summary) {
            previousCaseNotes = caseNotesResult.rows[0].previous_case_notes_summary;
            console.log(`[SUMMARY] Found previous case notes for patient: ${patientId}`);
          } else {
            console.log(`[SUMMARY] No previous case notes found for patient: ${patientId}`);
          }
          // Patient age/gender
          const { dob, gender } = caseNotesResult.rows[0];
          patientGender = gender || null;
          if (dob) {
            const birthDate = new Date(dob);
            const today = new Date();
            let age = today.getFullYear() - birthDate.getFullYear();
            const m = today.getMonth() - birthDate.getMonth();
            if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
              age--;
            }
            patientAge = age;
          }
        }
        let recentVitals = null;
        console.log(`[SUMMARY] Querying vitals for patient: ${patientId}`);
        const vitalsResult = await client.query(
          `SELECT vitals FROM patients WHERE reg_no = $1`,
          [patientId]
        );
        console.log(`[SUMMARY] Vitals query result:`, vitalsResult.rows);
        if (vitalsResult.rows.length > 0 && vitalsResult.rows[0].vitals) {
          console.log(`[SUMMARY] Found vitals for patient: ${patientId}`);
          recentVitals = extractAndFormatRecentVitals(vitalsResult.rows[0].vitals, new Date());
          console.log(`[SUMMARY] Formatted recent vitals:\n${recentVitals}`);
        } else {
          console.log(`[SUMMARY] No vitals found for patient: ${patientId}`);
        }

        if (recentVitals) {
          extraContext += `Recent Vitals: ${recentVitals}\n`;
          console.log(`[SUMMARY] Added recent vitals to extraContext.`);
        }
        // Add age/gender to extraContext
        if (patientAge !== null) extraContext = `Patient Age: ${patientAge}\n` + extraContext;
        if (patientGender) extraContext = `Patient Gender: ${patientGender}\n` + extraContext;
      } catch (err) {
        console.error(`[SUMMARY] Error fetching patient data: ${err.message}`);
        // Continue with summary generation even if patient data fetch fails
      }
    } else {
      console.log(`[SUMMARY] No patientId provided, skipping previous case notes lookup`);
    }

    // Get all filepaths for this session/event
    const result = await client.query(
      `SELECT filepath FROM recording_files WHERE session_id = $1 AND event_id = $2`,
      [sessionId, eventId]
    );
    console.log(`[SUMMARY] Found ${result.rows.length} files for session.`);
    if (result.rows.length === 0) return;
    const filepaths = result.rows.map(r => r.filepath);
    const fs = require('fs');
    const path = require('path');
    const axios = require('axios');
    const { BlobServiceClient, StorageSharedKeyCredential, generateBlobSASQueryParameters, BlobSASPermissions } = require('@azure/storage-blob');
    let allTranscripts = '';
    language = language || 'ml-IN';
    const AZURE_SPEECH_KEY = process.env.AZURE_SPEECH_KEY;
    const AZURE_SPEECH_REGION = process.env.AZURE_SPEECH_REGION;
    const AZURE_STORAGE_ACCOUNT = process.env.AZURE_STORAGE_ACCOUNT;
    const AZURE_STORAGE_CONTAINER = process.env.AZURE_STORAGE_CONTAINER;
    const AZURE_STORAGE_KEY = process.env.AZURE_STORAGE_KEY;
    if (!AZURE_SPEECH_KEY || !AZURE_SPEECH_REGION) throw new Error('Azure Speech credentials not set');
    // Collect short and long files
    let shortFiles = [], longFiles = [];
    for (const filepath of filepaths) {
      const fullPath = path.join(RECORDINGS_DIR, filepath);
      if (!fs.existsSync(fullPath)) {
        console.error(`[SUMMARY] File does not exist: ${fullPath}`);
        continue;
      }
      // Use ffprobe to get duration
      let duration = 0;
      try {
        duration = await getAudioDuration(fullPath);
        console.log(`[SUMMARY] File: ${fullPath}, duration: ${duration}s`);
      } catch (e) {
        console.error(`[SUMMARY] Could not get duration for ${fullPath}:`, e.message);
      }
      if (duration < 300) {
        shortFiles.push(fullPath);
      } else {
        longFiles.push(fullPath);
      }
    }
    console.log(`[SUMMARY] Short files: ${shortFiles.length}, Long files: ${longFiles.length}`);
    // Process short files with real-time STT
    for (const fullPath of shortFiles) {
      const stats = fs.statSync(fullPath);
      console.log(`[SUMMARY] Processing short file: ${fullPath}, size: ${stats.size}`);
      const audioData = fs.readFileSync(fullPath);
      const url = `https://${AZURE_SPEECH_REGION}.stt.speech.microsoft.com/speech/recognition/conversation/cognitiveservices/v1?language=${language}`;
      try {
        const resp = await axios.post(url, audioData, {
          headers: {
            'Ocp-Apim-Subscription-Key': AZURE_SPEECH_KEY,
            'Content-Type': 'audio/wav',
          },
          timeout: 120000
        });
        console.log(`[SUMMARY] Azure STT response for ${fullPath}:`, resp.status, resp.data);
        const transcript = resp.data.DisplayText || '';
        allTranscripts += transcript + '\n';
      } catch (err) {
        console.error(`[SUMMARY] Azure STT error for ${fullPath}:`, err.response ? err.response.status : err.message, err.response ? err.response.data : '');
        allTranscripts += `ERROR: Azure STT failed for file ${fullPath}\n`;
      }
    }
    // Process long files with batch STT
    if (longFiles.length > 0) {
      if (!AZURE_STORAGE_ACCOUNT || !AZURE_STORAGE_CONTAINER || !AZURE_STORAGE_KEY) throw new Error('Azure Blob Storage credentials not set');
      const sharedKeyCredential = new StorageSharedKeyCredential(AZURE_STORAGE_ACCOUNT, AZURE_STORAGE_KEY);
      const blobServiceClient = new BlobServiceClient(
        `https://${AZURE_STORAGE_ACCOUNT}.blob.core.windows.net`,
        sharedKeyCredential
      );
      const containerClient = blobServiceClient.getContainerClient(AZURE_STORAGE_CONTAINER);
      await containerClient.createIfNotExists();
      // Upload all long files to blob
      const blobInfos = [];
      for (const filePath of longFiles) {
        const blobName = `${require('crypto').randomUUID()}_${path.basename(filePath)}`;
        const blobClient = containerClient.getBlockBlobClient(blobName);
        await blobClient.uploadFile(filePath);
        blobInfos.push({ blobName, blobClient, filePath });
        console.log(`[SUMMARY] Uploaded long file to blob: ${blobName}`);
      }
      // Generate SAS URLs
      const expiresOn = new Date(Date.now() + 2 * 60 * 60 * 1000);
      const sas = generateBlobSASQueryParameters({
        containerName: AZURE_STORAGE_CONTAINER,
        permissions: BlobSASPermissions.parse('r'),
        startsOn: new Date(Date.now() - 5 * 60 * 1000),
        expiresOn,
      }, sharedKeyCredential).toString();
      const blobUrls = blobInfos.map(b => `${b.blobClient.url}?${sas}`);
      console.log(`[SUMMARY] Blob URLs for batch job:`, blobUrls);
      // Submit batch transcription job
      const batchEndpoint = `https://${AZURE_SPEECH_REGION}.api.cognitive.microsoft.com/speechtotext/v3.1/transcriptions`;
      const transcriptionName = `Transcription_${Date.now()}`;
      const transcriptionPayload = {
        displayName: transcriptionName,
        description: 'Batch transcription from generateSummaryForSession',
        locale: language,
        contentUrls: blobUrls,
        properties: {
          diarizationEnabled: false,
          wordLevelTimestampsEnabled: false
        }
      };
      const batchResp = await axios.post(batchEndpoint, transcriptionPayload, {
        headers: {
          'Ocp-Apim-Subscription-Key': AZURE_SPEECH_KEY,
          'Content-Type': 'application/json',
        },
        timeout: 60000
      });
      const transcriptionUrl = batchResp.headers['location'];
      if (!transcriptionUrl) throw new Error('No transcription job location returned from Azure');
      console.log(`[SUMMARY] Batch job submitted. Polling for completion...`);
      // Poll for job completion
      let status = '', rawTranscripts = [];
      let pollCount = 0, maxPolls = 60;
      while (pollCount < maxPolls) {
        await new Promise(r => setTimeout(r, 10000));
        const statusResp = await axios.get(transcriptionUrl, {
          headers: { 'Ocp-Apim-Subscription-Key': AZURE_SPEECH_KEY },
          timeout: 30000
        });
        status = statusResp.data.status;
        console.log(`[SUMMARY] Batch job status: ${status}`);
        if (status === 'Succeeded') {
          const filesUrl = statusResp.data.links && statusResp.data.links.files;
          if (!filesUrl) throw new Error('No files link in Azure response');
          const filesListResp = await axios.get(filesUrl, {
            headers: { 'Ocp-Apim-Subscription-Key': AZURE_SPEECH_KEY },
            timeout: 30000
          });
          const filesArr = filesListResp.data.values || [];
          for (const file of filesArr) {
            if (file.kind === 'Transcription' && file.links && file.links.contentUrl) {
              const transcriptResp = await axios.get(file.links.contentUrl, { timeout: 60000 });
              const phrases = transcriptResp.data.combinedRecognizedPhrases || transcriptResp.data.recognizedPhrases || [];
              rawTranscripts.push(phrases.map(p => p.display || p.lexical || p.text || '').join(' '));
            }
          }
          break;
        } else if (status === 'Failed') {
          console.error('[SUMMARY] Azure batch transcription failed');
          throw new Error('Azure batch transcription failed');
        }
        pollCount++;
      }
      if (!rawTranscripts.length) throw new Error('No transcript returned from Azure Batch Speech');
      // Clean up blobs
      for (const b of blobInfos) {
        try { await b.blobClient.delete(); console.log(`[SUMMARY] Deleted blob: ${b.blobName}`); } catch (e) { }
      }
      allTranscripts += rawTranscripts.join('\n');
    }
    // --- OpenAI summary logic (same as /api/upload_audio) ---
    let generatedSummary = '';
    if (allTranscripts.trim().length > 0) {
      try {
        console.log('[SUMMARY] Sending transcript to OpenAI for summary...');
        const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.AZURE_OPENAI_API_KEY;
        const OPENAI_API_BASE = process.env.OPENAI_API_BASE || process.env.AZURE_OPENAI_ENDPOINT_URL || 'https://api.openai.com/v1';
        const OPENAI_DEPLOYMENT = process.env.AZURE_OPENAI_DEPLOYMENT_NAME;
        if (!OPENAI_API_KEY) throw new Error('OpenAI API key not set');
        let translatedTranscript = allTranscripts;
        if (language && !language.toLowerCase().startsWith('en')) {
          console.log('[SUMMARY] Translating transcript to English...');
          const translationPrompt = MEDICAL_TRANSLATION_PROMPT(allTranscripts);
          let translationUrl, translationHeaders, translationBody;
          if (OPENAI_API_BASE.includes('azure.com')) {
            translationUrl = `${OPENAI_API_BASE}/openai/deployments/${OPENAI_DEPLOYMENT}/chat/completions?api-version=2024-02-15-preview`;
            translationHeaders = {
              'api-key': OPENAI_API_KEY,
              'Content-Type': 'application/json',
            };
            translationBody = {
              messages: [
                { role: 'system', content: 'You are a helpful medical translator.' },
                { role: 'user', content: translationPrompt }
              ],
              max_tokens: 4096,
              temperature: 0.0
            };
          } else {
            translationUrl = `${OPENAI_API_BASE}/chat/completions`;
            translationHeaders = {
              'Authorization': `Bearer ${OPENAI_API_KEY}`,
              'Content-Type': 'application/json',
            };
            translationBody = {
              model: 'gpt-4',
              messages: [
                { role: 'system', content: 'You are a helpful medical translator.' },
                { role: 'user', content: translationPrompt }
              ],
              max_tokens: 4096,
              temperature: 0.0
            };
          }
          const translationResp = await axios.post(translationUrl, translationBody, { headers: translationHeaders, timeout: 120000 });
          if (translationResp.data.choices && translationResp.data.choices.length > 0) {
            translatedTranscript = translationResp.data.choices[0].message?.content || translationResp.data.choices[0].text || allTranscripts;
          }
          console.log('[SUMMARY] Translation complete.');
        }
        // Create enhanced medical prompt that includes previous case notes if available
        let medicalPrompt = MEDICAL_SUMMARY_PROMPT;

        // Prepare user content - include both previous case notes and current transcript, and extra context (vitals)
        let userContent = extraContext + translatedTranscript;
        console.log(previousCaseNotes, 'previousCaseNotes');

        if (previousCaseNotes && previousCaseNotes.trim().length > 0) {
          userContent = `${extraContext}PREVIOUS CASE NOTES SUMMARY:\n${previousCaseNotes}\n\n---\n\nCURRENT CONSULTATION TRANSCRIPT:\n${translatedTranscript}`;
        }
        // Log the full content being sent to the AI for summary generation
        console.log('[SUMMARY] Content sent to AI (OpenAI) for summary generation:', {
          systemPrompt: medicalPrompt,
          userContent
        });

        let openaiUrl, headers, body;
        if (OPENAI_API_BASE.includes('azure.com')) {
          openaiUrl = `${OPENAI_API_BASE}/openai/deployments/${OPENAI_DEPLOYMENT}/chat/completions?api-version=2024-02-15-preview`;
          headers = {
            'api-key': OPENAI_API_KEY,
            'Content-Type': 'application/json',
          };
          body = {
            messages: [
              { role: 'system', content: medicalPrompt },
              { role: 'user', content: userContent }
            ],
            max_tokens: 1024,
            temperature: 0.2
          };
        } else {
          openaiUrl = `${OPENAI_API_BASE}/chat/completions`;
          headers = {
            'Authorization': `Bearer ${OPENAI_API_KEY}`,
            'Content-Type': 'application/json',
          };
          body = {
            model: 'gpt-4',
            messages: [
              { role: 'system', content: medicalPrompt },
              { role: 'user', content: userContent }
            ],
            max_tokens: 1024,
            temperature: 0.2
          };
        }
        console.log('[UPLOAD] Sending transcript for summary...');
        const openaiResp = await axios.post(openaiUrl, body, { headers, timeout: 120000 });
        if (openaiResp.data.choices && openaiResp.data.choices.length > 0) {
          generatedSummary = openaiResp.data.choices[0].message?.content || openaiResp.data.choices[0].text || '';
        }
        console.log('[UPLOAD] OpenAI summary complete.');
      } catch (err) {
        generatedSummary = `Error generating combined summary: ${err.message}`;
        console.error('[SUMMARY] Error during OpenAI summary:', err);
      }
    } else {
      console.warn('[SUMMARY] No transcripts found to summarize.');
      generatedSummary = ''; // Set empty string instead of leaving as null
    }
    // Save summary to all rows for this session
    await client.query(
      `UPDATE recording_summaries SET generated_summary = $1 WHERE session_id = $2 AND event_id = $3`,
      [generatedSummary, sessionId, eventId]
    );
    console.log('[SUMMARY] Summary saved to DB.');
  } catch (err) {
    console.error('[API] Error in generateSummaryForSession:', err);
  } finally {
    if (client) await client.release();
  }
}

// Get or generate summary by sessionId (sync for demo, async for real use)
app.post('/api/get_summary_by_session', async (req, res) => {
  console.log(req.body, 'req.body');

  const { sessionId, eventId, language, isStopped, patientId } = req.body;
  if (!sessionId && !eventId) {
    return res.status(400).json({ success: false, message: 'Missing sessionId' });
  }
  let client;
  try {
    client = await getClient();
    const result = await client.query(
      `SELECT * FROM recording_summaries WHERE session_id = $1 AND event_id = $2`,
      [sessionId, eventId]
    );
    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, message: 'No recordings found for this session' });
    }
    // Only one summary per session: use the first row
    const summaryRow = result.rows[0];
    if (!summaryRow.generated_summary) {
      // Run summary generation synchronously for demo (replace with async if needed)
      await generateSummaryForSession(sessionId, eventId, language, patientId);
      // Fetch updated summary
      const updated = await client.query(
        `SELECT generated_summary FROM recording_summaries WHERE session_id = $1 AND event_id = $2 LIMIT 1`,
        [sessionId, eventId]
      );
      const summary = updated.rows[0]?.generated_summary;
      // Check if summary is null (still processing) vs empty string (no content to summarize)
      console.log(sanitizeSummaryMarkdown(summary), 'SAUMMARY')
      if (summary === null) {
        return res.json({ success: true, status: 'processing' });
      }
      // Summary exists (even if empty) - return as done
      if (!isStopped) return res.json({ success: true, status: 'done', summary: summary || 'No audio content was detected for summarization.' });
    }
    console.log(isStopped, 'isStopped');

    // Summary present, return it
    if (!isStopped) return res.json({ success: true, status: 'done', summary: sanitizeSummaryMarkdown(summaryRow.generated_summary) });
  } catch (err) {
    console.error('[API] Error in get_summary_by_session:', err);
    res.status(500).json({ success: false, message: 'Database error', error: err.message });
  } finally {
    if (client) await client.release();
  }
});

// API to save recording summary metadata
// app.post('/api/save_recording_summary', async (req, res) => {
//   console.log('[API] /api/save_recording_summary called. Body:', req.body);
//   const { filepath, sessionId, patientId, eventId, generatedSummary, timestamp } = req.body;
//   if (!filepath || !sessionId || !patientId || !eventId) {
//     console.warn('[API] Missing required fields:', { filepath, sessionId, patientId, eventId });
//     return res.status(400).json({ success: false, message: 'Missing required fields' });
//   }
//   let client;
//   try {
//     client = await getClient();
//     const result = await client.query(
//       `INSERT INTO recording_summaries (filepath, session_id, patient_id, event_id, created_at, generated_summary)
//        VALUES ($1, $2, $3, $4, $5, $6)
//        ON CONFLICT (filepath, session_id, patient_id, event_id)
//        DO UPDATE SET generated_summary = EXCLUDED.generated_summary, created_at = EXCLUDED.created_at
//        RETURNING *`,
//       [filepath, sessionId, patientId, eventId, timestamp || new Date(), generatedSummary]
//     );
//     res.status(200).json({ success: true, data: result.rows[0] });
//   } catch (err) {
//     console.error('[DB] Error saving recording summary:', err);
//     res.status(500).json({ success: false, message: 'Database error', error: err.message });
//   } finally {
//     if (client) await client.end();
//   }
// });

app.get('/api/db_test', async (req, res) => {
  let client;
  try {
    client = await getClient();
    await client.query('SELECT 1');
    res.json({ success: true, message: 'DB connection OK' });
  } catch (err) {
    res.status(500).json({ success: false, message: 'DB connection failed', error: err.message });
  } finally {
    if (client) await client.release();
  }
});

// GET /api/job_status/:id - returns {status: 'processing'|'done'|'error'}
app.get('/api/job_status/:id', (req, res) => {
  const job = jobStore[req.params.id];
  if (!job) return res.status(404).json({ success: false, message: 'Job not found' });
  res.json({ success: true, status: job.status, error: job.error });
});

// --- Markdown sanitizer and header-to-bold converter for summaries ---
function sanitizeSummaryMarkdown(summary) {
  if (!summary || typeof summary !== 'string') return summary;
  // Convert markdown headers (## Heading) to bold (**Heading**)
  let processed = summary.replace(/^##\s*(.+)$/gm, '**$1**');
  // Optionally, also convert single # headers
  processed = processed.replace(/^#\s*(.+)$/gm, '**$1**');
  // Remove any remaining markdown formatting except bold
  processed = processed
    // .replace(/^#+\s+/gm, '') // (no longer needed, handled above)
    // .replace(/\*\*(.*?)\*\*/g, '$1') // keep bold
    .replace(/\*(.*?)\*/g, '$1') // italics
    .replace(/`([^`]+)`/g, '$1') // inline code
    .replace(/\[(.*?)\]\((.*?)\)/g, '$1') // links
    .replace(/!\[(.*?)\]\((.*?)\)/g, '') // images
    .replace(/^>\s+/gm, '') // blockquotes
    .replace(/^-\s+/gm, '') // list dashes
    .replace(/^\d+\.\s+/gm, '') // numbered lists
    .replace(/\r?\n/g, '\n') // normalize newlines
    .replace(/\n{2,}/g, '\n\n') // collapse multiple newlines
    .trim();
  return processed;
}

// GET /api/job_result/:id - returns consistent format for all states
app.get('/api/job_result/:id', (req, res) => {
  const job = jobStore[req.params.id];
  if (!job) return res.status(404).json({ success: false, message: 'Job not found' });
  // Sanitize combinedSummary to remove markdown formatting
  let cleanSummary = job.combinedSummary ? sanitizeSummaryMarkdown(job.combinedSummary) : null;
  res.json({
    success: true,
    jobId: req.params.id,
    results: job.results || null,
    combinedSummary: cleanSummary,
    status: job.status || null,
    error: job.error || null
  });
});

// GET /api/patient_summaries/:patientId - returns list of summaries for a patient
app.get('/api/patient_summaries/:patientId', async (req, res) => {
  const { patientId } = req.params;
  if (!patientId) {
    return res.status(400).json({ success: false, message: 'Missing patientId' });
  }

  let client;
  try {
    client = await getClient();
    // Fetch summaries for the patient
    const result = await client.query(
      `SELECT id, session_id, event_id, created_at, generated_summary, updated_summary
       FROM recording_summaries
       WHERE patient_id = $1 AND generated_summary IS NOT NULL
       ORDER BY created_at DESC`,
      [patientId]
    );

    // Fetch previous_case_notes_summary from patients table
    let previousCaseNotesSummary = null;
    try {
      const patientResult = await client.query(
        `SELECT previous_case_notes_summary FROM patients WHERE reg_no = $1`,
        [patientId]
      );
      if (patientResult.rows.length > 0) {
        previousCaseNotesSummary = patientResult.rows[0].previous_case_notes_summary || null;
      }
    } catch (e) {
      console.warn('[API] Could not fetch previous_case_notes_summary:', e.message);
    }

    const summaries = result.rows.map(row => ({
      id: row.id,
      sessionId: row.session_id,
      eventId: row.event_id,
      createdAt: row.created_at,
      summary: row.generated_summary,
      updatedSummary: row.updated_summary,
      previousCaseNotesSummary // include for each summary
    }));

    res.json({ success: true, summaries });
  } catch (err) {
    console.error('[API] Error fetching patient summaries:', err);
    res.status(500).json({ success: false, message: 'Database error', error: err.message });
  } finally {
    if (client) await client.release();
  }
});

// --- Save WAV and summary logic ---
app.post('/api/save_wav', upload.single('audio'), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ success: false, message: 'No file uploaded' });
  }
  let relPath = path.relative(RECORDINGS_DIR, req.file.path).replace(/\\/g, '/');
  const {
    sessionId, patientId, eventId, generatedSummary, timestamp,
    consultantId, consultantName, departmentId, departmentName
  } = req.body;
  if (!sessionId || !patientId || !eventId) {
    return res.status(400).json({ success: false, message: 'Missing required fields' });
  }
  let client;
  try {
    client = await getClient();
    // Save or update summary (no filepath) FIRST
    const result = await client.query(
      `INSERT INTO recording_summaries (
        session_id, patient_id, event_id, created_at, generated_summary,
        consultant_id, consultant_name, department_id, department_name
      )
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
       ON CONFLICT (session_id, patient_id, event_id)
       DO UPDATE SET 
         generated_summary = EXCLUDED.generated_summary,
         created_at = EXCLUDED.created_at,
         consultant_id = EXCLUDED.consultant_id,
         consultant_name = EXCLUDED.consultant_name,
         department_id = EXCLUDED.department_id,
         department_name = EXCLUDED.department_name
       RETURNING *`,
      [
        sessionId, patientId, eventId, timestamp || new Date(), generatedSummary,
        consultantId || null, consultantName || null, departmentId || null, departmentName || null
      ]
    );
    // Save file path to recording_files (no ON CONFLICT clause)
    await client.query(
      `INSERT INTO recording_files (filepath, session_id, event_id, created_at)
       VALUES ($1, $2, $3, $4)`,
      [relPath, sessionId, eventId, timestamp || new Date()]
    );
    res.status(200).json({ success: true, data: result.rows[0], filename: req.file.filename, relPath });
  } catch (err) {
    console.error('[DB] Error saving recording file:', err);
    res.status(500).json({ success: false, message: 'Database error', error: err.message });
  } finally {
    if (client) await client.release();
  }
});

// Add this to your server.js
app.get('/api/download/:filename', (req, res) => {
  const filename = req.params.filename;
  const filePath = path.join(RECORDINGS_DIR, filename);

  console.log(`[DOWNLOAD] Requested file: ${filename}`);
  console.log(`[DOWNLOAD] Full path: ${filePath}`);

  // Security check - ensure filename doesn't contain path traversal
  if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
    return res.status(400).json({ error: 'Invalid filename' });
  }

  // Check if file exists
  if (!fs.existsSync(filePath)) {
    console.error(`[DOWNLOAD] File not found: ${filePath}`);
    return res.status(404).json({ error: 'File not found' });
  }

  // Get file stats
  const stats = fs.statSync(filePath);
  console.log(`[DOWNLOAD] File stats: size=${stats.size}, isFile=${stats.isFile()}`);

  // Set proper headers
  res.setHeader('Content-Type', 'audio/wav');
  res.setHeader('Content-Length', stats.size);
  res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

  // Stream the file
  const fileStream = fs.createReadStream(filePath);
  fileStream.pipe(res);

  fileStream.on('error', (err) => {
    console.error(`[DOWNLOAD] Stream error:`, err);
    res.status(500).json({ error: 'Error streaming file' });
  });
});

// Add this to serve static files from recordings_web
app.use('/recordings_web', express.static(RECORDINGS_DIR, {
  setHeaders: (res, path) => {
    if (path.endsWith('.wav')) {
      res.setHeader('Content-Type', 'audio/wav');
    }
  }
}));
app.get('/api/list_recordings', async (req, res) => {
  const { session, event } = req.query;
  if (!session) {
    return res.status(400).json({ success: false, message: 'Missing session parameter' });
  }
  let client;
  try {
    client = await getClient();
    let query = `SELECT filepath FROM recording_files WHERE session_id = $1`;
    let params = [session];
    if (event) {
      query += ' AND event_id = $2';
      params.push(event);
    }
    const result = await client.query(query, params);
    const files = (result.rows || [])
      .map(r => r.filepath)
      .filter(f => {
        if (!f) return false;
        const absPath = path.join(RECORDINGS_DIR, f);
        return fs.existsSync(absPath) && f.endsWith('.wav');
      });
    res.json({ success: true, files });
  } catch (err) {
    res.status(500).json({ success: false, message: 'Error reading recordings from DB', error: err.message });
  } finally {
    if (client) await client.release();
  }
});

// Endpoint to download all audio files for a session and event as a zip
const archiver = require('archiver');
app.get('/api/download_all', async (req, res) => {
  const { session, event } = req.query;
  if (!session) {
    return res.status(400).send('Missing session parameter');
  }
  let client;
  try {
    client = await getClient();
    let query = `SELECT filepath FROM recording_files WHERE session_id = $1`;
    let params = [session];
    if (event) {
      query += ' AND event_id = $2';
      params.push(event);
    }
    const result = await client.query(query, params);
    const files = (result.rows || [])
      .map(r => r.filepath)
      .filter(f => {
        if (!f) return false;
        const absPath = path.join(RECORDINGS_DIR, f);
        return fs.existsSync(absPath) && f.endsWith('.wav');
      });
    if (files.length === 0) {
      return res.status(404).send('No files found for this session/event');
    }
    res.setHeader('Content-Type', 'application/zip');
    res.setHeader('Content-Disposition', `attachment; filename="session_${session}${event ? '_' + event : ''}_audio.zip"`);
    const archive = archiver('zip', { zlib: { level: 9 } });
    archive.pipe(res);
    files.forEach(f => {
      archive.file(path.join(RECORDINGS_DIR, f), { name: f });
    });
    archive.finalize();
  } catch (err) {
    res.status(500).send('Error creating zip: ' + err.message);
  } finally {
    if (client) await client.release();
  }
});

// --- REPORT ISSUE EMAIL ENDPOINT ---
const reportUpload = multer({ storage: multer.memoryStorage() });
const emailTransporter = require('./emailTransporter');

// Gmail: 25MB max message size, but base64 encoding increases size (~33%)
// So, set a safe total limit (20MB) and per-file limit (10MB)
const MAX_TOTAL_ATTACHMENT_SIZE = 20 * 1024 * 1024; // 20MB
const MAX_SINGLE_ATTACHMENT_SIZE = 10 * 1024 * 1024; // 10MB

app.post('/api/report_issue', reportUpload.array('attachments', 10), async (req, res) => {
  try {
    const {
      title,
      description,
      sessionId,
      eventId,
      patientId,
      patientName,
      consultantId,
      consultantName,
      departmentId,
      departmentName,
      timestamp
    } = req.body;
    const files = req.files || [];
    if (!title || !description) {
      return res.status(400).json({ success: false, message: 'Missing title or description.' });
    }
    // Check attachment sizes
    let totalSize = 0;
    for (const file of files) {
      if (file.size > MAX_SINGLE_ATTACHMENT_SIZE) {
        return res.status(413).json({
          success: false,
          message: `Attachment '${file.originalname}' exceeds the 10MB per-file limit.`
        });
      }
      totalSize += file.size;
    }
    if (totalSize > MAX_TOTAL_ATTACHMENT_SIZE) {
      return res.status(413).json({
        success: false,
        message: 'Total attachment size exceeds the 20MB limit. Please remove some files or choose smaller ones.'
      });
    }
    // Prepare attachments for nodemailer
    const attachments = files.map(file => ({
      filename: file.originalname,
      content: file.buffer,
      contentType: file.mimetype
    }));
    const htmlBody = `
  <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 700px; margin: 20px auto; padding: 0; background: #ffffff; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
    <!-- Header -->
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 12px 12px 0 0; text-align: center;">
      <h1 style="margin: 0; font-size: 28px; font-weight: 600;">🩺 ALaaS Issue Report</h1>
      </div>

    <!-- Content -->
    <div style="padding: 30px;">
      <div style="background: #f8fafc; border-left: 4px solid #3b82f6; padding: 20px; margin-bottom: 25px; border-radius: 0 8px 8px 0;">
        <h3 style="margin: 0 0 15px 0; color: #1e40af; font-size: 18px;">📋 Issue Details</h3>
        <div style="margin-bottom: 15px;">
          <strong style="color: #374151; font-size: 16px;">Title:</strong>
          <div style="background: white; padding: 12px; border-radius: 6px; margin-top: 5px; border: 1px solid #e5e7eb; font-size: 16px; color: #111827;">${title}</div>
        </div>
        <div>
          <strong style="color: #374151; font-size: 16px;">Description:</strong>
          <div style="background: white; padding: 15px; border-radius: 6px; margin-top: 5px; border: 1px solid #e5e7eb; white-space: pre-wrap; line-height: 1.6; font-size: 15px; color: #111827;">${description}</div>
        </div>
      </div>

      ${consultantId && consultantId !== 'unknown' ? `
      <!-- Reported By Section -->
      <div style="background: #ecfdf5; border-left: 4px solid #10b981; padding: 20px; margin-bottom: 25px; border-radius: 0 8px 8px 0;">
        <h3 style="margin: 0 0 15px 0; color: #047857; font-size: 18px;">👨‍⚕️ Reported By</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
          <div>
            <strong style="color: #374151;">Consultant:</strong><br>
            <span style="color: #111827; font-size: 15px;">${consultantName || 'Not specified'}</span>
          </div>
          <div>
            <strong style="color: #374151;">Consultant ID:</strong><br>
            <span style="color: #111827; font-size: 15px;">${consultantId || 'Not specified'}</span>
          </div>
          <div>
            <strong style="color: #374151;">Department:</strong><br>
            <span style="color: #111827; font-size: 15px;">${departmentName || 'Not specified'}</span>
          </div>
          <div>
            <strong style="color: #374151;">Department ID:</strong><br>
            <span style="color: #111827; font-size: 15px;">${departmentId || 'Not specified'}</span>
          </div>
        </div>
      </div>
      ` : ''}

      <!-- Patient Context Section -->
      <div style="background: #fef3c7; border-left: 4px solid #f59e0b; padding: 20px; margin-bottom: 25px; border-radius: 0 8px 8px 0;">
        <h3 style="margin: 0 0 15px 0; color: #92400e; font-size: 18px;">🏥 Patient Context</h3>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
          <div>
            <strong style="color: #374151;">Patient Name:</strong><br>
            <span style="color: #111827; font-size: 15px;">${patientName || 'Not specified'}</span>
          </div>
          <div>
            <strong style="color: #374151;">Patient ID:</strong><br>
            <span style="color: #111827; font-size: 15px;">${patientId || 'Not specified'}</span>
          </div>
          <div>
            <strong style="color: #374151;">Session ID:</strong><br>
            <span style="color: #111827; font-size: 15px;">${sessionId || 'Not specified'}</span>
          </div>
          <div>
            <strong style="color: #374151;">Event ID:</strong><br>
            <span style="color: #111827; font-size: 15px;">${eventId || 'Not specified'}</span>
          </div>
        </div>
      </div>

      ${attachments && attachments.length > 0 ? `
      <!-- Attachments Section -->
      <div style="background: #fef2f2; border-left: 4px solid #ef4444; padding: 20px; margin-bottom: 25px; border-radius: 0 8px 8px 0;">
        <h3 style="margin: 0 0 15px 0; color: #dc2626; font-size: 18px;">📎 Attachments</h3>
        <p style="margin: 0; color: #374151;">This report includes <strong>${attachments.length}</strong> attachment(s):</p>
        <ul style="margin: 10px 0 0 20px; color: #6b7280;">
          ${attachments.map(att => `<li style="margin: 5px 0;">${att.filename}</li>`).join('')}
        </ul>
      </div>
      ` : ''}

      <!-- Timestamp Section -->
      <div style="background: #f1f5f9; border-left: 4px solid #64748b; padding: 20px; border-radius: 0 8px 8px 0;">
        <h3 style="margin: 0 0 10px 0; color: #475569; font-size: 18px;">⏰ Report Information</h3>
        <p style="margin: 0; color: #374151;">
          <strong>Reported At:</strong> ${new Date(timestamp || new Date()).toLocaleString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: 'Asia/Kolkata'
    })}
        </p>
      </div>
    </div>

    <!-- Footer -->
    <div style="background: #f8fafc; padding: 25px; border-radius: 0 0 12px 12px; border-top: 1px solid #e5e7eb; text-align: center;">
      <p style="margin: 0 0 10px 0; color: #6b7280; font-size: 14px;">
        This issue was reported through the ALaaS Medical AI Assistant platform.<br>
        Please review and take appropriate action to resolve the reported issue.
      </p>
      <div style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #e5e7eb;">
        <p style="margin: 0; color: #9ca3af; font-size: 13px; font-weight: 500;">
          🏥 Behive Healthcare Team | ALaaS Support<br>
          <span style="color: #6b7280;">Advancing Healthcare with AI Technology</span>
        </p>
      </div>
    </div>
  </div>
`;

    console.log(htmlBody, 'htmlBody');

    // Send email
    await emailTransporter.sendMail({
      from: '<EMAIL>',
      to: ['<EMAIL>'], // add more as needed
      cc: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
      subject: `[ALaaS Report Issue] ${title}`,
      text: `ALaaS Reported Issue\n\nTitle: ${title}\n\nDescription: ${description}\n\nPatient ID: ${patientId || 'N/A'}\nPatient Name: ${patientName || 'N/A'}\nSession ID: ${sessionId || 'N/A'}\nEvent ID: ${eventId || 'N/A'}\nTimestamp: ${timestamp || new Date().toISOString()}\n\nAttachments: ${attachments.length > 0 ? attachments.map(a => a.filename).join(', ') : 'None'}`,
      html: htmlBody,
      attachments
    });
    res.json({ success: true, message: 'Report sent successfully.' });
  } catch (err) {
    console.error('[REPORT ISSUE] Email send failed:', err);
    res.status(500).json({ success: false, message: 'Failed to send report.', error: err.message });
  }
});

// --- Ensure required columns exist in recording_summaries table ---
async function ensureRecordingSummariesColumns() {
  let client;
  try {
    client = await getClient();
    // updated_summary
    let colCheck = await client.query(`SELECT column_name FROM information_schema.columns WHERE table_name='recording_summaries' AND column_name='updated_summary'`);
    if (colCheck.rows.length === 0) {
      await client.query(`ALTER TABLE recording_summaries ADD COLUMN updated_summary text`);
      console.log('[DB] Added updated_summary column to recording_summaries');
    }
    // consultant_id
    colCheck = await client.query(`SELECT column_name FROM information_schema.columns WHERE table_name='recording_summaries' AND column_name='consultant_id'`);
    if (colCheck.rows.length === 0) {
      await client.query(`ALTER TABLE recording_summaries ADD COLUMN consultant_id varchar`);
      console.log('[DB] Added consultant_id column to recording_summaries');
    }
    // consultant_name
    colCheck = await client.query(`SELECT column_name FROM information_schema.columns WHERE table_name='recording_summaries' AND column_name='consultant_name'`);
    if (colCheck.rows.length === 0) {
      await client.query(`ALTER TABLE recording_summaries ADD COLUMN consultant_name varchar`);
      console.log('[DB] Added consultant_name column to recording_summaries');
    }
    // department_id
    colCheck = await client.query(`SELECT column_name FROM information_schema.columns WHERE table_name='recording_summaries' AND column_name='department_id'`);
    if (colCheck.rows.length === 0) {
      await client.query(`ALTER TABLE recording_summaries ADD COLUMN department_id varchar`);
      console.log('[DB] Added department_id column to recording_summaries');
    }
    // department_name
    colCheck = await client.query(`SELECT column_name FROM information_schema.columns WHERE table_name='recording_summaries' AND column_name='department_name'`);
    if (colCheck.rows.length === 0) {
      await client.query(`ALTER TABLE recording_summaries ADD COLUMN department_name varchar`);
      console.log('[DB] Added department_name column to recording_summaries');
    }
  } catch (err) {
    console.error('[DB] Error ensuring columns in recording_summaries:', err);
  } finally {
    if (client) await client.release();
  }
}
// Call on startup
ensureRecordingSummariesColumns();

// --- Setup Patient Data Sync Tables ---
async function setupPatientDataTables() {
  let client;
  try {
    client = await getClient();

    // Create departments table
    await client.query(`
      CREATE TABLE IF NOT EXISTS departments (
        department_id VARCHAR PRIMARY KEY,
        name VARCHAR NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create doctors table
    await client.query(`
      CREATE TABLE IF NOT EXISTS doctors (
        doctor_id VARCHAR PRIMARY KEY,
        name VARCHAR NOT NULL,
        department_id VARCHAR,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (department_id) REFERENCES departments(department_id) ON DELETE SET NULL
      );
    `);

    // Create patients table
    await client.query(`
      CREATE TABLE IF NOT EXISTS patients (
        reg_no VARCHAR PRIMARY KEY,
        name VARCHAR NOT NULL,
        dob DATE,
        gender VARCHAR,
        vitals JSONB,
        first_visit_info JSONB,
        previous_case_notes_summary TEXT,
        is_new_visit BOOLEAN NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
      );
    `);

    // Create encounters table
    await client.query(`
      CREATE TABLE IF NOT EXISTS encounters (
        encounter_id BIGINT PRIMARY KEY,
        patient_reg_no VARCHAR NOT NULL,
        doctor_id VARCHAR,
        department_id VARCHAR,
        encounter_date DATE,
        case_notes JSONB,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (patient_reg_no) REFERENCES patients(reg_no) ON DELETE CASCADE,
        FOREIGN KEY (doctor_id) REFERENCES doctors(doctor_id) ON DELETE SET NULL,
        FOREIGN KEY (department_id) REFERENCES departments(department_id) ON DELETE SET NULL
      );
    `);

    console.log('[DB] Patient data sync tables created/verified successfully');
  } catch (err) {
    console.error('[DB] Error setting up patient data tables:', err);
  } finally {
    if (client) client.release();
  }
}

// Call on startup
setupPatientDataTables();

// --- Ensure previous_case_notes_summary column exists in patients table ---
// async function ensurePreviousCaseNotesSummaryColumn() {
//   let client;
//   try {
//     client = await getClient();
//     // Check if column exists
//     const colCheck = await client.query(`
//       SELECT column_name
//       FROM information_schema.columns
//       WHERE table_name='patients' AND column_name='previous_case_notes_summary'
//     `);

//     if (colCheck.rows.length === 0) {
//       // Add the column
//       await client.query(`ALTER TABLE patients ADD COLUMN previous_case_notes_summary TEXT`);
//       console.log('[DB] Added previous_case_notes_summary column to patients table');
//     } else {
//       console.log('[DB] previous_case_notes_summary column already exists in patients table');
//     }
//   } catch (err) {
//     console.error('[DB] Error ensuring previous_case_notes_summary column:', err);
//   } finally {
//     if (client) client.release();
//   }
// }

// // Call on startup
// ensurePreviousCaseNotesSummaryColumn();

// --- Helper function to check encounters and extract case notes ---
// --- Helper function to extract and format vitals for summary context ---
function extractAndFormatRecentVitals(vitals, audioCaptureDate = null) {
  if (!Array.isArray(vitals) || vitals.length === 0) return '';

  // Group vitals by date (YYYY-MM-DD)
  const vitalsByDate = {};
  vitals.forEach(vital => {
    if (!vital.date) return;
    const dateKey = new Date(vital.date).toISOString().slice(0, 10); // YYYY-MM-DD
    if (!vitalsByDate[dateKey]) vitalsByDate[dateKey] = [];
    vitalsByDate[dateKey].push(vital);
  });

  // Sort dates descending
  const sortedDates = Object.keys(vitalsByDate).sort((a, b) => new Date(b) - new Date(a));

  // If audioCaptureDate is provided, ensure it's included as the latest
  let selectedDates = sortedDates;
  if (audioCaptureDate) {
    const audioDateKey = new Date(audioCaptureDate).toISOString().slice(0, 10);
    if (!selectedDates.includes(audioDateKey)) {
      selectedDates.unshift(audioDateKey);
    }
    selectedDates = [...new Set(selectedDates)];
  }
  // Limit to latest 2 dates
  selectedDates = selectedDates.slice(0, 2);

  // Format vitals for each date
  let formatted = '';
  selectedDates.forEach(dateKey => {
    const vitalsList = vitalsByDate[dateKey];
    if (!vitalsList || vitalsList.length === 0) return;
    formatted += `## Vitals for ${dateKey}\n`;
    vitalsList.forEach(v => {
      formatted += `• ${v.componentName}: ${v.value}\n`;
    });
    formatted += '\n';
  });
  return formatted.trim();
}
async function checkEncountersAndExtractCaseNotes(patientRegNo, client) {
  try {
    console.log(`[AI] Checking encounters for patient: ${patientRegNo}`);

    // Query to get all encounters with case notes for the patient
    const encountersResult = await client.query(`
      SELECT encounter_id, patient_reg_no, encounter_date, case_notes, created_at
      FROM encounters
      WHERE patient_reg_no = $1
      AND case_notes IS NOT NULL
      AND case_notes != 'null'
      AND case_notes != '[]'
      ORDER BY encounter_date DESC, created_at DESC
    `, [patientRegNo]);

    if (encountersResult.rows.length === 0) {
      console.log(`[AI] No encounters with case notes found for patient: ${patientRegNo}`);
      return [];
    }

    console.log(`[AI] Found ${encountersResult.rows.length} encounters with case notes for patient: ${patientRegNo}`);

    // Process and filter encounters that have meaningful case notes
    const encountersWithValidCaseNotes = [];

    for (const encounter of encountersResult.rows) {
      try {
        // Parse case notes (they are stored as JSON strings)
        const caseNotes = typeof encounter.case_notes === 'string'
          ? JSON.parse(encounter.case_notes)
          : encounter.case_notes;

        // Check if case notes array has meaningful content
        if (Array.isArray(caseNotes) && caseNotes.length > 0) {
          const hasValidNotes = caseNotes.some(note =>
            note && note.head && note.text &&
            note.text.trim() !== '' &&
            note.text.trim() !== '<div></div>' &&
            note.text.trim() !== '<p></p>'
          );

          if (hasValidNotes) {
            encountersWithValidCaseNotes.push({
              encounter_id: encounter.encounter_id,
              encounter_date: encounter.encounter_date,
              case_notes: caseNotes,
              created_at: encounter.created_at
            });
          }
        }
      } catch (parseError) {
        console.warn(`[AI] Error parsing case notes for encounter ${encounter.encounter_id}:`, parseError);
      }
    }

    console.log(`[AI] Found ${encountersWithValidCaseNotes.length} encounters with valid case notes for patient: ${patientRegNo}`);

    // Filter encounters to only those within the past 1 year
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
    const encountersWithinYear = encountersWithValidCaseNotes.filter(encounter => {
      if (!encounter.encounter_date) return false;
      const encDate = new Date(encounter.encounter_date);
      return encDate >= oneYearAgo;
    });

    // Limit to latest 4 encounters within the past year
    const limitedEncounters = encountersWithinYear.slice(0, 8);

    if (limitedEncounters.length < encountersWithinYear.length) {
      console.log(`[AI] Limited to latest ${limitedEncounters.length} encounters (from ${encountersWithinYear.length} within past year) for summary generation`);
    }

    return limitedEncounters;

  } catch (error) {
    console.error('[AI] Error checking encounters and extracting case notes:', error);
    return [];
  }
}

// --- AI Service for Case Notes Summary Generation ---
async function generateCaseNotesSummary(encountersWithCaseNotes) {
  try {
    if (!encountersWithCaseNotes || encountersWithCaseNotes.length === 0) {
      console.log('[AI] No encounters with case notes found for summary generation');
      return '';
    }

    // Extract and format case notes with dates and age calculation
    let formattedCaseNotes = '';
    const currentDate = new Date();

    for (let i = 0; i < encountersWithCaseNotes.length; i++) {
      const encounter = encountersWithCaseNotes[i];
      const encounterDate = encounter.encounter_date || encounter.date || 'Unknown Date';
      const caseNotes = encounter.case_notes || encounter.caseNotes || [];

      // Calculate age of encounter
      let encounterAge = '';
      if (encounterDate !== 'Unknown Date') {
        try {
          const encDate = new Date(encounterDate);
          const diffTime = Math.abs(currentDate - encDate);
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

          if (diffDays < 30) {
            encounterAge = `(${diffDays} days ago)`;
          } else if (diffDays < 365) {
            const months = Math.floor(diffDays / 30);
            encounterAge = `(${months} month${months > 1 ? 's' : ''} ago)`;
          } else {
            const years = Math.floor(diffDays / 365);
            encounterAge = `(${years} year${years > 1 ? 's' : ''} ago)`;
          }
        } catch (e) {
          encounterAge = '';
        }
      }

      if (Array.isArray(caseNotes) && caseNotes.length > 0) {
        const encounterLabel = i === 0 ? 'MOST RECENT' : i === 1 ? 'PREVIOUS' : `ENCOUNTER ${i + 1}`;
        formattedCaseNotes += `\n--- ${encounterLabel} ENCOUNTER: ${encounterDate} ${encounterAge} ---\n`;

        for (const note of caseNotes) {
          if (note.head && note.text) {
            // Remove HTML tags from text for cleaner processing
            const cleanText = note.text.replace(/<[^>]*>/g, '').trim();
            formattedCaseNotes += `${note.head}: ${cleanText}\n`;
          }
        }
        formattedCaseNotes += '\n';
      }
    }

    if (!formattedCaseNotes.trim()) {
      console.log('[AI] No valid case notes content found for summary generation');
      return '';
    }

    console.log('[AI] Generating case notes summary using OpenAI...');

    // Import axios for API calls
    const axios = require('axios');

    // Get OpenAI configuration
    const OPENAI_API_KEY = process.env.OPENAI_API_KEY || process.env.AZURE_OPENAI_API_KEY;
    const OPENAI_API_BASE = process.env.OPENAI_API_BASE || process.env.AZURE_OPENAI_ENDPOINT_URL || 'https://api.openai.com/v1';
    const OPENAI_DEPLOYMENT = process.env.AZURE_OPENAI_DEPLOYMENT_NAME;

    if (!OPENAI_API_KEY) {
      throw new Error('OpenAI API key not set');
    }



    // Create optimized prompt for crisp and clear case notes summary
    const encounterCount = encountersWithCaseNotes.length;
    const timeframeNote = encounterCount === 1 ? 'most recent encounter' : `latest ${encounterCount} encounters`;

    const caseNotesPrompt = `You are a medical AI assistant. Create a CRISP and CLEAR summary of the patient's previous medical history from the ${timeframeNote}.

REQUIREMENTS:
• Use BULLET POINTS for easy scanning
• Prioritize RECENT findings over older ones
• Focus on CLINICALLY SIGNIFICANT information only
• Organize by medical importance, not chronologically

INCLUDE ONLY:
✓ Active/ongoing conditions
✓ Significant diagnoses and treatments
✓ Current medications and responses
✓ Important test results or procedures
✓ Allergies or contraindications
✓ Notable patterns or trends

EXCLUDE:
✗ Routine follow-ups without findings
✗ Minor complaints that resolved
✗ Excessive detail or repetitive information
✗ Administrative notes

FORMAT:
## Medical History Summary
• **Primary Conditions:** [List active conditions]
• **Key Treatments:** [Current/recent treatments]
• **Medications:** [Active medications with responses]
• **Diagnostics:** [Significant test results]
• **Clinical Notes:** [Important observations/patterns]


Previous Case Notes:
${formattedCaseNotes}

Generate a crisp, clear medical history summary:`;

    let openaiUrl, headers, body;

    if (OPENAI_API_BASE.includes('azure.com')) {
      openaiUrl = `${OPENAI_API_BASE}/openai/deployments/${OPENAI_DEPLOYMENT}/chat/completions?api-version=2024-02-15-preview`;
      headers = {
        'api-key': OPENAI_API_KEY,
        'Content-Type': 'application/json',
      };
      body = {
        messages: [
          { role: 'system', content: 'You are a medical AI assistant specialized in creating CRISP and CLEAR case notes summaries. Always prioritize brevity and clinical relevance over comprehensive detail.' },
          { role: 'user', content: caseNotesPrompt }
        ],
        max_tokens: 800,
        temperature: 0.2
      };
    } else {
      openaiUrl = `${OPENAI_API_BASE}/chat/completions`;
      headers = {
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      };
      body = {
        model: 'gpt-4',
        messages: [
          { role: 'system', content: 'You are a medical AI assistant specialized in creating CRISP and CLEAR case notes summaries. Always prioritize brevity and clinical relevance over comprehensive detail.' },
          { role: 'user', content: caseNotesPrompt }
        ],
        max_tokens: 800,
        temperature: 0.2
      };
    }


    // --- Retry logic for 429 errors ---
    const maxRetries = 5;
    let attempt = 0;
    let lastError = null;
    let delay = 2000; // Start with 2 seconds
    while (attempt < maxRetries) {
      try {
        const openaiResp = await axios.post(openaiUrl, body, { headers, timeout: 120000 });
        if (openaiResp.data.choices && openaiResp.data.choices.length > 0) {
          const generatedSummary = openaiResp.data.choices[0].message?.content || openaiResp.data.choices[0].text || '';
          console.log('[AI] Case notes summary generated successfully');
          return generatedSummary;
        } else {
          throw new Error('No response from OpenAI');
        }
      } catch (error) {
        // Check for 429 Too Many Requests
        if (error.response && error.response.status === 429) {
          attempt++;
          lastError = error;
          const retryAfter = parseInt(error.response.headers['retry-after'], 10);
          const waitTime = !isNaN(retryAfter) ? retryAfter * 1000 : delay;
          console.warn(`[AI] OpenAI rate limited (429). Retrying in ${waitTime / 1000}s (attempt ${attempt}/${maxRetries})...`);
          await new Promise(r => setTimeout(r, waitTime));
          delay = Math.min(delay * 2, 30000); // Exponential backoff, max 30s
          continue;
        } else {
          throw error;
        }
      }
    }
    // If we get here, all retries failed
    throw new Error('OpenAI API rate limit (429) - all retries failed.' + (lastError ? ' Last error: ' + lastError.message : ''));

  } catch (error) {
    console.error('[AI] Error generating case notes summary:', error);
    return `Error generating case notes summary: ${error.message}`;
  }
}

// API to update summary for a given id (now saves to updated_summary)
app.post('/api/update_summary/:id', async (req, res) => {
  const { id } = req.params;
  const { summary } = req.body;
  if (!id || typeof summary !== 'string' || !summary.trim()) {
    return res.status(400).json({ success: false, message: 'Missing id or summary in request' });
  }
  let client;
  try {
    client = await getClient();
    // Save to updated_summary instead of generated_summary
    const result = await client.query(
      `UPDATE recording_summaries SET updated_summary = $1 WHERE id = $2 RETURNING *`,
      [summary, id]
    );
    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, message: 'No record found for given id' });
    }
    res.json({ success: true, data: result.rows[0] });
  } catch (err) {
    console.error('[API] Error updating summary:', err);
    res.status(500).json({ success: false, message: 'Database error', error: err.message });
  } finally {
    if (client) await client.release();
  }
});
// API to update summary for a given sessionId (now saves to updated_summary)
app.post('/api/update_summary_by_session/:sessionId/:eventId', async (req, res) => {
  const { sessionId, eventId } = req.params;
  const { summary } = req.body;
  if (!sessionId || !eventId || typeof summary !== 'string' || !summary.trim()) {
    return res.status(400).json({ success: false, message: 'Missing sessionId, eventId, or summary in request' });
  }
  let client;
  try {
    client = await getClient();
    // Save to updated_summary for the specific sessionId and eventId
    const result = await client.query(
      `UPDATE recording_summaries SET updated_summary = $1 WHERE session_id = $2 AND event_id = $3 RETURNING *`,
      [summary, sessionId, eventId]
    );
    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, message: 'No record found for given sessionId and eventId' });
    }
    res.json({ success: true, data: result.rows });
  } catch (err) {
    console.error('[API] Error updating summary by sessionId and eventId:', err);
    res.status(500).json({ success: false, message: 'Database error', error: err.message });
  } finally {
    if (client) await client.release();
  }
});

// --- Check if sessionId + eventId exists ---
app.post('/api/check_session_event', async (req, res) => {
  const { sessionId, eventId } = req.body;
  if (!sessionId || !eventId) {
    // Always send exists: false if params missing
    return res.status(200).json({ exists: false, success: false, message: 'Missing sessionId or eventId' });
  }
  let client;
  try {
    client = await getClient();
    const result = await client.query(
      `SELECT 1 FROM recording_summaries WHERE session_id = $1 AND event_id = $2 LIMIT 1`,
      [sessionId, eventId]
    );
    if (result.rows.length > 0) {
      return res.json({ exists: true, success: true });
    } else {
      return res.json({ exists: false, success: true });
    }
  } catch (err) {
    console.error('[API] Error in check_session_event:', err);
    // Always send exists: false on error
    res.status(200).json({ exists: false, success: false, message: 'Database error', error: err.message });
  } finally {
    if (client) await client.release();
  }
});

// Helper function to validate and format dates
function validateDate(dateString) {
  if (!dateString) return null;

  try {
    const date = new Date(dateString);
    // Check if the date is valid and not NaN
    if (isNaN(date.getTime())) {
      console.warn('[API] Invalid date format:', dateString);
      return null;
    }

    // Check if the date string matches what we expect (YYYY-MM-DD format)
    const isoString = date.toISOString().split('T')[0];
    const inputDate = dateString.split('T')[0]; // Handle both date and datetime strings

    // Validate that the parsed date matches the input (catches invalid dates like 2025-06-31)
    if (isoString !== inputDate) {
      console.warn('[API] Date out of range or invalid:', dateString, 'parsed as:', isoString);
      return null;
    }

    return isoString;
  } catch (error) {
    console.warn('[API] Date parsing error:', dateString, error.message);
    return null;
  }
}

// --- Store Patient Data ---
app.post('/api/storePatientData', async (req, res) => {
  console.log('[API] /api/storePatientData called');
  const data = req.body;
  console.log(data, 'patientData');

  // Validate required fields
  if (!data) {
    return res.status(400).json({
      success: false,
      message: 'Missing required patient or consultant data'
    });
  }

  const { patient, consultant, vitals, encounters, isNewVisit, firstVisitInfo } = data;

  // Validate patient required fields
  if (!patient.regNo || !patient.name) {
    return res.status(400).json({
      success: false,
      message: 'Missing required patient fields: regNo, name'
    });
  }
  console.log(consultant, consultant?.consultantId);

  // Validate consultant required fields
  // if (!consultant.consultantId || !consultant.name) {
  //   return res.status(400).json({
  //     success: false,
  //     message: 'Missing required consultant fields: consultantId, Name'
  //   });
  // }

  let client;
  try {
    client = await getClient();
    await client.query('BEGIN');

    console.log('[API] Patient data received:', {
      patientRegNo: patient.regNo,
      patientName: patient.name,
      consultantId: consultant?.consultantId,
      consultantName: consultant?.name,
      departmentId: consultant?.departmentId,
      departmentName: consultant?.departmentName
    });

    // Upsert Department
    if (consultant?.departmentId && consultant?.departmentName) {
      await client.query(`
        INSERT INTO departments (department_id, name)
        VALUES ($1, $2)
        ON CONFLICT (department_id)
        DO UPDATE SET
          name = EXCLUDED.name,
          updated_at = CURRENT_TIMESTAMP
      `, [consultant.departmentId, consultant.departmentName]);
      console.log('[API] Department upserted:', consultant.departmentId);
    }

    // Upsert Doctor
    if (consultant) {
      await client.query(`
      INSERT INTO doctors (doctor_id, name, department_id)
      VALUES ($1, $2, $3)
      ON CONFLICT (doctor_id)
      DO UPDATE SET
        name = EXCLUDED.name,
        department_id = EXCLUDED.department_id,
        updated_at = CURRENT_TIMESTAMP
    `, [consultant.consultantId, consultant.name, consultant.departmentId || null]);
    }
    console.log('[API] Doctor upserted:', consultant?.consultantId);

    // Upsert Patient
    const validatedDob = validateDate(patient.dob);
    await client.query(`
      INSERT INTO patients (reg_no, name, dob, gender, vitals, first_visit_info,is_new_visit)
      VALUES ($1, $2, $3, $4, $5, $6,$7)
      ON CONFLICT (reg_no)
      DO UPDATE SET
        name = EXCLUDED.name,
        dob = EXCLUDED.dob,
        gender = EXCLUDED.gender,
        vitals = EXCLUDED.vitals,
        first_visit_info = EXCLUDED.first_visit_info,
        is_new_visit = EXCLUDED.is_new_visit,
        updated_at = CURRENT_TIMESTAMP
    `, [
      patient.regNo,
      patient.name,
      validatedDob,
      patient.gender || null,
      JSON.stringify(vitals || {}),
      JSON.stringify(firstVisitInfo || {}),
      JSON.stringify(patient.isNewVisit || false),
    ]);
    console.log('[API] Patient upserted:', patient.regNo);

    // Upsert Encounters
    if (encounters && Array.isArray(encounters)) {
      for (const encounter of encounters) {
        if (encounter.encounterId) {
          const validatedEncounterDate = validateDate(encounter.date);
          // Ensure department exists for this encounter
          const deptId = encounter.departmentId || consultant.departmentId;
          const deptName = encounter.departmentName || consultant.departmentName || 'Unknown';
          if (deptId) {
            await client.query(`
              INSERT INTO departments (department_id, name)
              VALUES ($1, $2)
              ON CONFLICT (department_id)
              DO UPDATE SET name = EXCLUDED.name, updated_at = CURRENT_TIMESTAMP
            `, [deptId, deptName]);
          }
          const doctorId = encounter.doctorId || consultant.consultantId;
          const doctorName = encounter.doctorName || consultant.name || 'Unknown';
          if (doctorId) {
            await client.query(`
              INSERT INTO doctors (doctor_id, name)
              VALUES ($1, $2)
              ON CONFLICT (doctor_id)
              DO UPDATE SET name = EXCLUDED.name, updated_at = CURRENT_TIMESTAMP
            `, [doctorId, doctorName]);
          }
          await client.query(`
            INSERT INTO encounters (encounter_id, patient_reg_no, doctor_id, department_id, encounter_date, case_notes)
            VALUES ($1, $2, $3, $4, $5, $6)
            ON CONFLICT (encounter_id)
            DO UPDATE SET
              patient_reg_no = EXCLUDED.patient_reg_no,
              doctor_id = EXCLUDED.doctor_id,
              department_id = EXCLUDED.department_id,
              encounter_date = EXCLUDED.encounter_date,
              case_notes = EXCLUDED.case_notes,
              updated_at = CURRENT_TIMESTAMP
          `, [
            encounter.encounterId,
            patient.regNo,
            encounter.doctorId || consultant.consultantId,
            deptId,
            validatedEncounterDate,
            JSON.stringify(encounter.CaseNotes || [])
          ]);
          console.log('[API] Encounter upserted:', encounter.encounterId);
        }
      }
    }

    // Case notes summary generation is now handled by separate endpoint for better performance

    await client.query('COMMIT');

    console.log('[API] Patient data stored successfully for regNo:', patient.regNo);
    res.json({
      success: true,
      message: 'Patient data stored successfully in database',
      patientRegNo: patient.regNo,
      patientName: patient.name,
      note: 'Case notes summary generation can be triggered separately using /api/generatePreviousCaseNotesSummary'
    });

  } catch (error) {
    if (client) await client.query('ROLLBACK');
    console.error('[API] Error processing patient data:', error);
    res.status(500).json({
      success: false,
      message: 'Processing error: ' + error.message
    });
  } finally {
    if (client) client.release();
  }
});

// --- Get Patient Data for verification ---
app.get('/api/getPatientData/:regNo', async (req, res) => {
  const { regNo } = req.params;
  if (!regNo) {
    return res.status(400).json({ success: false, message: 'Missing regNo parameter' });
  }

  let client;
  try {
    client = await getClient();

    // Get patient with related data
    const patientResult = await client.query(`
      SELECT p.*,
             d.name as doctor_name, d.doctor_id,
             dept.name as department_name, dept.department_id
      FROM patients p
      LEFT JOIN encounters e ON p.reg_no = e.patient_reg_no
      LEFT JOIN doctors d ON e.doctor_id = d.doctor_id
      LEFT JOIN departments dept ON e.department_id = dept.department_id
      WHERE p.reg_no = $1
      LIMIT 1
    `, [regNo]);

    if (patientResult.rows.length === 0) {
      return res.status(404).json({ success: false, message: 'Patient not found' });
    }

    // Get encounters for this patient
    const encountersResult = await client.query(`
      SELECT e.*, d.name as doctor_name, dept.name as department_name
      FROM encounters e
      LEFT JOIN doctors d ON e.doctor_id = d.doctor_id
      LEFT JOIN departments dept ON e.department_id = dept.department_id
      WHERE e.patient_reg_no = $1
      ORDER BY e.encounter_date DESC
    `, [regNo]);

    const patient = patientResult.rows[0];
    const encounters = encountersResult.rows;

    res.json({
      success: true,
      data: {
        patient: {
          ...patient,
          vitals: typeof patient.vitals === 'string' ? JSON.parse(patient.vitals) : patient.vitals,
          first_visit_info: typeof patient.first_visit_info === 'string' ? JSON.parse(patient.first_visit_info) : patient.first_visit_info
        },
        encounters: encounters.map(enc => ({
          ...enc,
          case_notes: typeof enc.case_notes === 'string' ? JSON.parse(enc.case_notes) : enc.case_notes
        }))
      }
    });

  } catch (error) {
    console.error('[API] Error getting patient data:', error);
    res.status(500).json({ success: false, message: 'Database error: ' + error.message });
  } finally {
    if (client) client.release();
  }
});

// --- Update Patient Previous Case Notes Summary ---
app.post('/api/updatePatientCaseNotesSummary', async (req, res) => {
  const { regNo, previousCaseNotesSummary } = req.body;

  if (!regNo) {
    return res.status(400).json({ success: false, message: 'Missing regNo parameter' });
  }

  let client;
  try {
    client = await getClient();

    const result = await client.query(`
      UPDATE patients
      SET previous_case_notes_summary = $1, updated_at = CURRENT_TIMESTAMP
      WHERE reg_no = $2
      RETURNING reg_no, name, previous_case_notes_summary
    `, [previousCaseNotesSummary || null, regNo]);

    if (result.rows.length === 0) {
      return res.status(404).json({ success: false, message: 'Patient not found' });
    }

    console.log('[API] Updated previous case notes summary for patient:', regNo);
    res.json({
      success: true,
      message: 'Previous case notes summary updated successfully',
      data: result.rows[0]
    });

  } catch (error) {
    console.error('[API] Error updating previous case notes summary:', error);
    res.status(500).json({ success: false, message: 'Database error: ' + error.message });
  } finally {
    if (client) client.release();
  }
});

// --- Generate and Save Previous Case Notes Summary API (New Async Endpoint) ---
app.post('/api/generatePreviousCaseNotesSummary', async (req, res) => {
  console.log('[API] /api/generatePreviousCaseNotesSummary endpoint called');
  console.log('[API] Request body:', req.body);

  const { regNo } = req.body;

  if (!regNo) {
    console.log('[API] Missing regNo in request body');
    return res.status(400).json({
      success: false,
      message: 'Patient registration number (regNo) is required'
    });
  }

  console.log(`[API] Starting async case notes summary generation for patient: ${regNo}`);

  let client;
  try {
    client = await getClient();

    // Check if patient exists
    const patientCheck = await client.query(
      'SELECT reg_no, name FROM patients WHERE reg_no = $1',
      [regNo]
    );

    if (patientCheck.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Patient not found',
        data: { regNo }
      });
    }

    const patient = patientCheck.rows[0];
    console.log(`[API] Generating case notes summary for patient: ${patient.name} (${regNo})`);

    // Check for encounters with case notes
    const encountersWithCaseNotes = await checkEncountersAndExtractCaseNotes(regNo, client);

    if (encountersWithCaseNotes.length === 0) {
      return res.json({
        success: true,
        message: 'No encounters with case notes found for this patient',
        data: {
          regNo: regNo,
          name: patient.name,
          encountersFound: 0,
          summaryGenerated: false,
          previousCaseNotesSummary: null
        }
      });
    }

    console.log(`[API] Found ${encountersWithCaseNotes.length} encounters with case notes, generating AI summary...`);

    // Generate AI summary
    const previousCaseNotesSummary = await generateCaseNotesSummary(encountersWithCaseNotes);

    if (!previousCaseNotesSummary || previousCaseNotesSummary.trim() === '') {
      return res.status(500).json({
        success: false,
        message: 'Failed to generate case notes summary',
        data: {
          regNo: regNo,
          name: patient.name,
          encountersFound: encountersWithCaseNotes.length,
          summaryGenerated: false
        }
      });
    }

    // Update patient record with the generated summary
    const updateResult = await client.query(`
      UPDATE patients
      SET previous_case_notes_summary = $1, updated_at = CURRENT_TIMESTAMP
      WHERE reg_no = $2
      RETURNING reg_no, name, previous_case_notes_summary
    `, [previousCaseNotesSummary, regNo]);

    console.log(`[API] Case notes summary generated and saved for patient: ${regNo}`);

    res.json({
      success: true,
      message: 'Previous case notes summary generated and saved successfully',
      data: {
        regNo: updateResult.rows[0].reg_no,
        name: updateResult.rows[0].name,
        encountersFound: encountersWithCaseNotes.length,
        summaryGenerated: true,
        previousCaseNotesSummary: updateResult.rows[0].previous_case_notes_summary
      }
    });

  } catch (err) {
    console.error('[API] Error generating previous case notes summary:', err);
    res.status(500).json({
      success: false,
      message: 'Internal server error while generating case notes summary',
      error: err.message,
      data: { regNo }
    });
  } finally {
    if (client) await client.release();
  }
});

// --- Generate Case Notes Summary for Existing Patient (Original Manual Endpoint) ---
app.post('/api/generatePatientCaseNotesSummary', async (req, res) => {
  const { regNo } = req.body;

  if (!regNo) {
    return res.status(400).json({ success: false, message: 'Missing regNo parameter' });
  }

  let client;
  try {
    client = await getClient();

    // First, check if patient exists
    const patientCheck = await client.query(`
      SELECT reg_no, name FROM patients WHERE reg_no = $1
    `, [regNo]);

    if (patientCheck.rows.length === 0) {
      return res.status(404).json({ success: false, message: 'Patient not found' });
    }

    const patient = patientCheck.rows[0];
    console.log(`[API] Generating case notes summary for patient: ${patient.name} (${regNo})`);

    // Check for encounters with case notes
    const encountersWithCaseNotes = await checkEncountersAndExtractCaseNotes(regNo, client);

    if (encountersWithCaseNotes.length === 0) {
      return res.json({
        success: true,
        message: 'No encounters with case notes found for this patient',
        data: {
          regNo: regNo,
          name: patient.name,
          encountersFound: 0,
          summaryGenerated: false,
          previousCaseNotesSummary: null
        }
      });
    }

    console.log(`[API] Found ${encountersWithCaseNotes.length} encounters with case notes, generating AI summary...`);

    // Generate AI summary
    const previousCaseNotesSummary = await generateCaseNotesSummary(encountersWithCaseNotes);

    if (!previousCaseNotesSummary || previousCaseNotesSummary.trim() === '') {
      return res.status(500).json({
        success: false,
        message: 'Failed to generate case notes summary',
        data: {
          regNo: regNo,
          name: patient.name,
          encountersFound: encountersWithCaseNotes.length,
          summaryGenerated: false
        }
      });
    }

    // Update patient record with the generated summary
    const updateResult = await client.query(`
      UPDATE patients
      SET previous_case_notes_summary = $1, updated_at = CURRENT_TIMESTAMP
      WHERE reg_no = $2
      RETURNING reg_no, name, previous_case_notes_summary
    `, [previousCaseNotesSummary, regNo]);

    console.log(`[API] Case notes summary generated and saved for patient: ${regNo}`);

    res.json({
      success: true,
      message: 'Case notes summary generated and saved successfully',
      data: {
        regNo: updateResult.rows[0].reg_no,
        name: updateResult.rows[0].name,
        encountersFound: encountersWithCaseNotes.length,
        summaryGenerated: true,
        previousCaseNotesSummary: updateResult.rows[0].previous_case_notes_summary
      }
    });

  } catch (error) {
    console.error('[API] Error generating case notes summary:', error);
    res.status(500).json({
      success: false,
      message: 'Error generating case notes summary: ' + error.message
    });
  } finally {
    if (client) client.release();
  }
});

// API: Get audio files by sessionId and eventId
app.post('/api/get_audio_files_by_session_event', async (req, res) => {
  const { sessionId, eventId } = req.body;
  if (!sessionId || !eventId) {
    return res.status(400).json({ success: false, message: 'sessionId and eventId are required' });
  }
  let client;
  try {
    client = await getClient();
    const result = await client.query(
      'SELECT filepath FROM recording_files WHERE session_id = $1 AND event_id = $2',
      [sessionId, eventId]
    );
    const files = (result.rows || []).map(r => r.filepath);
    if (files.length > 0) {
      res.json({ success: true, files });
    } else {
      res.json({ success: false, message: 'No files found for this session/event', files: [] });
    }
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  } finally {
    if (client) await client.release();
  }
});

// --- Ensure recording_files table exists and foreign key is valid ---
async function ensureRecordingFilesTable() {
  let client;
  try {
    client = await getClient();
    // 1. Check for duplicate (session_id, event_id) in recording_summaries
    const dupCheck = await client.query(`
      SELECT session_id, event_id, COUNT(*)
      FROM recording_summaries
      GROUP BY session_id, event_id
      HAVING COUNT(*) > 1
      LIMIT 5;
    `);
    if (dupCheck.rows.length > 0) {
      console.error('[DB] ERROR: Duplicate (session_id, event_id) pairs found in recording_summaries. Please remove duplicates before migration. Example duplicates:', dupCheck.rows);
      return; // Do not proceed
    }
    // 2. Add unique constraint if not exists
    await client.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.table_constraints tc
          JOIN information_schema.constraint_column_usage ccu
            ON tc.constraint_name = ccu.constraint_name
          WHERE tc.table_name = 'recording_summaries'
            AND tc.constraint_type = 'UNIQUE'
            AND ccu.column_name = 'session_id'
        ) THEN
          ALTER TABLE recording_summaries ADD CONSTRAINT unique_session_event UNIQUE (session_id, event_id);
        END IF;
      END$$;
    `);
    // 3. Create recording_files table with foreign key
    await client.query(`
      CREATE TABLE IF NOT EXISTS recording_files (
        id BIGSERIAL PRIMARY KEY NOT NULL,
        filepath VARCHAR NOT NULL,
        session_id VARCHAR NOT NULL,
        event_id VARCHAR NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT current_timestamp
      );
    `);
    // 3b. Ensure unique constraint on (filepath, session_id, event_id)
    await client.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.table_constraints tc
          JOIN information_schema.constraint_column_usage ccu
            ON tc.constraint_name = ccu.constraint_name
          WHERE tc.table_name = 'recording_files'
            AND tc.constraint_type = 'UNIQUE'
            AND ccu.column_name = 'filepath'
        ) THEN
          ALTER TABLE recording_files ADD CONSTRAINT unique_filepath_session_event UNIQUE (filepath, session_id, event_id);
        END IF;
      END$$;
    `);
    // 3c. Add foreign key if not exists
    await client.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (
          SELECT 1 FROM information_schema.table_constraints tc
          WHERE tc.table_name = 'recording_files'
            AND tc.constraint_type = 'FOREIGN KEY'
        ) THEN
          ALTER TABLE recording_files ADD CONSTRAINT recording_files_session_id_event_id_fkey FOREIGN KEY (session_id, event_id) REFERENCES recording_summaries(session_id, event_id) ON DELETE CASCADE;
        END IF;
      END$$;
    `);
    // 4. Remove filepath from recording_summaries if exists
    const colCheck = await client.query(`SELECT column_name FROM information_schema.columns WHERE table_name='recording_summaries' AND column_name='filepath'`);
    if (colCheck.rows.length > 0) {
      await client.query(`ALTER TABLE recording_summaries DROP COLUMN filepath`);
      console.log('[DB] Dropped filepath column from recording_summaries');
    }
  } catch (err) {
    console.error('[DB] Error ensuring recording_files table:', err);
  } finally {
    if (client) await client.release();
  }
}
// Call on startup
// ensureRecordingFilesTable();

// --- Catch-all route for SPA ---
// This MUST be after all other API and static routes.
// It sends the main HTML file for any request that doesn't match a previous route.
// app.get('*', (req, res) => {
//   const indexPath = path.join(__dirname, 'dist', 'index.html');
//   if (fs.existsSync(indexPath)) {
//     res.sendFile(indexPath);
//   } else {
//     res.status(404).send('Application not found. Have you built the frontend?');
//   }
// });

if (USE_SECURE) {
  const server = app._createServer();
  server.listen(PORT, '0.0.0.0', () => {
    console.log(`Audio backend listening with HTTPS on port ${PORT}`);
    console.log(`Saved files will appear in: ${RECORDINGS_DIR}`);
  });
} else {
  app.listen(PORT, '0.0.0.0', () => {
    console.log(`Audio backend listening with HTTP on port ${PORT}`);
    console.log(`Saved files will appear in: ${RECORDINGS_DIR}`);
  });
}

// Graceful shutdown handler
process.on('SIGINT', async () => {
  console.log('\nReceived SIGINT. Gracefully shutting down...');
  try {
    await pool.end();
    console.log('Database pool closed.');
    process.exit(0);
  } catch (err) {
    console.error('Error during shutdown:', err);
    process.exit(1);
  }
});

process.on('SIGTERM', async () => {
  console.log('\nReceived SIGTERM. Gracefully shutting down...');
  try {
    await pool.end();
    console.log('Database pool closed.');
    process.exit(0);
  } catch (err) {
    console.error('Error during shutdown:', err);
    process.exit(1);
  }
});

// --- Patient context endpoint ---
app.get('/api/patient_context/:regNo', async (req, res) => {
  const patientId = req.params.regNo;
  if (!patientId) {
    return res.status(400).json({ success: false, message: 'Missing regNo parameter' });
  }
  let client;
  try {
    client = await getClient();
    // Get recent encounters with valid case notes
    const encountersWithCaseNotes = await checkEncountersAndExtractCaseNotes(patientId, client);

    let recentVitals = null;
    console.log(`[PATIENT_CONTEXT] Querying vitals for patient: ${patientId}`);
    const vitalsResult = await client.query(
      `SELECT vitals FROM patients WHERE reg_no = $1`,
      [patientId]
    );
    console.log(`[PATIENT_CONTEXT] Vitals query result:`, vitalsResult.rows);
    if (vitalsResult.rows.length > 0 && vitalsResult.rows[0].vitals) {
      console.log(`[SUMMARY] Found vitals for patient: ${patientId}`);
      recentVitals = extractAndFormatRecentVitals(vitalsResult.rows[0].vitals, new Date());
      console.log(`[PATIENT_CONTEXT] Formatted recent vitals:\n${recentVitals}`);
    } else {
      console.log(`[PATIENT_CONTEXT] No vitals found for patient: ${patientId}`);
    }
    res.json({
      success: true,
      encountersWithCaseNotes,
      recentVitals
    });
  } catch (err) {
    console.error('[API] Error fetching patient context:', err);
    res.status(500).json({ success: false, message: 'Database error', error: err.message });
  } finally {
    if (client) await client.release();
  }
});
