import { useState, useEffect } from 'react';
import AIDataService from '../services/aiDataService.js';

/**
 * Custom hook for managing AI data state and operations
 * @param {string} patientId - The patient ID to fetch data for
 * @param {boolean} isOpen - Whether the modal/component is open
 * @returns {Object} Hook state and methods
 */
export function useAIData(patientId, isOpen = false) {
  const [summaries, setSummaries] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [selectedSummary, setSelectedSummary] = useState(null);
  const [showSummaryDetail, setShowSummaryDetail] = useState(false);

  // Fetch summaries when modal opens and patientId is available
  useEffect(() => {
    if (isOpen && patientId && AIDataService.isValidPatientId(patientId)) {
      fetchPatientSummaries();
    }
  }, [isOpen, patientId]);

  /**
   * Fetch patient summaries from the API
   */
  const fetchPatientSummaries = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const result = await AIDataService.fetchPatientSummaries(patientId);
      
      if (result.success) {
        setSummaries(result.summaries);
      } else {
        setError(result.error);
        setSummaries([]);
      }
    } catch (err) {
      setError('An unexpected error occurred');
      setSummaries([]);
      console.error('Error in fetchPatientSummaries:', err);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Handle clicking on a summary to view details
   * @param {Object} summary - The summary object to view
   */
  const handleSummaryClick = (summary) => {
    setSelectedSummary(summary);
    setShowSummaryDetail(true);
  };

  /**
   * Handle going back to the summary list
   */
  const handleBackToList = () => {
    setShowSummaryDetail(false);
    setSelectedSummary(null);
  };

  /**
   * Reset all state (useful when closing modal)
   */
  const resetState = () => {
    setSummaries([]);
    setSelectedSummary(null);
    setShowSummaryDetail(false);
    setError(null);
    setLoading(false);
  };

  /**
   * Refresh summaries (re-fetch from API)
   */
  const refreshSummaries = () => {
    if (patientId && AIDataService.isValidPatientId(patientId)) {
      fetchPatientSummaries();
    }
  };

  /**
   * Format date/time using the service
   * @param {string} dateString - ISO date string
   * @returns {string} Formatted date string
   */
  const formatDateTime = (dateString) => {
    return AIDataService.formatDateTime(dateString);
  };

  return {
    // State
    summaries,
    loading,
    error,
    selectedSummary,
    showSummaryDetail,
    
    // Actions
    handleSummaryClick,
    handleBackToList,
    resetState,
    refreshSummaries,
    formatDateTime,
    
    // Computed values
    hasSummaries: summaries.length > 0,
    summaryCount: summaries.length
  };
}

export default useAIData;
