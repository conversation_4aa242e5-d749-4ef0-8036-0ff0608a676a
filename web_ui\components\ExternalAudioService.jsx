import React, { useEffect, useRef } from 'react';
import { transcriptLogger } from '../utils/transcriptLogger';

const SPEECH_KEY = 'EhVQLrNppOEFTy802mw5piyol8BESvZQpE5HeajO0qbba1FoY5X0JQQJ99BAACYeBjFXJ3w3AAAYACOGerJC';
const SPEECH_REGION = 'eastus';

// --- Add a global request queue for Azure STT ---
const azureRequestQueue = [];
let azureRequestInProgress = false;

async function processAzureQueue() {
  if (azureRequestInProgress || azureRequestQueue.length === 0) return;
  azureRequestInProgress = true;
  const { buffer, callback } = azureRequestQueue.shift();
  try {
    await callback(buffer);
  } finally {
    azureRequestInProgress = false;
    // Process next in queue
    if (azureRequestQueue.length > 0) {
      setTimeout(processAzureQueue, 100); // Small delay to avoid hammering
    }
  }
}

const ExternalAudioService = ({
  audioChunks,
  selectedMics,
  transcriptionChunks,
  setTranscriptionChunks,
  sessionId,
  eventId,
  patientId,
  language,
  isConnected,
  setMergedTranscript // <-- add this prop
}) => {
  const isSendingDataRef = useRef(false);

  // Function to concatenate WAV audio chunks
  const concatenateWavChunks = (chunks) => {
    if (!chunks || chunks.length === 0) return null;
    const headerSize = 44;
    let totalLength = chunks.reduce((acc, chunk, index) => {
      return acc + (index === 0 ? chunk.byteLength : chunk.byteLength - headerSize);
    }, 0);
    const result = new Uint8Array(totalLength);
    let offset = 0;
    // Copy the first chunk entirely (including header)
    const firstChunk = new Uint8Array(chunks[0]);
    result.set(firstChunk, offset);
    offset += firstChunk.length;
    // For the rest of the chunks, skip the header
    for (let i = 1; i < chunks.length; i++) {
      const chunk = new Uint8Array(chunks[i]);
      result.set(chunk.slice(headerSize), offset);
      offset += chunk.length - headerSize;
    }
    // Update the data size in the WAV header
    const dataSize = totalLength - headerSize;
    const dataView = new DataView(result.buffer);
    dataView.setUint32(4, totalLength - 8, true);
    dataView.setUint32(40, dataSize, true);
    return result.buffer;
  };

  // Function to estimate duration of WAV audio chunk
  const estimateAudioDuration = (wavBuffer) => {
    try {
      const view = new DataView(wavBuffer);
      const sampleRate = view.getUint32(24, true);
      const numChannels = view.getUint16(22, true);
      const bitsPerSample = view.getUint16(34, true);
      const dataLength = view.getUint32(40, true);
      const bytesPerSample = bitsPerSample / 8;
      const bytesPerFrame = bytesPerSample * numChannels;
      const numFrames = dataLength / bytesPerFrame;
      const durationSec = numFrames / sampleRate;
      return durationSec;
    } catch (error) {
      console.error('Error estimating audio duration:', error);
      return 0;
    }
  };

  // Helper: Get initials from a name
  const getInitials = (name) => {
    if (!name) return '';
    const parts = name.split(' ');
    if (parts.length === 1) return parts[0][0]?.toUpperCase() || '';
    return (parts[0][0] + parts[parts.length - 1][0]).toUpperCase();
  };

  // Helper: Get role and initials for a mic (by deviceId)
  const getRoleAndInitials = (deviceId) => {
    const mic = selectedMics?.find(m => String(m.device) === String(deviceId) || String(m.deviceId) === String(deviceId));
    const role = mic?.role || 'Unknown';
    const name = mic?.name || role;
    return { role, initials: getInitials(name) };
  };

  // Helper: Deduplicate transcript output, now with role and strong fuzzy/semantic matching
  const lastTranscriptRef = useRef({ text: '', role: '' });
  function stringSimilarity(a, b) {
    // Jaccard similarity over word sets (simple, fast, robust to order)
    const setA = new Set(a.toLowerCase().split(/\s+/));
    const setB = new Set(b.toLowerCase().split(/\s+/));
    const intersection = new Set([...setA].filter(x => setB.has(x)));
    const union = new Set([...setA, ...setB]);
    return union.size === 0 ? 0 : intersection.size / union.size;
  }
  const addTranscriptIfUnique = (text, deviceId) => {
    const cleaned = text.trim();
    const { role, initials } = getRoleAndInitials(deviceId);
    const last = lastTranscriptRef.current.text;
    let isDuplicate = false;
    if (cleaned && last) {
      if (
        cleaned === last ||
        last.endsWith(cleaned) ||
        cleaned.endsWith(last)
      ) {
        isDuplicate = true;
      } else {
        // Fuzzy: check if last 5+ words of last match first 5+ words of cleaned
        const lastWords = last.split(/\s+/);
        const cleanedWords = cleaned.split(/\s+/);
        for (let n = 7; n >= 4; n--) {
          if (
            lastWords.length >= n &&
            cleanedWords.length >= n &&
            lastWords.slice(-n).join(' ') === cleanedWords.slice(0, n).join(' ')
          ) {
            isDuplicate = true;
            break;
          }
        }
        // Stronger: Jaccard similarity over last 10 words and first 10 words
        if (!isDuplicate) {
          const lastTail = lastWords.slice(-10).join(' ');
          const cleanedHead = cleanedWords.slice(0, 10).join(' ');
          const sim = stringSimilarity(lastTail, cleanedHead);
          if (sim > 0.8) {
            isDuplicate = true;
            console.log('[TranscriptDebug] Jaccard deduplication triggered, sim:', sim, 'lastTail:', lastTail, 'cleanedHead:', cleanedHead);
          }
        }
      }
    }
    if (cleaned && !isDuplicate) {
      const timestamp = new Date().toISOString();
      // Logging deduplication decision
      console.log('[TranscriptDebug] Transcript accepted:', cleaned, 'role:', role, 'deviceId:', deviceId);
      // ROUTEMAP log
      console.log(`[ROUTEMAP] Step 5: Transcription received for mic ${deviceId} (${role}, ${initials}): ${cleaned}`);
      setTranscriptionChunks(prev => {
        // Get the last chunk from the same speaker if it exists and is recent
        const lastChunk = prev.length > 0 ? prev[prev.length - 1] : null;
        const isRecentChunk = lastChunk && 
          lastChunk.role === role && 
          Date.now() - new Date(lastChunk.timestamp).getTime() < 5000;

        let newChunks;
        if (isRecentChunk) {
          // Merge with the last chunk, trimming overlap
          const mergedText = trimAndMerge(lastChunk.text, cleaned);
          newChunks = [...prev.slice(0, -1)];
          newChunks.push({
            timestamp: lastChunk.timestamp,
            text: mergedText,
            role,
            initials
          });
        } else {
          // Add as new chunk
          newChunks = [...prev, { timestamp, text: cleaned, role, initials }];
        }

        // Get the latest chunk after update
        const updatedChunk = newChunks[newChunks.length - 1];
        
        // Log using exactly what's shown in the UI
        transcriptLogger.logTranscript({
          sessionId,
          encounterId: eventId,
          rawText: text, // Original text from Azure STT
          formattedText: updatedChunk.text,
          speakerRole: role,
          deviceId,
          language,
          timestamp: updatedChunk.timestamp
        });

        return newChunks;
      });

      lastTranscriptRef.current = { text: cleaned, role };
    } else if (isDuplicate) {
      console.log('[TranscriptDebug] Transcript deduplicated/skipped:', cleaned, 'role:', role, 'deviceId:', deviceId);
    }
  };

  // Helper: Resample WAV buffer to 16kHz PCM mono
  const resampleWavTo16kHz = async (wavBuffer) => {
    // Parse WAV header
    const view = new DataView(wavBuffer);
    const numChannels = view.getUint16(22, true);
    const sampleRate = view.getUint32(24, true);
    const bitsPerSample = view.getUint16(34, true);
    if (sampleRate === 16000 && numChannels === 1 && bitsPerSample === 16) {
      // Already correct format
      return wavBuffer;
    }
    // Decode WAV to PCM samples
    const audioCtx = new (window.OfflineAudioContext || window.webkitOfflineAudioContext)(numChannels, view.getUint32(40, true) / (bitsPerSample / 8) / numChannels, sampleRate);
    const audioBuffer = await audioCtx.decodeAudioData(wavBuffer.slice(0));
    // Resample to 16kHz mono
    const offlineCtx = new (window.OfflineAudioContext || window.webkitOfflineAudioContext)(1, Math.ceil(audioBuffer.duration * 16000), 16000);
    const src = offlineCtx.createBufferSource();
    // Mixdown to mono if needed
    let monoBuffer;
    if (audioBuffer.numberOfChannels > 1) {
      monoBuffer = offlineCtx.createBuffer(1, audioBuffer.length, audioBuffer.sampleRate);
      const channelData = monoBuffer.getChannelData(0);
      for (let c = 0; c < audioBuffer.numberOfChannels; c++) {
        const data = audioBuffer.getChannelData(c);
        for (let i = 0; i < data.length; i++) {
          channelData[i] = (channelData[i] || 0) + data[i] / audioBuffer.numberOfChannels;
        }
      }
    } else {
      monoBuffer = audioBuffer;
    }
    src.buffer = monoBuffer;
    src.connect(offlineCtx.destination);
    src.start(0);
    const renderedBuffer = await offlineCtx.startRendering();
    // Convert to 16-bit PCM WAV
    const samples = renderedBuffer.getChannelData(0);
    const wavBytes = 44 + samples.length * 2;
    const out = new ArrayBuffer(wavBytes);
    const outView = new DataView(out);
    // WAV header
    function writeString(view, offset, str) {
      for (let i = 0; i < str.length; i++) {
        view.setUint8(offset + i, str.charCodeAt(i));
      }
    }
    writeString(outView, 0, 'RIFF');
    outView.setUint32(4, 36 + samples.length * 2, true);
    writeString(outView, 8, 'WAVE');
    writeString(outView, 12, 'fmt ');
    outView.setUint32(16, 16, true);
    outView.setUint16(20, 1, true); // PCM
    outView.setUint16(22, 1, true); // Mono
    outView.setUint32(24, 16000, true);
    outView.setUint32(28, 16000 * 2, true); // byte rate
    outView.setUint16(32, 2, true); // block align
    outView.setUint16(34, 16, true); // bits per sample
    writeString(outView, 36, 'data');
    outView.setUint32(40, samples.length * 2, true);
    // PCM samples
    let offset = 44;
    for (let i = 0; i < samples.length; i++, offset += 2) {
      let s = Math.max(-1, Math.min(1, samples[i]));
      outView.setInt16(offset, s < 0 ? s * 0x8000 : s * 0x7FFF, true);
    }
    return out;
  };

  // Send 10s audio buffer to Azure Speech Service
  const sendBufferToAzure = async (audioBuffer, deviceId) => {
    const duration = estimateAudioDuration(audioBuffer);
    if (!audioBuffer || duration < 1.5) {
      console.warn('[Azure Speech] Skipping send: buffer too short (', duration, 's)');
      return;
    }
    isSendingDataRef.current = true;
    const azureStart = Date.now();
    try {
      // Always use the latest language from the dropdown if available, fallback to prop or 'ml-IN'
      let lang = 'ml-IN';
      const select = document.getElementById('language-select');
      if (select && select.value) {
        lang = select.value;
      } else if (typeof language === 'string' && language.length > 0) {
        lang = language;
      }
      // If the dropdown value is a language code, use as-is. If it's a name, map to code.
      const langMap = {
        'english': 'en-US',
        'malayalam': 'ml-IN',
        'tamil':' ta-IN',
        'hindi': 'hi-IN',
      };
      if (lang.length === 2 || lang.length === 5) {
        // Already a code like 'en-US', 'ml-IN', etc.
      } else if (langMap[lang.toLowerCase()]) {
        lang = langMap[lang.toLowerCase()];
      }
      // --- Resample to 16kHz PCM mono before sending ---
      const wav16k = await resampleWavTo16kHz(audioBuffer);
      // PATCH: Use dictation endpoint for single-mic, conversation for multi-mic
      const endpointType = (selectedMics && selectedMics.length === 1) ? 'dictation' : 'conversation';
      const url = `https://${SPEECH_REGION}.stt.speech.microsoft.com/speech/recognition/${endpointType}/cognitiveservices/v1?language=${encodeURIComponent(lang)}`;
      // Add log for Azure request
      console.log('[TranscriptDebug] Sending to Azure:', endpointType, 'lang:', lang, 'deviceId:', deviceId, 'duration:', duration, 'url:', url);
      const response = await fetch(url, {
        method: 'POST',
        headers:
         {
          'Ocp-Apim-Subscription-Key': SPEECH_KEY,
          'Content-Type': 'audio/wav; codecs=audio/pcm; samplerate=16000',
        },
        body: wav16k
      });
      if (!response.ok) {
        throw new Error(`Azure Speech Service error: ${response.status}`);
      }
      const data = await response.json();
      // Get the raw text exactly as returned by Azure
      const rawTranscript = data.DisplayText || data.NBest?.[0]?.Display || '';
      const azureElapsed = Date.now() - azureStart;
      if (!rawTranscript) {
        console.warn('[Azure Speech] No transcript returned. deviceId:', deviceId, 'response:', data, 'bufferDuration:', duration.toFixed(2), 's');
      }
      if (azureElapsed > 3000) {
        console.warn('[Azure Speech] Azure response time:', azureElapsed, 'ms for deviceId:', deviceId);
      }
      if (rawTranscript) {
        // Log raw transcript from Azure STT
        const { role } = getRoleAndInitials(deviceId);
        transcriptLogger.logTranscript({
          sessionId,
          encounterId: eventId,
          rawText: rawTranscript, // Exactly what Azure returned
          formattedText: rawTranscript, // This will be formatted by the useEffect hook that processes transcriptionChunks
          speakerRole: role,
          deviceId,
          language
        });
        addTranscriptIfUnique(rawTranscript, deviceId);
      }
    } catch (error) {
      console.error('Azure Speech Service request failed:', error);
      setTranscriptionChunks(prev => [...prev, `Azure error: ${error.message}`]);
    }
    isSendingDataRef.current = false;
  };

  // --- Sliding window buffer for 1.0s overlap, 10s window ---
  const windowRef = useRef({
    buffer: [], // Array of {chunk, duration}
    totalDuration: 0,
    windowSize: 10, // seconds
    overlap: 1.0, // seconds (reduced from 1.5)
    lastSentEnd: 0, // seconds
    lastProcessedChunkIndex: -1
  });

  // --- Post-processing: merge, clean, punctuate transcript chunks, and remove near-duplicates ---
  useEffect(() => {
    if (!transcriptionChunks || transcriptionChunks.length === 0) {
      if (setMergedTranscript) setMergedTranscript("");
      return;
    }
    // Remove timestamps, get just the text (only for string chunks)
    const texts = transcriptionChunks.map(chunk => {
      if (typeof chunk === 'string') {
        const idx = chunk.indexOf(": ");
        return idx !== -1 ? chunk.slice(idx + 2) : chunk;
      } else if (chunk && typeof chunk.text === 'string') {
        return chunk.text;
      }
      return '';
    });
    // Remove near-duplicates (Jaccard > 0.8 with previous)
    const deduped = [];
    for (let i = 0; i < texts.length; i++) {
      if (i === 0) {
        deduped.push(texts[i]);
        continue;
      }
      const sim = stringSimilarity(texts[i], deduped[deduped.length - 1]);
      if (sim < 0.8) {
        deduped.push(texts[i]);
      } else {
        console.log('[TranscriptDebug] Post-process deduplication: removed block', i, 'sim:', sim);
      }
    }
    // Merge at sentence boundaries and for likely continuations
    let merged = "";
    for (let i = 0; i < deduped.length; i++) {
      const prev = merged.trim();
      const curr = deduped[i].trim();
      if (!prev) {
        merged += curr;
        continue;
      }
      // Use enhanced trimAndMerge for post-processing as well
      merged = trimAndMerge(prev, curr);
    }
    merged = merged.replace(/(\b\w+(?:\s+\w+){4,}\b)(?:\s+\1)+/gi, "$1").trim();
    if (setMergedTranscript) setMergedTranscript(merged);
  }, [transcriptionChunks, setMergedTranscript]);

  // --- Post-processing: intelligent chunking for timestamped blocks, per-speaker, accumulate until speaker changes or max block size, allow merging of small initial blocks ---
  useEffect(() => {
    if (!transcriptionChunks || transcriptionChunks.length === 0) {
      if (setMergedTranscript) setMergedTranscript([]);
      return;
    }
    // Parse timestamp, text, role, initials
    const parsed = transcriptionChunks.map(chunk => {
      if (typeof chunk === 'string') {
        const idx = chunk.indexOf(': ');
        return idx !== -1
          ? { timestamp: chunk.slice(0, idx), text: chunk.slice(idx + 2).trim(), role: 'Unknown', initials: '?' }
          : { timestamp: '', text: chunk.trim(), role: 'Unknown', initials: '?' };
      } else if (chunk && typeof chunk.text === 'string') {
        return chunk;
      }
      return { timestamp: '', text: '', role: 'Unknown', initials: '?' };
    });
    // Accumulate blocks per speaker, only break on speaker change or max block size
    const maxWords = 400;
    const minBlockWords = 5;
    let blocks = [];
    let currentBlock = null;
    for (let i = 0; i < parsed.length; i++) {
      const { timestamp, text, role, initials } = parsed[i];
      if (!role || !text) continue;
      const wordCount = text.split(/\s+/).length;
      if (!currentBlock) {
        currentBlock = { timestamp, text, role, initials, wordCount };
        continue;
      }
      // If speaker changes or block would be too large, push and start new
      if (role !== currentBlock.role || currentBlock.wordCount + wordCount > maxWords) {
        if (currentBlock.wordCount >= minBlockWords || blocks.length === 0) {
          blocks.push({ ...currentBlock });
        } else if (blocks.length > 0) {
          // Merge small block into previous
          blocks[blocks.length - 1].text += ' ' + currentBlock.text;
          blocks[blocks.length - 1].wordCount += currentBlock.wordCount;
        }
        currentBlock = { timestamp, text, role, initials, wordCount };
      } else {
        currentBlock.text += ' ' + text;
        currentBlock.wordCount += wordCount;
      }
    }
    if (currentBlock) {
      if (currentBlock.wordCount >= minBlockWords || blocks.length === 0) {
        blocks.push({ ...currentBlock });
      } else if (blocks.length > 0) {
        blocks[blocks.length - 1].text += ' ' + currentBlock.text;
        blocks[blocks.length - 1].wordCount += currentBlock.wordCount;
      }
    }
    // Merge small initial blocks for each speaker if possible
    let mergedBlocks = [];
    let j = 0;
    while (j < blocks.length) {
      let block = blocks[j];
      if (
        block.wordCount < minBlockWords &&
        j + 1 < blocks.length &&
        block.role === blocks[j + 1].role &&
        (j === 0 || blocks[j - 1].role !== block.role)
      ) {
        // Merge with next block of same speaker
        blocks[j + 1].text = block.text + ' ' + blocks[j + 1].text;
        blocks[j + 1].wordCount += block.wordCount;
        j++;
        continue;
      }
      mergedBlocks.push(block);
      j++;
    }
    if (setMergedTranscript) setMergedTranscript(mergedBlocks);
  }, [transcriptionChunks, setMergedTranscript]);

  // --- Per-mic sliding window buffers ---
  const micWindowsRef = useRef({}); // { [deviceId]: { buffer: [], totalDuration, lastProcessedChunkIndex } }

  // --- Replace direct sendBufferToAzure calls with queueing ---
  const queueBufferForAzure = (audioBuffer, deviceId) => {
    // Add log for queueing
    console.log('[TranscriptDebug] Queueing buffer for Azure, deviceId:', deviceId, 'bufferBytes:', audioBuffer?.byteLength);
    azureRequestQueue.push({
      buffer: audioBuffer,
      callback: (buf) => sendBufferToAzure(buf, deviceId)
    });
    processAzureQueue();
  };

  // --- Main effect: process all new chunks per mic, send sliding window with overlap ---
  useEffect(() => {
    if (!audioChunks || !Array.isArray(selectedMics) || selectedMics.length === 0 || !isConnected) return;
    // --- Optimization: Check if there are any new chunks to process before proceeding ---
    let hasNewChunks = false;
    const isPerMic = audioChunks.length > 0 && typeof audioChunks[0] === 'object' && audioChunks[0].deviceId !== undefined;
    if (isPerMic) {
      console.log('selectedMics at start of effect:', selectedMics);
      for (const mic of selectedMics) {
        console.log('[ExternalAudioService] [PerMic] Loop mic:', mic);
        const deviceId = mic.device || mic.deviceId;
        console.log('[ExternalAudioService] [PerMic] Checking deviceId:', deviceId);
        console.log(!deviceId ? 'No deviceId found for mic' : 'DeviceId found:', deviceId);
        
        if (!deviceId) continue;
        if (!micWindowsRef.current[deviceId]) {
          micWindowsRef.current[deviceId] = {
            buffer: [],
            totalDuration: 0,
            lastProcessedChunkIndex: -1
          };
        }
        console.log('after checking device id:', deviceId, 'micWindowsRef:', micWindowsRef.current[deviceId]);
        
        const micChunks = audioChunks.filter(c => {
          if (typeof c !== 'object' || c.deviceId === undefined) return false;
          return String(c.deviceId) === String(deviceId);
        });
        let startIdx = micWindowsRef.current[deviceId].lastProcessedChunkIndex + 1;
        console.log('[ExternalAudioService] [PerMic] micChunks.length:', micChunks.length, 'lastProcessedChunkIndex:', micWindowsRef.current[deviceId].lastProcessedChunkIndex, 'startIdx:', startIdx);
        if (micChunks.length > 0 && startIdx < micChunks.length) {
          hasNewChunks = true;
          break;
        }
      }
    } else {
      let startIdx = windowRef.current.lastProcessedChunkIndex + 1;
      if (audioChunks.length > 0 && startIdx < audioChunks.length) {
        hasNewChunks = true;
      }
      console.log('[ExternalAudioService] [Fallback] startIdx:', startIdx, 'hasNewChunks:', hasNewChunks);
    }
    if (!hasNewChunks) {
      // No new audio chunks to process, skip effect
      return;
    }
    // --- Remove deviceId and chunk debug logs ---
    // --- Add targeted logs for transcript latency/missing transcript issues ---
    const startProcess = Date.now();
    let processedChunks = 0;
    let sentToAzure = 0;
    let azureResponses = 0;
    let missingTranscripts = 0;
    // --- Always prefer per-mic processing if possible ---
    if (isPerMic) {
      selectedMics.forEach(mic => {
        const deviceId = mic.device || mic.deviceId;
        if (!deviceId) return;
        if (!micWindowsRef.current[deviceId]) {
          micWindowsRef.current[deviceId] = {
            buffer: [],
            totalDuration: 0,
            lastProcessedChunkIndex: -1
          };
        }
        const micChunks = audioChunks.filter(c => {
          if (typeof c !== 'object' || c.deviceId === undefined) {
            // Only log once per session for missing deviceId
            if (!window._loggedMissingDeviceId) {
              console.warn('[ExternalAudioService] Skipping chunk missing deviceId:', c);
              window._loggedMissingDeviceId = true;
            }
            return false;
          }
          return String(c.deviceId) === String(deviceId);
        });
        let startIdx = micWindowsRef.current[deviceId].lastProcessedChunkIndex + 1;
        for (let i = startIdx; i < micChunks.length; i++) {
          const chunk = micChunks[i].chunk;
          if (!chunk) continue;
          processedChunks++;
          const duration = estimateAudioDuration(chunk);
          micWindowsRef.current[deviceId].buffer.push({ chunk, duration });
          micWindowsRef.current[deviceId].totalDuration += duration;
          micWindowsRef.current[deviceId].lastProcessedChunkIndex = i;
          if (micWindowsRef.current[deviceId].totalDuration >= 10) {
            const bufferToSend = concatenateWavChunks(micWindowsRef.current[deviceId].buffer.map(b => b.chunk));
            console.log('[TranscriptDebug] [PerMic] Sending buffer for deviceId:', deviceId, 'duration:', micWindowsRef.current[deviceId].totalDuration.toFixed(2), 's, chunks:', micWindowsRef.current[deviceId].buffer.length);
            sentToAzure++;
            queueBufferForAzure(bufferToSend, deviceId);
            let slideDuration = 10 - 1.5; // was 10 - 0.7
            let removedDuration = 0;
            while (micWindowsRef.current[deviceId].buffer.length && removedDuration < slideDuration) {
              const removed = micWindowsRef.current[deviceId].buffer.shift();
              removedDuration += removed.duration;
              micWindowsRef.current[deviceId].totalDuration -= removed.duration;
            }
            // Logging buffer slide
            console.log('[TranscriptDebug] [PerMic] Buffer slid by', slideDuration.toFixed(2), 's, remaining duration:', micWindowsRef.current[deviceId].totalDuration.toFixed(2), 's');
          }
        }
      });
    } else {
      if (selectedMics.length > 1) {
        if (!window._loggedFallbackMultiMic) {
          console.warn('[ExternalAudioService] Fallback logic triggered with multiple mics. This should not happen unless there is an upstream error.');
          window._loggedFallbackMultiMic = true;
        }
      }
      let startIdx = windowRef.current.lastProcessedChunkIndex + 1;
      for (let i = startIdx; i < audioChunks.length; i++) {
        let chunkObj = audioChunks[i];
        if (!chunkObj) continue;
        let chunkBuf = (typeof chunkObj === 'object' && chunkObj.chunk) ? chunkObj.chunk : chunkObj;
        if (!chunkBuf) continue;
        processedChunks++;
        const duration = estimateAudioDuration(chunkBuf);
        addChunkToWindow(chunkBuf, duration);
        windowRef.current.lastProcessedChunkIndex = i;
        if (windowRef.current.totalDuration >= windowRef.current.windowSize) {
          const bufferToSend = getWindowBuffer();
          let deviceId;
          if (selectedMics && selectedMics.length === 1) {
            deviceId = selectedMics[0].device || selectedMics[0].deviceId;
          } else {
            deviceId = 'Unknown';
          }
          sentToAzure++;
          queueBufferForAzure(bufferToSend, deviceId);
          let slideDuration = windowRef.current.windowSize - windowRef.current.overlap; // now 10 - 1.5
          let removedDuration = 0;
          while (windowRef.current.buffer.length && removedDuration < slideDuration) {
            const removed = windowRef.current.buffer.shift();
            removedDuration += removed.duration;
            windowRef.current.totalDuration -= removed.duration;
          }
          // Logging buffer slide
          console.log('[TranscriptDebug] [Fallback] Buffer slid by', slideDuration.toFixed(2), 's, remaining duration:', windowRef.current.totalDuration.toFixed(2), 's');
        }
      }
    }
    // --- Only log warnings if actual processing was attempted ---
    setTimeout(() => {
      const elapsed = Date.now() - startProcess;
      if (processedChunks === 0) {
        console.warn('[ExternalAudioService] No audio chunks processed in this cycle.');
      }
      if (sentToAzure === 0 && processedChunks > 0) {
        console.warn('[ExternalAudioService] No buffers sent to Azure despite processing chunks.');
      }
      if (elapsed > 2000) {
        console.warn('[ExternalAudioService] Transcript processing took', elapsed, 'ms for', processedChunks, 'chunks.');
      }
    }, 0);
  }, [audioChunks, isConnected, sessionId, eventId, patientId, language, selectedMics]);

  // --- Timer: flush any partial window for each mic after 3s of inactivity, but keep last 1.5s ---
  useEffect(() => {
    let flushTimer = null;
    if (isConnected) {
      flushTimer = setInterval(() => {
        if (Array.isArray(selectedMics)) {
          selectedMics.forEach(mic => {
            const deviceId = mic.device || mic.deviceId;
            const win = micWindowsRef.current[deviceId];
            if (win && win.buffer.length > 0 && win.totalDuration > 0) {
              const bufferToSend = concatenateWavChunks(win.buffer.map(b => b.chunk));
              console.log('[TranscriptDebug] [TimerFlush][PerMic] Flushing buffer for deviceId:', deviceId, 'duration:', win.totalDuration.toFixed(2), 's, chunks:', win.buffer.length);
              queueBufferForAzure(bufferToSend, deviceId);
              // Retain last 1.5s in buffer
              let retained = [];
              let retainedDuration = 0;
              for (let i = win.buffer.length - 1; i >= 0; i--) {
                if (retainedDuration >= 1.5) break;
                retained.unshift(win.buffer[i]);
                retainedDuration += win.buffer[i].duration;
              }
              win.buffer = retained;
              win.totalDuration = retainedDuration;
              console.log('[TranscriptDebug] [TimerFlush][PerMic] Retained', retained.length, 'chunks, duration:', retainedDuration.toFixed(2), 's');
            }
          });
        }
        // Fallback: old logic
        if (windowRef.current.buffer.length > 0 && windowRef.current.totalDuration > 0) {
          const bufferToSend = getWindowBuffer();
          let deviceId;
          if (selectedMics && selectedMics.length === 1) {
            deviceId = selectedMics[0].device || selectedMics[0].deviceId;
          } else if (selectedMics && selectedMics.length > 1) {
            deviceId = selectedMics[0].device || selectedMics[0].deviceId;
            console.warn('[ExternalAudioService] Timer flush: Fallback logic used with multiple mics. Diarization not possible, assigning all to first mic:', deviceId);
          } else {
            deviceId = 'Unknown';
          }
          console.log('[TranscriptDebug] [TimerFlush][Fallback] Flushing buffer, duration:', windowRef.current.totalDuration.toFixed(2), 's, chunks:', windowRef.current.buffer.length);
          queueBufferForAzure(bufferToSend, deviceId);
          // Retain last 1.5s in buffer
          let retained = [];
          let retainedDuration = 0;
          for (let i = windowRef.current.buffer.length - 1; i >= 0; i--) {
            if (retainedDuration >= 1.5) break;
            retained.unshift(windowRef.current.buffer[i]);
            retainedDuration += windowRef.current.buffer[i].duration;
          }
          windowRef.current.buffer = retained;
          windowRef.current.totalDuration = retainedDuration;
          console.log('[TranscriptDebug] [TimerFlush][Fallback] Retained', retained.length, 'chunks, duration:', retainedDuration.toFixed(2), 's');
        }
      }, 3000);
    }
    return () => flushTimer && clearInterval(flushTimer);
  }, [isConnected, selectedMics]);

  // --- Fallback helpers for single-stream sliding window ---
  const addChunkToWindow = (chunk, duration) => {
    windowRef.current.buffer.push({ chunk, duration });
    windowRef.current.totalDuration += duration;
  };
  const getWindowBuffer = () => {
    return concatenateWavChunks(windowRef.current.buffer.map(b => b.chunk));
  };

  // Helper to trim overlap when merging (now available globally in this file)
  function trimAndMerge(prev, curr) {
    const prevWords = prev.split(/\s+/);
    const currWords = curr.split(/\s+/);
    // Try exact match for up to 10 words
    for (let n = Math.min(10, prevWords.length, currWords.length); n >= 2; n--) {
      if (prevWords.slice(-n).join(' ') === currWords.slice(0, n).join(' ')) {
        return prev + ' ' + currWords.slice(n).join(' ');
      }
      // Fuzzy: Jaccard similarity for n-grams
      const prevGram = prevWords.slice(-n).join(' ');
      const currGram = currWords.slice(0, n).join(' ');
      const setA = new Set(prevGram.toLowerCase().split(/\s+/));
      const setB = new Set(currGram.toLowerCase().split(/\s+/));
      const intersection = new Set([...setA].filter(x => setB.has(x)));
      const union = new Set([...setA, ...setB]);
      const sim = union.size === 0 ? 0 : intersection.size / union.size;
      if (sim > 0.7 && n >= 2) {
        return prev + ' ' + currWords.slice(n).join(' ');
      }
    }
    // If prev ends without punctuation and curr starts lowercase, merge with space
    if (prev && curr && !/[.!?]$/.test(prev.trim()) && /^[a-z]/.test(curr.trim())) {
      return prev.trim() + ' ' + curr.trim();
    }
    // Otherwise, merge with a period if needed
    if (prev && curr && !/[.!?]$/.test(prev.trim()) && /^[A-Z]/.test(curr.trim())) {
      return prev.trim() + '. ' + curr.trim();
    }
    return prev + ' ' + curr;
  }

  // --- Log before reaching Conversation Panel ---
  useEffect(() => {
    if (transcriptionChunks && transcriptionChunks.length > 0) {
      const last = transcriptionChunks[transcriptionChunks.length - 1];
      if (last && last.text && last.initials) {
        console.log(`[ROUTEMAP] Step 5.5: Preparing to load transcript into ConversationPanel for ${last.role} (${last.initials}): ${last.text}`);
      } else if (typeof last === 'string') {
        console.log(`[ROUTEMAP] Step 5.5: Preparing to load transcript into ConversationPanel: ${last}`);
      }
    }
  }, [transcriptionChunks]);

  return (
    <div className="external-audio-service">
      {/* No UI required */}
    </div>
  );
};

export default ExternalAudioService;