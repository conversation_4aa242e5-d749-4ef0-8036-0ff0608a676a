import React, { useState, useRef, useEffect } from 'react';
import { FaPlay, FaPause, FaStop, FaVolumeUp, FaVolumeMute, FaStepBackward, FaStepForward, FaRedo, FaDownload, FaChevronUp, FaChevronDown } from 'react-icons/fa';
import './EnhancedAudioPlayer.css';
import { isAdmin } from '../utils/roleUtils';

const EnhancedAudioPlayer = ({
  audioFiles,
  isVisible,
  onClose,
  sessionId,
  eventId,
  API_BASE_URL,
  role,
  selectedMics = [] // Add selectedMics prop to get mic names
}) => {
  const [currentFileIndex, setCurrentFileIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(0.8);
  const [isMuted, setIsMuted] = useState(false);
  const [playbackRate, setPlaybackRate] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [waveformData, setWaveformData] = useState([]);
  const [isExpanded, setIsExpanded] = useState(true); // New state for accordion functionality

  const audioRef = useRef(null);
  const canvasRef = useRef(null);
  const animationRef = useRef(null);
  console.log('[EnhancedAudioPlayer] Rendering with props:', { audioFiles, isVisible, sessionId, eventId, API_BASE_URL });
  console.log('[EnhancedAudioPlayer]', API_BASE_URL, 'API BASE IN ENHANCEDPLAYED');

  // Function to get mic name from filename
  const getMicNameFromFilename = (filename) => {
    if (!filename || !selectedMics || selectedMics.length === 0) {
      return filename?.split('/').pop().replace('.wav', '') || 'Unknown';
    }

    // Extract mic device ID from filename (e.g., "session_123_timestamp_mic1.wav" -> "1")
    const micMatch = filename.match(/mic(\w+)\.wav$/);
    if (micMatch) {
      const deviceId = micMatch[1];
      // Find the corresponding mic name from selectedMics
      const mic = selectedMics.find(m => m.device === deviceId);
      if (mic && mic.micname) {
        return mic.micname;
      }
    }

    // Fallback to filename without extension
    return filename.split('/').pop().replace('.wav', '');
  };

  // Get current audio file
  const currentFile = audioFiles && audioFiles.length > 0 ?
    (Array.isArray(audioFiles) ? audioFiles[currentFileIndex] : audioFiles) : null;

  // Initialize audio when file changes (fetch from server as blob and play)
  useEffect(() => {
    console.log('[EnhancedAudioPlayer] Audio loading useEffect triggered. Current file:', currentFile);
    if (!currentFile) {
      console.log('[EnhancedAudioPlayer] No currentFile, skipping audio load.');
      return;
    }

    let isMounted = true;
    const fileName = typeof currentFile === 'string' ? currentFile : currentFile.name;
    const audioUrl = `${API_BASE_URL}/api/recordings_web/${encodeURIComponent(fileName)}`;
    console.log(`[EnhancedAudioPlayer] Constructed audio URL:`, audioUrl);
    console.log(`[EnhancedAudioPlayer] Preparing to fetch audio:`, audioUrl);
    setIsLoading(true);

    // Fetch the audio file as a blob from the server
    console.log('[EnhancedAudioPlayer] Fetching audio from URL...');
    fetch(audioUrl)
      .then(async (response) => {
        console.log('[EnhancedAudioPlayer] Initial fetch response received.');
        console.log(`[EnhancedAudioPlayer] Response Status: ${response.status} ${response.statusText}`);
        console.log(`[EnhancedAudioPlayer] Response Content-Type: ${response.headers.get('Content-Type')}`);
        if (!response.ok) {
          console.error('[EnhancedAudioPlayer] Failed to fetch audio file:', response.status, response.statusText);
          throw new Error('Failed to fetch audio file');
        }
        const blob = await response.blob();
        console.log('[EnhancedAudioPlayer] Response converted to blob. Size:', blob.size, 'Type:', blob.type);
        if (!isMounted) {
          console.log('[EnhancedAudioPlayer] Component unmounted before audio blob loaded.');
          return;
        }
        const blobUrl = URL.createObjectURL(blob);
        console.log('[EnhancedAudioPlayer] Created blob URL:', blobUrl);
        const audio = new Audio();
        audioRef.current = audio;
        audio.src = blobUrl;
        audio.volume = volume;
        audio.playbackRate = playbackRate;
        audio.muted = isMuted;
        console.log('[EnhancedAudioPlayer] Audio object created and src set to blobUrl.');

        const handleLoadedMetadata = () => {
          console.log(`[EnhancedAudioPlayer] 'loadedmetadata' event fired. Duration: ${audio.duration}, ReadyState: ${audio.readyState}`);
          setDuration(audio.duration);
          setIsLoading(false);
          generateWaveform(audio);
        };

        const handleTimeUpdate = () => {
          // This log is very noisy, so it's commented out by default.
          // console.log(`[EnhancedAudioPlayer] 'timeupdate' event. CurrentTime: ${audio.currentTime}`);
          setCurrentTime(audio.currentTime);
        };

        const handleEnded = () => {
          console.log("[EnhancedAudioPlayer] 'ended' event fired.");
          setIsPlaying(false);
          setCurrentTime(0);
          // Auto-play next file if available
          if (Array.isArray(audioFiles) && currentFileIndex < audioFiles.length - 1) {
            setCurrentFileIndex(prev => prev + 1);
          }
        };

        const handleError = (e) => {
          console.error('[EnhancedAudioPlayer] Audio element error event fired. See details below.');
          if (audioRef.current?.error) {
            console.error(`[EnhancedAudioPlayer] > Error Code: ${audioRef.current.error.code}`);
            console.error(`[EnhancedAudioPlayer] > Error Message: ${audioRef.current.error.message}`);
          } else {
            console.error('[EnhancedAudioPlayer] > No specific error code/message available on audio element.', e);
          }
          setIsLoading(false);
        };

        audio.addEventListener('loadedmetadata', handleLoadedMetadata);
        audio.addEventListener('timeupdate', handleTimeUpdate);
        audio.addEventListener('ended', handleEnded);
        audio.addEventListener('error', handleError);

        console.log('[EnhancedAudioPlayer] Calling audio.load()');
        audio.load();

        // Cleanup
        return () => {
          console.log('[EnhancedAudioPlayer] Cleanup: removing audio event listeners and revoking blobUrl.');
          audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
          audio.removeEventListener('timeupdate', handleTimeUpdate);
          audio.removeEventListener('ended', handleEnded);
          audio.removeEventListener('error', handleError);
          audio.pause();
          URL.revokeObjectURL(blobUrl);
        };
      })
      .catch((err) => {
        setIsLoading(false);
        setDuration(0);
        setCurrentTime(0);
        setWaveformData([]);
        console.error('[EnhancedAudioPlayer] CATCH block: Audio fetch/play error:', err);
      });

    return () => {
      isMounted = false;
      if (audioRef.current) {
        console.log('[EnhancedAudioPlayer] Cleanup: pausing and clearing audio src.');
        audioRef.current.pause();
        audioRef.current.src = '';
      }
    };
  }, [currentFile, API_BASE_URL]);

  // Update audio properties when state changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = isMuted ? 0 : volume;
      audioRef.current.playbackRate = playbackRate;
    }
  }, [volume, isMuted, playbackRate]);

  // Generate simple waveform visualization
  const generateWaveform = async (audio) => {
    try {
      console.log('[EnhancedAudioPlayer] Generating waveform...');
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const response = await fetch(audio.src);
      const arrayBuffer = await response.arrayBuffer();
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
      console.log('[EnhancedAudioPlayer] Audio buffer decoded for waveform.');
      const channelData = audioBuffer.getChannelData(0);
      const samples = 200; // Number of waveform bars
      const blockSize = Math.floor(channelData.length / samples);
      const waveform = [];

      for (let i = 0; i < samples; i++) {
        let sum = 0;
        for (let j = 0; j < blockSize; j++) {
          sum += Math.abs(channelData[i * blockSize + j]);
        }
        waveform.push(sum / blockSize);
      }

      setWaveformData(waveform);
      console.log('[EnhancedAudioPlayer] Waveform data set.');
    } catch (error) {
      console.error('[EnhancedAudioPlayer] Error generating waveform:', error);
      // Generate dummy waveform
      setWaveformData(Array.from({ length: 200 }, () => Math.random() * 0.5));
    }
  };

  const handlePlayPause = () => {
    if (!audioRef.current) return;

    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      audioRef.current.play().then(() => {
        setIsPlaying(true);
      }).catch(error => {
        console.error('Error playing audio:', error);
      });
    }
  };

  const handleStop = () => {
    if (!audioRef.current) return;
    audioRef.current.pause();
    audioRef.current.currentTime = 0;
    setIsPlaying(false);
    setCurrentTime(0);
  };

  const handleSeek = (event) => {
    if (!audioRef.current || !duration) return;

    const rect = event.currentTarget.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const newTime = (clickX / rect.width) * duration;

    audioRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const handleSkip = (seconds) => {
    if (!audioRef.current) return;
    const newTime = Math.max(0, Math.min(duration, currentTime + seconds));
    audioRef.current.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const handleFileChange = (index) => {
    if (isPlaying) {
      audioRef.current?.pause();
      setIsPlaying(false);
    }
    setCurrentFileIndex(index);
    setCurrentTime(0);
  };

  const formatTime = (time) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleDownload = async () => {
    if (!currentFile) return;

    try {
      const fileName = typeof currentFile === 'string' ? currentFile : currentFile.name;
      const downloadUrl = `${API_BASE_URL}/api/download/${encodeURIComponent(fileName)}`;
      console.log(downloadUrl, 'downloadUrl');

      const response = await fetch(downloadUrl);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to download audio file:', error);
      alert('Failed to download audio file: ' + error.message);
    }
  };

  const handleDownloadAll = async () => {
    if (!sessionId) {
      alert('Session ID is required to download all audio files');
      return;
    }

    try {
      const url = `${API_BASE_URL}/api/download_all?session=${encodeURIComponent(sessionId)}${eventId ? `&event=${encodeURIComponent(eventId)}` : ''
        }`;

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = `session_${sessionId}${eventId ? '_' + eventId : ''}_audio.zip`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(downloadUrl);
    } catch (error) {
      console.error('Failed to download all audio files:', error);
      alert('Failed to download all audio files: ' + error.message);
    }
  };

  // --- Timeline slider logic ---
  // Show slider only if not recording and there is a loaded file
  const showTimeline = !isLoading && !isPlaying && duration > 0;

  // Handler for slider drag
  const handleSliderChange = (e) => {
    const newTime = (parseFloat(e.target.value) / 100) * duration;
    setCurrentTime(newTime);
    if (audioRef.current) {
      audioRef.current.currentTime = newTime;
    }
  };

  // Handler for slider drag end (if not playing, sets start point for next play)
  const handleSliderMouseUp = (e) => {
    if (!isPlaying && audioRef.current) {
      audioRef.current.currentTime = currentTime;
    }
  };

  if (!isVisible || !currentFile) {
    return null;
  }

  return (
    <div className="enhanced-audio-player">
      <div className="audio-player-header">
        <div className="player-title">
          <h3>🎵 Audio Player</h3>
          {Array.isArray(audioFiles) && audioFiles.length > 1 && (
            <span className="file-counter">
              {currentFileIndex + 1}/{audioFiles.length}
            </span>
          )}
        </div>
        <button
          className="expand-collapse-toggle"
          onClick={() => setIsExpanded(!isExpanded)}
          title={isExpanded ? "Collapse" : "Expand"}
        >
          {isExpanded ? <FaChevronUp /> : <FaChevronDown />}
        </button>
      </div>

      {/* Collapsible content */}
      <div className={`collapsible-content ${isExpanded ? 'expanded' : 'collapsed'}`}>
        {isExpanded && (
          <>
          {/* File selector for multiple files */}
          {Array.isArray(audioFiles) && audioFiles.length > 1 && (
            <div className="file-selector">
              {audioFiles.map((file, index) => {
                const fileName = typeof file === 'string' ? file : file.name;
                const displayName = getMicNameFromFilename(fileName);
                return (
                  <button
                    key={index}
                    className={`file-tab ${index === currentFileIndex ? 'active' : ''}`}
                    onClick={() => handleFileChange(index)}
                  >
                    {displayName}
                  </button>
                );
              })}
            </div>
          )}

      {/* Waveform visualization */}
      <div className="waveform-container" onClick={handleSeek}>
        <div className="waveform-bars">
          {waveformData.map((amplitude, index) => (
            <div
              key={index}
              className="waveform-bar"
              style={{
                height: `${Math.max(2, amplitude * 100)}%`,
                backgroundColor: (index / waveformData.length) <= (currentTime / duration)
                  ? '#007bff' : '#e0e0e0'
              }}
            />
          ))}
        </div>
        <div className="progress-indicator" style={{ left: `${(currentTime / duration) * 100}%` }} />
      </div>

      {/* Time display */}
      <div className="time-display">
        <span>{formatTime(currentTime)} / {formatTime(duration)}</span>
      </div>

      {/* Main controls */}
      <div className="main-controls">
        <button
          className="control-btn skip-btn"
          onClick={() => handleSkip(-10)}
          title="Back 10 seconds"
        >
          <FaStepBackward />
        </button>

        <button
          className="control-btn play-btn"
          onClick={handlePlayPause}
          disabled={isLoading}
          title={isPlaying ? "Pause" : "Play"}
        >
          {isLoading ? (
            <div className="loading-spinner" />
          ) : isPlaying ? (
            <FaPause />
          ) : (
            <FaPlay />
          )}
        </button>

        <button
          className="control-btn stop-btn"
          onClick={handleStop}
          title="Stop"
        >
          <FaStop />
        </button>

        <button
          className="control-btn skip-btn"
          onClick={() => handleSkip(10)}
          title="Forward 10 seconds"
        >
          <FaStepForward />
        </button>
      </div>

      {/* Secondary controls */}
      <div className="secondary-controls">
        <div className="volume-control">
          <button
            className="control-btn volume-btn"
            onClick={() => setIsMuted(!isMuted)}
            title={isMuted ? "Unmute" : "Mute"}
          >
            {isMuted ? <FaVolumeMute /> : <FaVolumeUp />}
          </button>
          <input
            type="range"
            min="0"
            max="1"
            step="0.01"
            value={isMuted ? 0 : volume}
            onChange={(e) => {
              setVolume(parseFloat(e.target.value));
              if (isMuted) setIsMuted(false);
            }}
            className="volume-slider"
          />
        </div>

        <div className="playback-rate">
          <label>Speed:</label>
          <select
            value={playbackRate}
            onChange={(e) => setPlaybackRate(parseFloat(e.target.value))}
            className="rate-selector"
          >
            <option value={0.5}>0.5x</option>
            <option value={0.75}>0.75x</option>
            <option value={1}>1x</option>
            <option value={1.25}>1.25x</option>
            <option value={1.5}>1.5x</option>
            <option value={2}>2x</option>
          </select>
        </div>

        {isAdmin(role) && (
          <>
            <button
              className="control-btn download-btn"
              onClick={handleDownload}
              title="Download current audio file"
            >
              <FaDownload />
            </button>

            {sessionId && Array.isArray(audioFiles) && audioFiles.length > 1 && (
              <button
                className="control-btn download-all-btn"
                onClick={handleDownloadAll}
                title="Download all audio files as ZIP"
                style={{ marginLeft: '8px' }}
              >
                <FaDownload />
                <span style={{ fontSize: '10px', marginLeft: '2px' }}>All</span>
              </button>
            )}
          </>
        )}

      </div>

      {/* Timeline slider: visible only when paused or stopped, not during active recording/playback */}
      {(!isPlaying && !isLoading && duration > 0) && (
        <div style={{ display: 'flex', alignItems: 'center', gap: 12, margin: '16px 0 8px 0' }}>
          <input
            type="text"
            value={formatTime(currentTime)}
            readOnly
            style={{ width: 48, textAlign: 'center', border: 'none', background: 'transparent', fontWeight: 500 }}
            tabIndex={-1}
          />
          <input
            type="range"
            min={0}
            max={100}
            step={0.1}
            value={duration ? (currentTime / duration) * 100 : 0}
            onChange={handleSliderChange}
            onMouseUp={handleSliderMouseUp}
            style={{ flex: 1, accentColor: '#007bff', height: 4 }}
            aria-label="Seek timeline"
          />
          <input
            type="text"
            value={formatTime(duration)}
            readOnly
            style={{ width: 48, textAlign: 'center', border: 'none', background: 'transparent', fontWeight: 500 }}
            tabIndex={-1}
          />
        </div>
      )}
          </>
        )}
      </div>
    </div>
  );
};

export default EnhancedAudioPlayer;
