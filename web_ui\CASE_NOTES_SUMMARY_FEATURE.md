# Case Notes Summary Feature

## Overview

This feature automatically generates AI-powered summaries of previous case notes when patient data is received from external APIs. The system checks for existing encounters with case notes and uses OpenAI to create comprehensive summaries that are stored in the `previous_case_notes_summary` column of the patients table.

## How It Works

### Automatic Summary Generation

1. **API Response Processing**: When patient data is received via `/api/storePatientData`
2. **Encounter Detection**: System checks if the patient has encounters with case notes
3. **Case Notes Extraction**: Extracts all case notes from encounters with their dates
4. **AI Summary Generation**: Uses OpenAI to generate a comprehensive medical summary
5. **Database Storage**: Saves the generated summary to the `previous_case_notes_summary` column

### Manual Summary Generation

A dedicated API endpoint allows manual triggering of case notes summary generation:

```
POST /api/generatePatientCaseNotesSummary
{
  "regNo": "PATIENT-REG-NUMBER"
}
```

## Database Schema

The feature uses the existing database structure:

- **patients table**: Contains `previous_case_notes_summary` TEXT column
- **encounters table**: Contains `case_notes` JSONB column with encounter data
- **Case notes format**: Array of objects with `head` and `text` properties

## API Endpoints

### 1. Store Patient Data (Enhanced)
- **Endpoint**: `POST /api/storePatientData`
- **Enhancement**: Now automatically generates case notes summary
- **Process**: Stores patient data → Checks encounters → Generates summary → Saves summary

### 2. Generate Case Notes Summary
- **Endpoint**: `POST /api/generatePatientCaseNotesSummary`
- **Purpose**: Manual summary generation for existing patients
- **Request Body**: `{ "regNo": "patient-registration-number" }`
- **Response**: Summary generation status and result

### 3. Get Patient Data (Enhanced)
- **Endpoint**: `GET /api/getPatientData/:regNo`
- **Enhancement**: Now includes `previous_case_notes_summary` in response

## AI Summary Generation

### OpenAI Integration
- Uses the same OpenAI configuration as existing summary features
- Supports both Azure OpenAI and standard OpenAI endpoints
- Configurable via environment variables

### Prompt Engineering
The AI prompt is designed to generate:
- Key medical history and patterns
- Recurring symptoms or conditions
- Treatment progression and outcomes
- Important diagnostic findings
- Medication history and responses
- Significant changes over time

### Error Handling
- Graceful degradation: If AI summary fails, patient data storage continues
- Error logging for debugging
- Fallback to empty summary if generation fails

## Configuration

### Environment Variables
The feature uses existing OpenAI configuration:
- `OPENAI_API_KEY` or `AZURE_OPENAI_API_KEY`
- `OPENAI_API_BASE` or `AZURE_OPENAI_ENDPOINT_URL`
- `AZURE_OPENAI_DEPLOYMENT_NAME` (for Azure)

### Case Notes Processing
- Filters out empty or invalid case notes
- Removes HTML tags for cleaner AI processing
- Organizes notes by encounter date
- Handles both string and object case notes formats

## Testing

### Test Script
Run the test script to verify functionality:
```bash
node test-case-notes-summary.js
```

### Mock Data
Enhanced mock data in `/mock/patientResponse.json` includes:
- Realistic medical case notes
- Multiple encounters with different dates
- Proper case note structure for testing

## Benefits

1. **Automated Workflow**: No manual intervention required for summary generation
2. **Comprehensive Summaries**: AI-generated summaries provide structured medical insights
3. **Historical Context**: Maintains continuity of care across encounters
4. **Scalable**: Handles multiple encounters and large case note volumes
5. **Configurable**: Uses existing OpenAI infrastructure

## Error Scenarios

### Handled Cases
- No encounters found for patient
- Empty or invalid case notes
- OpenAI API failures
- Database connection issues
- Missing environment variables

### Logging
All operations are logged with appropriate prefixes:
- `[AI]` for AI-related operations
- `[API]` for API endpoint operations
- Error details for debugging

## Future Enhancements

1. **Summary Versioning**: Track changes to summaries over time
2. **Custom Prompts**: Allow customization of AI prompts per department
3. **Summary Analytics**: Track summary generation metrics
4. **Batch Processing**: Process multiple patients simultaneously
5. **Summary Validation**: Medical professional review workflow
