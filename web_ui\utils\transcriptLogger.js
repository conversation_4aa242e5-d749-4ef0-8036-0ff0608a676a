class TranscriptLogger {
    constructor() {
        this.sessions = new Map(); // sessionId -> session data
        this.pendingMerge = new Map(); // sessionId -> {text, timestamp, deviceId, speakerRole}
        this.mergeWindow = 5000; // 5 second window for merging
    }

    // Helper to remove duplicated phrases and merge text properly
    cleanAndMergeText(existingText, newText) {
        if (!existingText) return newText;
        if (!newText) return existingText;

        // Clean up periods and extra spaces
        const cleanText = (text) => {
            return text.replace(/\s*\.+\s*/g, '.')
                      .replace(/\s+/g, ' ')
                      .trim();
        };

        const text1 = cleanText(existingText);
        const text2 = cleanText(newText);

        // If one text contains the other, return the longer one
        if (text1.includes(text2)) return text1;
        if (text2.includes(text1)) return text2;

        // Split into sentences, keeping the periods
        const splitSentences = (text) => {
            return text.split(/(?<=\.)/).map(s => s.trim()).filter(Boolean);
        };

        const sentences1 = splitSentences(text1);
        const sentences2 = splitSentences(text2);

        // Combine sentences, removing duplicates and near-duplicates
        const uniqueSentences = [];
        const allSentences = [...sentences1, ...sentences2];

        for (const sentence of allSentences) {
            const isDuplicate = uniqueSentences.some(existing => {
                // Check for exact match or if one contains the other
                return existing.includes(sentence) || sentence.includes(existing);
            });
            if (!isDuplicate) {
                uniqueSentences.push(sentence);
            }
        }

        return uniqueSentences.join(' ');
    }

    logTranscript({
        sessionId,
        encounterId,
        rawText,
        formattedText,
        speakerRole,
        deviceId,
        language,
        timestamp = new Date().toISOString()
    }) {
        // Initialize session if it doesn't exist
        if (!this.sessions.has(sessionId)) {
            this.sessions.set(sessionId, {
                encounterId,
                language,
                rawText: [],
                formattedText: [],
                roleDevices: new Map() // Track latest device for each role
            });
        }

        const session = this.sessions.get(sessionId);

        // Update raw text array without duplicates
        if (rawText && !session.rawText.includes(rawText)) {
            session.rawText.push(rawText);
        }

        if (formattedText) {
            const currentTime = Date.now();

            // Update or get the primary device for this role
            if (!session.roleDevices.has(speakerRole)) {
                session.roleDevices.set(speakerRole, deviceId);
            }
            const primaryDevice = session.roleDevices.get(speakerRole);

            // Only process text from the primary device for each role
            if (deviceId === primaryDevice) {
                // Try to find the last formatted text entry from the same speaker
                let lastEntry = session.formattedText.length > 0 ? 
                    session.formattedText[session.formattedText.length - 1] : null;

                // If there's a recent entry from the same speaker, update it
                if (lastEntry && 
                    lastEntry.speakerRole === speakerRole && 
                    currentTime - new Date(lastEntry.timestamp).getTime() < this.mergeWindow) {
                    lastEntry.text = formattedText;
                    lastEntry.timestamp = timestamp;
                } else {
                    // Add new entry
                    session.formattedText.push({
                        text: formattedText,
                        timestamp,
                        speakerRole,
                        deviceId
                    });
                }
            }
        }
    }

    // Download transcripts for a session
    downloadTranscripts(sessionId) {
        if (!this.sessions.has(sessionId)) {
            console.warn(`No transcripts found for session ${sessionId}`);
            return;
        }

        const session = this.sessions.get(sessionId);
        const jsonContent = JSON.stringify({
            [sessionId]: session
        }, null, 2);
        const blob = new Blob([jsonContent], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `transcript_${sessionId}_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // Download transcripts in CSV format
    downloadTranscriptsAsCSV(sessionId) {
        if (!this.sessions.has(sessionId)) {
            console.warn(`No transcripts found for session ${sessionId}`);
            return;
        }

        const session = this.sessions.get(sessionId);
        const headers = ['timestamp', 'speakerRole', 'text', 'deviceId'];
        const csvRows = [
            headers.join(','),
            ...session.formattedText.map(entry => {
                return headers.map(header => {
                    const value = entry[header] || '';
                    // Escape commas and quotes in the content
                    return `"${value.toString().replace(/"/g, '""')}"`;
                }).join(',');
            })
        ];

        const csvContent = csvRows.join('\n');
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `transcript_${sessionId}_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // Get session data
    getSessionData(sessionId) {
        if (!this.sessions.has(sessionId)) {
            return null;
        }

        // If there's a pending merge, save it before returning
        const pending = this.pendingMerge.get(sessionId);
        if (pending) {
            const session = this.sessions.get(sessionId);
            session.formattedText.push(pending);
            this.pendingMerge.delete(sessionId);
        }

        return {
            [sessionId]: this.sessions.get(sessionId)
        };
    }
}

export const transcriptLogger = new TranscriptLogger();
