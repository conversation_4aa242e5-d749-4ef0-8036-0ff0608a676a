#!/bin/bash
# Build MMR-Agent.exe and create installer on macOS (cross-compilation)
# Requires: wine, pyinstaller, innoextract, osslsigncode (optional for signing)
# Place arca-logo-sec.ico and arca-logo.ico in the same folder (convert PNG to ICO first)
# Place ARCA Privacy Policy.pdf in the same folder

set -e

# 1. Build Windows EXE with PyInstaller (using Wine)
wine ~/.wine/drive_c/users/apple/AppData/Local/Programs/Python/Python310/Scripts/pyinstaller.exe --onefile --noconsole \
  --name "MMR-Agent" \
  --icon "arca-logo-sec.ico" \
  --version-file "version_info.txt" \
  audio_agent.py

# 2. Copy config and privacy policy to dist
cp audio_agent_config.ini dist/
cp "ARCA Privacy Policy.pdf" dist/

# 3. (Optional) Sign the EXE (requires code signing cert and osslsigncode)
# osslsigncode sign -pkcs12 mycert.p12 -pass pass:YOURPASS -n "MMR-Agent" -i https://www.arcaai.com -in dist/MMR-Agent.exe -out dist/MMR-Agent-signed.exe

# 4. (Optional) Build installer with Inno Setup (using Wine)
# wine ~/.wine/drive_c/Program\ Files\ \(x86\)/Inno\ Setup\ 6/ISCC.exe MMR-Agent-Installer.iss

# 5. (Optional) Extract installer for inspection
# innoextract MMR-Agent-Installer.exe

echo "Build complete. Distributables are in dist/"
