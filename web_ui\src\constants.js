// src/constants.js
// Central place for API base URLs and configuration

// --- Dynamic API Base URL for Test Environment ---
let computedApiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:4000';
if (typeof window !== 'undefined') {
  const hostname = window.location.hostname;
  // Add all test/staging domains here
  if (
    hostname === 'test.arca.com' ||
    hostname.startsWith('test-') ||
    hostname === 'arcaai-staging.bcmch.org'
  ) {
    computedApiBaseUrl = 'https://arcaai-staging.bcmch.org'; // Set your test API base URL here
  }
}
console.log('[Config] API_BASE_URL set to:', computedApiBaseUrl);
export const API_BASE_URL = computedApiBaseUrl;

// Patient Data Sync Configuration
export const USE_MOCK_DATA = import.meta.env.VITE_USE_MOCK_DATA === 'true';
console.log('[Config] USE_MOCK_DATA:', USE_MOCK_DATA);

export const EXTERNAL_API_URL = USE_MOCK_DATA
  ? '/mock/patientResponse.json'
  : (import.meta.env.VITE_EXTERNAL_API || 'https://mi-preproduction.bcmch.org:4432');
console.log('[Config] EXTERNAL_API_URL set to:', EXTERNAL_API_URL);

export  const VITE_EMR_BEARER_TOKEN = import.meta.env.VITE_EMR_BEARER_TOKEN;
