import React, { useState } from 'react';
import './FeedbackInput.css';

function FeedbackInput() {
  const [feedback, setFeedback] = useState('');
  const [rating, setRating] = useState(0);
  
  return (
    <div className="feedback-input-container">
      {/* Textarea positioned at the top */}
      <div className="feedback-textarea-container">
        <textarea 
          className="feedback-textarea"
          placeholder="Share your feedback"
          value={feedback}
          onChange={e => setFeedback(e.target.value)}
        />
      </div>
      
      {/* Rating and button row with space-between */}
      <div className="rating-button-row">
        <div className="star-rating">
          {[1,2,3,4,5].map(star => (
            <span
              key={star}
              className={star <= rating ? 'star filled' : 'star'}
              onClick={() => setRating(star)}
            >★</span>
          ))}
        </div>
        
        <button className="btn btn-submit">Submit</button>
      </div>
    </div>
  );
}

export default FeedbackInput;