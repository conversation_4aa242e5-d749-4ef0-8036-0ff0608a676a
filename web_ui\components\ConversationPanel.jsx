import React, { useEffect, useRef, useState, useCallback } from 'react';
import { transcriptLogger } from '../utils/transcriptLogger';
import './ConversationPanel.css';

const COLORS = {
  Doctor: '#1a9c5e',
  Patient: '#1a355e',
  Nurse: '#b07d1a',
  Other: '#888',
  Unknown: '#aaa',
};

function getAvatarBg(role) {
  switch (role) {
    case 'Doctor': return '#eaf7e9';
    case 'Patient': return '#e5eaf7';
    case 'Nurse': return '#f7f3e9';
    default: return '#f0f0f0';
  }
}

function DownloadButtons({ sessionId, recording, audioChunks, onSaveAudio, onShowDownloadModal }) {
  return (
    <div style={{ position: 'relative' }}>
      <button
        className="btn"
        onClick={() => {
          if (!recording) {
            transcriptLogger.downloadTranscripts(sessionId);
          }
        }}
        style={{
          width: '32px',
          height: '32px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: 0,
          borderRadius: '50%',
          border: 'none',
          background: '#f8f9fa',
          cursor: recording ? 'not-allowed' : 'pointer',
          fontSize: '18px',
          color: '#2c3e50',
          transition: 'all 0.2s ease',
          opacity: recording ? 0.5 : 1,
        }}
        title="Download Transcript"
        disabled={recording}
        onMouseEnter={(e) => {
          if (!recording) {
            e.target.style.background = '#e9ecef';
          }
        }}
        onMouseLeave={(e) => {
          e.target.style.background = '#f8f9fa';
        }}
      >
        📥
      </button>
    </div>
  );
}

function ConversationPanel({ mergedTranscript, sessionId }) {
  // Defensive: ensure mergedTranscript is always an array
  const transcriptBlocks = Array.isArray(mergedTranscript) ? mergedTranscript : [];
  
  // Refs and state for auto-scroll functionality
  const scrollContainerRef = useRef(null);
  const isUserScrollingRef = useRef(false);
  const scrollTimeoutRef = useRef(null);
  const lastTranscriptLengthRef = useRef(0);
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true);

  // Check if user is near bottom of scroll container
  const isNearBottom = useCallback(() => {
    const container = scrollContainerRef.current;
    if (!container) return true;
    
    const { scrollTop, scrollHeight, clientHeight } = container;
    const threshold = 100; // pixels from bottom
    return scrollHeight - scrollTop - clientHeight < threshold;
  }, []);

  // Smooth scroll to bottom
  const scrollToBottom = useCallback(() => {
    const container = scrollContainerRef.current;
    if (!container) return;
    
    container.scrollTo({
      top: container.scrollHeight,
      behavior: 'smooth'
    });
  }, []);

  // Handle scroll events to detect user manual scrolling
  const handleScroll = useCallback(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    // Clear existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // Mark as user scrolling
    isUserScrollingRef.current = true;
    
    // Update auto-scroll preference based on scroll position
    const nearBottom = isNearBottom();
    setShouldAutoScroll(nearBottom);

    // Reset user scrolling flag after a delay
    scrollTimeoutRef.current = setTimeout(() => {
      isUserScrollingRef.current = false;
    }, 150);
  }, [isNearBottom]);

  // Effect to handle auto-scrolling when transcript updates
  useEffect(() => {
    const currentLength = transcriptBlocks.length;
    const hasNewContent = currentLength > lastTranscriptLengthRef.current;
    
    if (hasNewContent) {
      // Only auto-scroll if user hasn't manually scrolled away from bottom
      // or if this is the first message
      if (shouldAutoScroll || lastTranscriptLengthRef.current === 0) {
        // Small delay to ensure DOM is updated
        const timeoutId = setTimeout(() => {
          if (!isUserScrollingRef.current) {
            scrollToBottom();
          }
        }, 50);
        
        return () => clearTimeout(timeoutId);
      }
      
      lastTranscriptLengthRef.current = currentLength;
    }
  }, [transcriptBlocks.length, shouldAutoScroll, scrollToBottom]);

  // Initial scroll to bottom when component mounts with existing content
  useEffect(() => {
    if (transcriptBlocks.length > 0 && lastTranscriptLengthRef.current === 0) {
      setTimeout(() => {
        scrollToBottom();
        lastTranscriptLengthRef.current = transcriptBlocks.length;
      }, 100);
    }
  }, [transcriptBlocks.length, scrollToBottom]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  // Alternate left/right for each role, always Doctor left, Patient right, others alternate
  const roleOrder = [];
  transcriptBlocks.forEach(block => {
    if (block.role && !roleOrder.includes(block.role)) roleOrder.push(block.role);
  });

  return (
    <div style={{ display: 'flex', flexDirection: 'column', height: '100%', position: 'relative' }}>
      <div 
        ref={scrollContainerRef}
        onScroll={handleScroll}
        style={{ 
          flexGrow: 1, 
          overflowY: 'auto', 
          fontSize: '13px', 
          minHeight: 0,
          scrollBehavior: 'smooth'
        }}
      >
        {transcriptBlocks.length > 0 ? (
          <div style={{ marginBottom: '18px' }}>
            {transcriptBlocks.map((block, idx) => {
              // Determine orientation
              let align = 'left';
              if (block.role === 'Patient') align = 'right';
              else if (block.role !== 'Doctor' && block.role !== 'Patient') {
                // Alternate for other roles
                const pos = roleOrder.indexOf(block.role);
                align = pos % 2 === 0 ? 'left' : 'right';
              }
              
              // Avatar/initials
              const avatar = (
                <div style={{
                  width: 38, 
                  height: 38, 
                  borderRadius: '50%', 
                  background: getAvatarBg(block.role),
                  display: 'flex', 
                  flexDirection: 'column', 
                  alignItems: 'center', 
                  justifyContent: 'center',
                  fontWeight: 700, 
                  fontSize: 18, 
                  color: COLORS[block.role] || '#555', 
                  margin: align === 'left' ? '0 12px 0 0' : '0 0 0 12px',
                  boxShadow: '0 1px 4px #e3eaf2',
                  flexShrink: 0 // Prevent avatar from shrinking
                }}>
                  <span>{block.initials || '?'}</span>
                  <span style={{ 
                    fontSize: 10, 
                    fontWeight: 400, 
                    color: '#666', 
                    marginTop: 2 
                  }}>
                    {block.role}
                  </span>
                </div>
              );
              
              // Timestamp
              const timestamp = (
                <span style={{ 
                  fontWeight: 500, 
                  color: '#888', 
                  fontSize: '11px', 
                  position: 'absolute', 
                  top: 8, 
                  [align === 'left' ? 'right' : 'left']: 16 
                }}>
                  {block.timestamp}
                </span>
              );
              
              // Bubble
              const bubble = (
                <div style={{
                  background: align === 'left' ? '#f8fafc' : '#eaf7e9',
                  border: '1px solid #bcd',
                  borderRadius: 12,
                  padding: '12px 16px',
                  color: '#222',
                  fontSize: '15px',
                  lineHeight: 1.7,
                  fontFamily: 'inherit',
                  whiteSpace: 'pre-line',
                  maxWidth: 420,
                  minWidth: 120,
                  boxShadow: '0 1px 4px #e3eaf2',
                  marginLeft: align === 'left' ? 0 : 'auto',
                  marginRight: align === 'right' ? 0 : 'auto',
                  position: 'relative',
                  display: 'flex',
                  flexDirection: 'column',
                }}>
                  {timestamp}
                  <span style={{ marginTop: 18 }}>{block.text}</span>
                </div>
              );
              
              return (
                <div key={idx} style={{
                  display: 'flex', 
                  flexDirection: align === 'left' ? 'row' : 'row-reverse', 
                  alignItems: 'flex-end', 
                  marginBottom: 18,
                }}>
                  {avatar}
                  {bubble}
                </div>
              );
            })}
          </div>
        ) : (
          <div style={{ 
            padding: '20px', 
            textAlign: 'center', 
            color: '#666',
            fontStyle: 'italic'
          }}>
            No conversation yet
          </div>
        )}
      </div>
      
      {/* Optional: Add scroll-to-bottom button when user scrolls up */}
      {!shouldAutoScroll && transcriptBlocks.length > 0 && (
        <div style={{
          position: 'absolute',
          bottom: '25px',
          right: '10px',
          zIndex: 10
        }}>
          <button
            onClick={scrollToBottom}
            style={{
              backgroundColor: '#22396a',
              color: 'white',
              border: 'none',
              borderRadius: '50%',
              width: '40px',
              height: '40px',
              cursor: 'pointer',
              boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '18px',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.target.style.backgroundColor = '#1a2d52';
              e.target.style.transform = 'scale(1.1)';
            }}
            onMouseLeave={(e) => {
              e.target.style.backgroundColor = '#22396a';
              e.target.style.transform = 'scale(1)';
            }}
            title="Scroll to latest message"
          >
            ↓
          </button>
        </div>
      )}
    </div>
  );
}

// Export both components
ConversationPanel.DownloadButtons = DownloadButtons;
export default ConversationPanel;