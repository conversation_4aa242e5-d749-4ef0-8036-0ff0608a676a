# Security Hardening: Multi-mic Audio Agent

## Key Security Measures
- **CORS**: Only allow requests from the local web UI server (edit `allow_origins` in production)
- **Port Check**: Prevents accidental double-start and port conflicts
- **Graceful Shutdown**: Handles SIGINT/SIGTERM, ensures all threads/resources are cleaned up
- **No Sensitive Data**: No credentials, tokens, or PII stored or transmitted
- **Logging**: All access, errors, and state changes are logged for audit
- **No External Calls**: All processing and streaming is local to the customer environment
- **Dependency Management**: Only trusted, minimal dependencies (see requirements.txt)
- **Input Validation**: All API inputs are validated (Pydantic, JSO<PERSON> parsing)
- **No Arbitrary Code Execution**: No eval, exec, or shell commands from user input

## Deployment Notes
- Each agent runs on a local endpoint (not exposed to the internet)
- Web UI is hosted on a local server, CORS must be restricted to that server
- Use a firewall to restrict access to the agent port (default 5001) to only the web UI server
- Regularly update dependencies for security patches

---
