from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor
from opentelemetry.instrumentation.logging import LoggingInstrumentor
from opentelemetry.instrumentation.requests import RequestsInstrumentor
from opentelemetry.sdk.resources import SERVICE_NAME, Resource
from opentelemetry.sdk.trace import Tracer<PERSON>rovider
from opentelemetry.sdk.trace.export import BatchSpanProcessor, ConsoleSpanExporter
from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
from opentelemetry import trace
import logging

logger = logging.getLogger("audio_agent")

class OpenTelemetryManager:
    def __init__(self, app, enable_opentelemetry=False):
        self.app = app
        self.enable_opentelemetry = enable_opentelemetry
        if self.enable_opentelemetry:
            self.setup_opentelemetry()

    def setup_opentelemetry(self):
        resource = Resource(attributes={SERVICE_NAME: "audio-agent"})
        provider = TracerProvider(resource=resource)
        trace.set_tracer_provider(provider)
        # Export to console (for demo); replace with OTLPSpanExporter for production
        processor = BatchSpanProcessor(ConsoleSpanExporter())
        provider.add_span_processor(processor)
        # To export to an observability backend, uncomment below and configure endpoint
        # otlp_exporter = OTLPSpanExporter(endpoint="http://localhost:4318/v1/traces", insecure=True)
        # provider.add_span_processor(BatchSpanProcessor(otlp_exporter))
        FastAPIInstrumentor.instrument_app(self.app)
        LoggingInstrumentor().instrument()
        RequestsInstrumentor().instrument()
