# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['audio_agent.py'],
    pathex=[],
    binaries=[],
    datas=[('..\\audio_agent_config.ini', '.'), ('..\\dspsettings.json', '.'), ('..\\..\\ARCA Privacy Policy.pdf', '.')],
    hiddenimports=['sounddevice', 'soundfile', 'pedalboard', 'pedalboard._pedalboard', 'prometheus_client', 'opentelemetry', 'uvicorn', 'fastapi', 'passlib.handlers.bcrypt', 'passlib.handlers', 'passlib.context', 'jose', 'jose.jwt', 'jose.exceptions'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='Audio-Agent',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['..\\..\\arca-logo-sec.ico'],
)
