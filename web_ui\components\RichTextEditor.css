/* Rich Text Editor Styles */
.rich-text-editor {
  font-family: inherit;
}

/* Spinner animation */
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Content editable focus styles */
.rich-text-editor [contenteditable="true"]:focus {
  outline: none;
  border-color: #3b82f6 !important;
}

/* Placeholder styles */
.rich-text-editor .placeholder {
  color: #9ca3af;
  font-style: italic;
}

/* Button hover effects */
.rich-text-editor button:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rich-text-editor button:active:not(:disabled) {
  transform: translateY(0);
}

/* Smooth transitions */
.rich-text-editor * {
  transition: all 0.2s ease;
}

/* Text selection styles */
.rich-text-editor [contenteditable="true"]::selection {
  background-color: #3b82f6;
  color: white;
}

/* Formatting styles for content */
.rich-text-editor strong {
  font-weight: 600;
}

.rich-text-editor em {
  font-style: italic;
}

/* Loading state overlay */
.rich-text-editor .loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}
