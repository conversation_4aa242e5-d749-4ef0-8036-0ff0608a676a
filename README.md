# Medical Ambient Listening as a Service v3.0

## Observability
- Prometheus metrics endpoint: `/metrics` (and port 8001)
- OpenTelemetry tracing: toggle with `enable_opentelemetry` in config (see README)

## Service Management
- See README for Windows service install/start/stop/restart commands.

## Overview
This project provides a multi-microphone audio agent and a React web UI for live audio streaming and device management. All transcription and ML dependencies have been removed for simplicity and compatibility.

## Requirements
- Python 3.8+
- See `requirements.txt` for all dependencies.
- Key dependencies:
  - sounddevice
  - soundfile
  - fastapi
  - uvicorn
  - requests
  - pydantic
  - numpy
  - pedalboard (for robust, production-grade DSP)

### Components
- **audio_agent.py**: Python FastAPI server for multi-mic recording and live audio streaming via WebSocket.
- **web_ui/**: React web application for device selection, session control, and live audio playback.

## Features
- List and select available microphones.
- Start/stop multi-mic recording sessions.
- Live stream audio from all selected mics to the web UI.
- Web UI audio player with play, pause, and mute controls.

## DSP Processing (Production-Grade)
This project now uses [Spotify Pedalboard](https://github.com/spotify/pedalboard) for all real-time DSP (EQ, noise gate, compressor, limiter) in the backend. This eliminates manual FFT/irfft code and ensures robust, error-free audio processing.

- All DSP settings are adjustable live via the web UI and applied in real time.
- No more shape mismatch errors or manual numpy DSP logic.

## Setup

### Python Audio Agent
1. Install dependencies:
   ```sh
   pip install -r requirements.txt
   ```
2. Run the audio agent:
   ```sh
   python audio_agent.py
   ```
   The server will start on `http://localhost:5001`.

### React Web UI
1. Navigate to the `web_ui` directory:
   ```sh
   cd web_ui
   ```
2. Install dependencies:
   ```sh
   npm install
   ```
3. Start the development server:
   ```sh
   npm run dev
   ```
   The app will be available at `http://localhost:5173` (or as indicated in the terminal).

## Usage
- Open the web UI in your browser.
- Select microphones and start a session.
- Listen to live audio from all selected mics using the built-in player.

## Notes
- All transcription, Whisper, and ML dependencies have been removed.
- No audio is stored or transcribed; only live streaming is supported.
- For multi-mic support, ensure your system and browser allow access to all input devices.

## Observability & Monitoring

### Prometheus Metrics
- The agent exposes a Prometheus-compatible metrics endpoint at `http://<host>:8001/` (background server) and `/metrics` (FastAPI endpoint).
- Metrics include request count, active recordings, errors, and recording durations.

### OpenTelemetry Tracing
- To enable OpenTelemetry distributed tracing, set `enable_opentelemetry = true` in `audio_agent_config.ini` under `[server]`.
- By default, tracing is **disabled**.
- If enabled, the agent will check for all required OpenTelemetry dependencies at startup. If any are missing, the agent will log an error and exit.
- To export traces to your observability backend, configure the exporter in `audio_agent.py` (see `setup_opentelemetry()` function).

## Service Management (Windows)

To install, start, stop, or restart the MMR-Agent as a Windows service (after packaging as an EXE):

**Install the service (using NSSM):**
```sh
nssm install MMR-Agent "C:\Program Files\MMR-Agent\MMR-Agent.exe"
```

**Start the service:**
```sh
nssm start MMR-Agent
```

**Stop the service:**
```sh
nssm stop MMR-Agent
```

**Restart the service:**
```sh
nssm restart MMR-Agent
```

Or, if registered as a standard Windows service:

**Start:**
```sh
sc start MMR-Agent
```
**Stop:**
```sh
sc stop MMR-Agent
```
**Restart:**
```sh
sc stop MMR-Agent && sc start MMR-Agent
```

## Cleaning Up
- To remove all cached model data and legacy files, see the project cleanup commands in the commit history.

---
For any issues, please open an issue or contact the maintainer.
