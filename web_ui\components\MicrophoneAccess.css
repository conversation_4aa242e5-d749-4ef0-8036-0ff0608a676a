.microphone-access {
	background-color: white;
	border-radius: 8px;
	padding: 20px;
	margin-bottom: 20px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .permission-request {
	text-align: center;
	padding: 20px;
	border: 1px dashed #ddd;
	border-radius: 8px;
	background-color: #f9f9f9;
  }
  
  .permission-request h3 {
	margin-top: 0;
	color: #333;
	font-size: 1.2rem;
	margin-bottom: 10px;
  }
  
  .permission-request p {
	margin-bottom: 20px;
	color: #666;
  }
  
  .request-access-button {
	background-color: #1a9c5e;
	color: white;
	border: none;
	border-radius: 4px;
	padding: 10px 20px;
	font-weight: 600;
	cursor: pointer;
	transition: background-color 0.2s ease;
  }
  
  .request-access-button:hover {
	background-color: #158a4f;
  }
  
  .error-message {
	color: #e74c3c;
	margin-top: 10px;
	font-size: 0.9rem;
  }
  
  .no-devices {
	text-align: center;
	padding: 20px;
	border: 1px solid #f8d7da;
	border-radius: 8px;
	background-color: #fff5f5;
	color: #721c24;
  }
  
  .refresh-button {
	background-color: #3498db;
	color: white;
	border: none;
	border-radius: 4px;
	padding: 8px 16px;
	margin-top: 10px;
	font-weight: 600;
	cursor: pointer;
	transition: background-color 0.2s ease;
  }
  
  .refresh-button:hover {
	background-color: #2980b9;
  }
  
  .devices-detected {
	text-align: center;
	padding: 10px;
	border: 1px solid #d4edda;
	border-radius: 8px;
	background-color: #f8fff9;
	color: #155724;
  }
  