import numpy as np
import io
import soundfile as sf
from pedalboard._pedalboard import Pedalboard
from pedalboard import Compressor, Limiter, NoiseGate, HighpassFilter, LowpassFilter
import logging

logger = logging.getLogger("audio_agent")

class DSPProcessor:
    def __init__(self):
        pass

    def apply_dsp(self, frames, dsp, samplerate, chunk_frames):
        """Apply DSP (EQ, noise gate, compressor, limiter) to audio frames using Pedalboard."""
        try:
            if not dsp:
                return frames
            board = Pedalboard()
            eq = dsp.get('eq', {})
            low_cut = eq.get('lowCutFreq', 0)
            if low_cut > 0:
                board.append(HighpassFilter(cutoff_frequency_hz=low_cut))
            high_cut = eq.get('highCutFreq', 0)
            if high_cut > 0:
                board.append(LowpassFilter(cutoff_frequency_hz=high_cut))
            ng = dsp.get('noiseGate', {})
            ng_thresh = ng.get('threshold', 0)
            if ng_thresh > 0:
                board.append(NoiseGate(threshold_db=20 * np.log10(max(ng_thresh, 1e-8))))
            comp = dsp.get('compressor', {})
            comp_thresh = comp.get('threshold', 1)
            comp_ratio = comp.get('ratio', 1)
            if comp_thresh < 1 and comp_ratio > 1:
                board.append(Compressor(threshold_db=20 * np.log10(max(comp_thresh, 1e-8)), ratio=comp_ratio))
            lim = dsp.get('limiter', {})
            lim_thresh = lim.get('threshold', 1)
            if lim_thresh < 1:
                board.append(Limiter(threshold_db=20 * np.log10(max(lim_thresh, 1e-8))))
            frames = np.asarray(frames, dtype=np.float32).reshape(-1)
            frames = frames[np.newaxis, :]
            processed = board(frames, samplerate)
            frames = processed.flatten().astype(np.float32)
            if len(frames) != chunk_frames:
                logger.error("Final DSP output frame size mismatch. Resizing.")
                frames = np.resize(frames, chunk_frames)
        except Exception as e:
            logger.error(f"DSP Error: {e}")
            frames = np.resize(frames, chunk_frames)
        return frames