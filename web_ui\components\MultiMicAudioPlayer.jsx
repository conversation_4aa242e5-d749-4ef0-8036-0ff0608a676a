import React, { useState, useEffect, useRef, useCallback } from 'react';
import MuteControl from './MuteControl.jsx';
import authService from '../services/authService.js';

function MultiMicAudioPlayer({ selectedMics, recording, paused, setAudioChunks }) {
  const [mutedMics, setMutedMics] = useState({});
  const mutedMicsRef = useRef(mutedMics);
  useEffect(() => { mutedMicsRef.current = mutedMics; }, [mutedMics]);
  const audioCtxRef = useRef(null);
  const chunkQueues = useRef({});
  const playingRef = useRef({});
  const gainNodesRef = useRef({});

  const handleToggleMute = useCallback((deviceId) => {
    setMutedMics(prev => {
      const newState = { ...prev, [deviceId]: !prev[deviceId] };
      // Get or create gain node and update its value
      if (gainNodesRef.current[deviceId]) {
        gainNodesRef.current[deviceId].gain.value = newState[deviceId] ? 0 : 1;
      }
      return newState;
    });
  }, []);

  // ✅ FIXED: Initialize muted state BEFORE WebSocket connection
  useEffect(() => {
    if (!recording) {
      // Stop playback and clear queues when recording stops
      chunkQueues.current = {};
      playingRef.current = {};
      if (audioCtxRef.current) {
        audioCtxRef.current.suspend();
      }
      return;
    }

    // ✅ Initialize muted state FIRST - before WebSocket connection
    if (selectedMics && selectedMics.length > 0) {
      const newMuted = {};
      selectedMics.forEach(mic => {
        const id = mic.deviceId || mic.device;
        if (id !== undefined && id !== null && id !== "") {
          newMuted[id] = true; // Start muted for safety
        }
      });
      setMutedMics(newMuted);
    }

    if (!audioCtxRef.current) {
      audioCtxRef.current = new (window.AudioContext || window.webkitAudioContext)();
    }

    // ✅ Pre-initialize gain nodes with muted state
    selectedMics.forEach(mic => {
      const micId = mic.deviceId || mic.device;
      if (!gainNodesRef.current[micId]) {
        const gainNode = audioCtxRef.current.createGain();
        gainNode.gain.value = 0; // Start muted
        gainNode.connect(audioCtxRef.current.destination);
        gainNodesRef.current[micId] = gainNode;
      }
    });

    // Enable echo cancellation
    getUserMediaWithEchoCancellation();

    // Now start WebSocket connection with authentication
    const initWebSocket = async () => {
      // Ensure we have a valid token
      if (!authService.isAuthenticated()) {
        await authService.login();
      }
      const token = authService.getToken();
      let ws = new window.WebSocket(`ws://localhost:5001/audio_stream?token=${encodeURIComponent(token)}`);
      ws.binaryType = 'arraybuffer';
      ws.onopen = () => console.log('WebSocket opened');
      ws.onerror = (e) => console.error('WebSocket error', e);
      ws.onclose = () => console.log('WebSocket closed');
      return ws;
    };

    initWebSocket().then(ws => {
      ws.onmessage = (event) => {
        try {
          const data = new DataView(event.data);
          const micId = data.getUint8(0);
          const samplerate = data.getUint32(1, false);
          const wavLen = data.getUint32(5, false);
          const wavBytes = event.data.slice(9, 9 + wavLen);
          // ROUTEMAP log
          console.log(`[ROUTEMAP] Step 4: Audio chunk received for mic ${micId} (samplerate=${samplerate}, wavLen=${wavLen})`);
          if (!chunkQueues.current[micId]) chunkQueues.current[micId] = [];
          chunkQueues.current[micId].push(wavBytes);

          // Always include deviceId in audioChunks for diarization
          setAudioChunks((prevChunks) => [...prevChunks, { deviceId: micId, chunk: wavBytes }]);

          playNextChunk(micId);
        } catch (e) {
          console.error('WebSocket audio chunk error:', e);
        }
      };
    }).catch(error => {
      console.error('Failed to initialize WebSocket:', error);
    });

    return () => {
      // Note: ws is now inside the promise, so we need to handle cleanup differently
      if (audioCtxRef.current) {
        audioCtxRef.current.close();
        audioCtxRef.current = null;
      }
      chunkQueues.current = {};
      playingRef.current = {};
      gainNodesRef.current = {};
    };
  }, [JSON.stringify(selectedMics), recording]);

  // ✅ Ensure gain nodes reflect the current mutedMics state
  useEffect(() => {
    if (!audioCtxRef.current) return;
    Object.keys(mutedMics).forEach(micId => {
      let gainNode = gainNodesRef.current[micId];
      if (gainNode) {
        gainNode.gain.value = mutedMics[micId] ? 0 : 1;
      }
    });
  }, [mutedMics]);

  async function getUserMediaWithEchoCancellation() {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        },
        video: false
      });
      console.log('Microphone stream with echo cancellation enabled:', stream);
    } catch (err) {
      console.error('Error accessing microphone with echo cancellation:', err);
    }
  }

  // ✅ FIXED: Improved initGainNode with better fallback handling
  function initGainNode(micId) {
    const ctx = audioCtxRef.current;
    if (!ctx) return null;

    if (!gainNodesRef.current[micId]) {
      const gainNode = ctx.createGain();
      gainNode.gain.value = 0; // Always start muted for safety
      gainNode.connect(ctx.destination);
      gainNodesRef.current[micId] = gainNode;
    }

    const gainNode = gainNodesRef.current[micId];
    // ✅ Use explicit boolean check with fallback to muted (safe default)
    const isMuted = mutedMics[micId] !== undefined ? mutedMics[micId] : true;
    gainNode.gain.value = isMuted ? 0 : 1;

    return gainNode;
  }

  // ✅ FIXED: Added safety checks in playNextChunk
  function playNextChunk(micId) {
    const ctx = audioCtxRef.current;
    if (!ctx) return;
    if (!chunkQueues.current[micId] || chunkQueues.current[micId].length === 0) return;
    if (playingRef.current[micId]) return; // Already playing

    const gainNode = initGainNode(micId);
    if (!gainNode) return; // ✅ Safety check

    const wavBytes = chunkQueues.current[micId].shift();
    playingRef.current[micId] = true;

    ctx.decodeAudioData(wavBytes.slice(0), (audioBuffer) => {
      console.log(`[mic ${micId}] Decoded chunk, duration: ${audioBuffer.duration}s, sampleRate: ${audioBuffer.sampleRate}`);
      const src = ctx.createBufferSource();
      src.buffer = audioBuffer;

      // ✅ Double-check mute state right before playback using ref
      const isMuted = mutedMicsRef.current[micId] !== undefined ? mutedMicsRef.current[micId] : true;
      gainNode.gain.value = isMuted ? 0 : 1;

      // Connect source -> gain node -> destination
      src.connect(gainNode);

      src.start();
      src.onended = () => {
        src.disconnect();
        playingRef.current[micId] = false;
        setTimeout(() => playNextChunk(micId), 0);
      };
    }, (err) => {
      console.error(`[mic ${micId}] decodeAudioData error:`, err);
      playingRef.current[micId] = false;
    });
  }

  return (
    <div className="multi-mic-audio-player">
      {recording && selectedMics && selectedMics.map(mic => (
        <div key={mic.deviceId || mic.device} className="audio-track">
          <div className="audio-track-info">
            <div className="audio-track-label">
              Mic {mic.device} ({mic.role || mic.device})
            </div>
            <div className="audio-track-status">
              <div className="live-indicator">
                <span className="live-dot"></span>
                Live Streaming..
              </div>
              <MuteControl
                deviceId={mic.deviceId || mic.device}
                isMuted={mutedMics[mic.deviceId || mic.device]}
                onToggleMute={handleToggleMute}
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

export default MultiMicAudioPlayer;
